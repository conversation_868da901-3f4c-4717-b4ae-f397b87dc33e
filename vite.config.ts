import path from 'path'
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'
import { TanStackRouterVite } from '@tanstack/router-plugin/vite'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react(),TanStackRouterVite()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),

      // fix loading all icon chunks in dev mode
      // https://github.com/tabler/tabler-icons/issues/1233
      '@tabler/icons-react': '@tabler/icons-react/dist/esm/icons/index.mjs',
    },
  },
  esbuild: {
    jsxInject: "", // If you're using JSX, it's helpful, but not necessary for unused variable warnings
    logLevel: 'silent', // Mute logs to suppress the warning
  },
  server: {
    proxy: {
      '/api': {
        target: 'http://20.233.67.251:3000',
        changeOrigin: true,
      },
    },
  },
})
