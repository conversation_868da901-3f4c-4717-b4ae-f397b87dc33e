import React from 'react'
import { useFormContext, useWatch } from 'react-hook-form'
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { ApplicationFormValues } from '../../types/application-form-types'

interface Step5FormProps {
  localValues: {
    basicSalary: string
    transportationAllowance: string
    accommodationAllowance: string
    otherAllowance: string
    workingDays: string
    calendarDays: string
  }
  setLocalValues: React.Dispatch<
    React.SetStateAction<{
      basicSalary: string
      transportationAllowance: string
      accommodationAllowance: string
      otherAllowance: string
      workingDays: string
      calendarDays: string
    }>
  >
}

const Step5VisaApplication: React.FC<Step5FormProps> = ({
  localValues,
  setLocalValues,
}) => {
  const form = useFormContext<ApplicationFormValues>()
  const visaType = useWatch({ control: form.control, name: 'visaType' })

  // Log all form values for debugging
  console.log('Step 5 Form Values:', form.watch())

  return (
    <>
      <form className='space-y-4 fz-form'>
        {visaType === 'Employment Visa Renewal' && (
          <>
            <div className='mb-4'>
              <FormField
                control={form.control}
                name='salaryChange'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Do you want to change the salary details of the Visa
                      holder ?
                    </FormLabel>
                    <FormControl>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder='Choose' />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value='Yes'>Yes</SelectItem>
                          <SelectItem value='No'>No</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </>
        )}
        {/* Basic Salary (AED) */}
        <div className='mb-4'>
          <FormField
            control={form.control}
            name='basicSalary'
            render={({ field }) => (
              <FormItem data-error-field='basicSalary'>
                <FormLabel>
                  Basic Salary (AED) <span className='text-red-500'>*</span>
                </FormLabel>
                <FormControl>
                  <div className='flex items-center rounded-md'>
                    <Input
                      placeholder='Enter'
                      type='number'
                      value={localValues.basicSalary}
                      className='pr-20'
                      onChange={(e) => {
                        const value = e.target.value

                        setLocalValues((prev) => ({
                          ...prev,
                          basicSalary: value,
                        }))

                        if (value === '') {
                          field.onChange(undefined)
                          form.setError('basicSalary', {
                            type: 'manual',
                            message: 'This field is required',
                          })
                        } else if (/^\d+$/.test(value)) {
                          const numberValue = Number(value)
                          field.onChange(numberValue)
                          form.clearErrors('basicSalary')
                        } else {
                          field.onChange(undefined)
                        }
                      }}
                      onKeyDown={(e) => {
                        const invalidChars = ['e', 'E', '+', '-', '.']
                        if (invalidChars.includes(e.key)) {
                          e.preventDefault()
                        }
                      }}
                      min={10}
                      max={9999999}
                    />
                    <span className='px-4 absolute right-[70px] bg-primary dark:bg-primary-dark text-white rounded-[12px]'>
                      AED
                    </span>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Transportation Allowance (AED) */}
        <div className='mb-4'>
          <FormField
            control={form.control}
            name='transportationAllowance'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Transportation Allowance (AED)</FormLabel>
                <FormControl>
                  <div className='flex items-center rounded-md'>
                    <Input
                      placeholder='Enter'
                      type='number'
                      value={localValues.transportationAllowance}
                      className='pr-20'
                      onChange={(e) => {
                        const value = e.target.value

                        setLocalValues((prev) => ({
                          ...prev,
                          transportationAllowance: value,
                        }))

                        if (value === '') {
                          field.onChange(undefined)
                        } else if (/^\d+$/.test(value)) {
                          const num = Number(value)
                          field.onChange(num)
                        } else {
                          field.onChange(undefined)
                        }
                      }}
                      onKeyDown={(e) => {
                        const invalidChars = ['e', 'E', '+', '-', '.']
                        if (invalidChars.includes(e.key)) {
                          e.preventDefault()
                        }
                      }}
                      min={0}
                    />
                    <span className='px-4 absolute right-[70px] bg-primary dark:bg-primary-dark text-white rounded-[12px]'>
                      AED
                    </span>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        {/* Accommodation Allowance (AED) */}
        <div className='mb-4'>
          <FormField
            control={form.control}
            name='accommodationAllowance'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Accommodation Allowance (AED)</FormLabel>
                <FormControl>
                  <div className='flex items-center rounded-md'>
                    <Input
                      placeholder='Enter'
                      type='number'
                      value={localValues.accommodationAllowance}
                      className='pr-20'
                      onChange={(e) => {
                        const value = e.target.value

                        setLocalValues((prev) => ({
                          ...prev,
                          accommodationAllowance: value,
                        }))

                        if (value === '') {
                          field.onChange(undefined)
                        } else if (/^\d+$/.test(value)) {
                          const num = Number(value)
                          field.onChange(num)
                        } else {
                          field.onChange(undefined)
                        }
                      }}
                      onKeyDown={(e) => {
                        const invalidChars = ['e', 'E', '+', '-', '.']
                        if (invalidChars.includes(e.key)) {
                          e.preventDefault()
                        }
                      }}
                      min={0}
                    />
                    <span className='px-4 absolute right-[70px] bg-primary dark:bg-primary-dark text-white rounded-[12px]'>
                      AED
                    </span>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Other Allowance (AED) */}
        <div className='mb-4'>
          <FormField
            control={form.control}
            name='otherAllowance'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Other Allowance (AED)</FormLabel>
                <FormControl>
                  <div className='flex items-center rounded-md'>
                    <Input
                      placeholder='Enter'
                      type='number'
                      value={localValues.otherAllowance}
                      className='pr-20'
                      onChange={(e) => {
                        const value = e.target.value

                        setLocalValues((prev) => ({
                          ...prev,
                          otherAllowance: value,
                        }))

                        if (value === '') {
                          field.onChange(undefined)
                        } else if (/^\d+$/.test(value)) {
                          const num = Number(value)
                          field.onChange(num)
                        } else {
                          field.onChange(undefined)
                        }
                      }}
                      onKeyDown={(e) => {
                        const invalidChars = ['e', 'E', '+', '-', '.']
                        if (invalidChars.includes(e.key)) {
                          e.preventDefault()
                        }
                      }}
                      min={0}
                    />
                    <span className='px-4 absolute right-[70px] bg-primary dark:bg-primary-dark text-white rounded-[12px]'>
                      AED
                    </span>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        {/* Total Monthly Salary (AED) */}
        <div className='mb-4'>
          <FormField
            control={form.control}
            name='totalMonthlySalary'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Total Monthly Salary (AED)</FormLabel>
                <FormControl>
                  <div className='flex items-center rounded-md'>
                    <Input {...field} disabled className='pr-20' />
                    <span className='px-4 absolute right-[70px] bg-primary dark:bg-primary-dark text-white rounded-[12px]'>
                      AED
                    </span>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </form>
    </>
  )
}

export default Step5VisaApplication
