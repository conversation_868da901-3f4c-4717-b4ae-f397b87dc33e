import { useState } from 'react'
import { Link } from '@tanstack/react-router'
import data from './values.json' // adjust path if needed
import { Input } from '@/components/ui/input'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { ThemeSwitch } from '@/components/theme-switch'
import { RadioGroup} from '@/components/ui/radio-group'
import { RadioOption } from '@/features/track-application/components/radio-option'


export default function TrackApplication() {
  const [appNumber, setAppNumber] = useState('')
  const [isValid, setIsValid] = useState(false)
  const [error, setError] = useState(false)

  const validateAppNumber = () => {
    const found = data.applications.find(
      (app) => app.applicationDetails.applicationNumber === appNumber.trim()
    )
    if (found) {
      setIsValid(true)
      setError(false)
    } else {
      setIsValid(false)
      setError(true)
    }
  }

  return (
    <>
      <Header>
        <div className='ml-auto flex items-center space-x-4'>
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className="mb-2 flex items-center justify-between">
          <div className='mb-2 flex items-center '>
            <h1 className='text-2xl font-bold tracking-tight lg:text-[28px]'>
              Track Your Application
            </h1>
          </div>

          {!isValid ? (
            <Button onClick={validateAppNumber}>Next</Button>
          ) : (
            <Link to="/track-application" className="inline-flex items-center justify-center">
              <Button>
                <Link to="/track-application/tracker_status">Next</Link>
              </Button>
            </Link>
          )}
        </div>

        <Card className="mt-6 w-full">
          <div className="flex justify-between items-start px-6 pt-6">
            <h2 className="text-base font-semibold text-slate-900">
              What do you want to track? <span className="text-red-500">*</span>
            </h2>
          </div>

          {/* radio option is a component */}
          <CardContent className="px-6 pb-4 pt-4">
            <RadioGroup defaultValue="license" className="flex space-x-8 mb-6">
              <RadioOption value="license" label="License Application" />
              <RadioOption value="visa" label="Visa Application" />
              <RadioOption value="renewal" label="Renewal Application" />
            </RadioGroup>

            <div className="mb-6">
              <label htmlFor="applicationNumber" className="text-sm font-medium leading-none mb-3 block">
                Please enter your Application Number <span className="text-red-500">*</span>
              </label>
              <Input
                id="applicationNumber"
                value={appNumber}
                onChange={(e) => setAppNumber(e.target.value)}
                placeholder="Enter application number"
              />
              {error && <p className="text-red-500 text-sm mt-2">Application number not found.</p>}
            </div>


          </CardContent>
        </Card>
      </Main>
    </>
  )
}
