import { BASE_URL } from '@/utils/network';
import axios from 'axios';
import { useAuthStore } from '@/stores/authStore';

// Helper to format Date or string to YYYY-MM-DD
function formatDate(input: Date | string): string {
  const date = typeof input === 'string' ? new Date(input) : input;
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

export interface VisaApplicationFormValues {
  appemail: string;
  ar_nationality: string;
  nationality: string;
  City: string;
  City_of_Birth: string;
  Company_Name_Initial: string;
  Country_of_Birth: string;
  Current_Visa_Status: string;
  Current_Visa_Valid_Date: Date | string;
  Customer_Mobile: string;
  Date_of_Birth: Date | string;
  Designation_Amendment: string;
  Duration_of_Employment: string;
  Educational_Qualification: string;
  Email: string;
  emp_entitlement: string;
  emp_leavecd: number;
  emp_leavewd: number;
  emp_leavetype: string;
  emp_probationperiod: string;
  emp_startdate: Date | string;
  en_companyname: string;
  Establishment_Card_Number: string;
  en_emptype: string;
  en_jobtitle: string;
  en_name_first: string;
  en_name_last: string;
  en_name_middle: string;
  en_nationality: string;
  en_ticket: string;
  E_Visa_Application_Type: string;
  Existing_Emirates_ID_Expiry_Date: Date | string;
  Existing_Emirates_ID_Number: string;
  Father_s_Full_Name: string;
  Form: string;
  From_Portal: boolean;
  Gender: string;
  General_Manager_Email_Address: string;
  General_Manager_Name: string;
  Home_Address_Line_1: string;
  Home_City: string;
  Home_Country: string;
  Home_Region: string;
  incountry: string;
  Marital_Status: string;
  Mother_s_Full_Name: string;
  Name: string;
  Passport_Country_of_Issue: string;
  Passport_Expiry_Date: Date | string;
  Passport_Issue_Date: Date | string;
  Passport_No: string;
  Passport_Place_of_Issue: string;
  paymentmethod: string;
  Portal_Submission_Date: Date | string;
  Previous_Emirates_ID: string;
  Previous_Nationality: string;
  Processing_Type: string;
  Religion: string;
  Religion_Sub_Category: string;
  Region: string;
  Salary_Amendment: string;
  attestedDegree: string;
  noAttestedDegree: string;
  Saluatation: string;
  Service_Type: string;
  slr_basic: number;
  Trade_License_Num_Initial: string;
  Type_of_Visa: string;
  UAE_Country: string;
  VFL_Applied: boolean;
}

export function mainpulateVisaApplicationData(data: any) {
  return {
    // Contact & Application Info
    appemail: data.appUpdatesEmail || '',
    Email: data.emailAddress || '',
    Customer_Mobile: data.phone || '',
    Portal_Submission_Date: data.applicationDate ? formatDate(data.applicationDate) : '',
    From_Portal: true,
    
    // Personal Info
    Saluatation: data.title || '',
    en_name_first: data.firstName || '',
    en_name_middle: data.middleName || '',
    en_name_last: data.lastName || '',
    arabicName: data.arabicName || '',
    firstNameArabic: data.firstNameArabic || '',
    middleNameArabic: data.middleNameArabic || '',
    lastNameArabic: data.lastNameArabic || '',
    Gender: data.gender || '',
    Marital_Status: data.maritalStatus || '',
    Religion: data.religion || '',
    Religion_Sub_Category: data.religionSubCategory || '',
    Date_of_Birth: data.dateOfBirth ? formatDate(data.dateOfBirth) : '',
    City_of_Birth: data.cityOfBirth || '',
    Country_of_Birth: data.countryOfBirth || '',
    Previous_Nationality: data.previousNationality || '',
    en_nationality: data.nationality || '',
    ar_nationality: data.nationality || '',
    Father_s_Full_Name: data.fatherFullName || '',
    Mother_s_Full_Name: data.motherFullName || '',
    
    // Passport Info
    Passport_No: data.passportNumber || '',
    Passport_Country_of_Issue: data.countryOfIssuance || '',
    Passport_Place_of_Issue: data.placeOfIssue || '',
    Passport_Issue_Date: data.passportIssueDate ? formatDate(data.passportIssueDate) : '',
    Passport_Expiry_Date: data.passportExpiryDate ? formatDate(data.passportExpiryDate) : '',
    
    // Address Info
    City: data.city || '',
    Region: data.province || '',
    Home_City: data.city || '',
    Home_Region: data.province || '',
    Home_Country: data.country1 || '',
    Home_Address_Line_1: data.streetAddress1 || '',
    addressLine2: data.addressLine2 || '',
    streetAddress: data.streetAddress || '',
    addressLine: data.addressLine || '',
    cityAddress: data.cityAddress || '',
    stateProvince: data.stateProvince || '',
    country: data.country || '',
    UAE_Country: data.country || '',
    
    // Company Info
    Company_Name_Initial: data.companyName || '',
    en_companyname: data.companyName || '',
    Trade_License_Num_Initial: data.tradeLicenseNumber || '',
    Establishment_Card_Number: data.establishmentCardNumber || '',
    General_Manager_Name: data.authorizedSignatory || '',
    General_Manager_Email_Address: data.emailAddressOfGeneralManager || '',
    
    // Visa Info
    Type_of_Visa: data.visaType || '',
    E_Visa_Application_Type: data.eVisaApplicationType || '',
    Current_Visa_Status: data.currentVisaStatus || '',
    Current_Visa_Valid_Date: data.visaValidUntil ? formatDate(data.visaValidUntil) : '',
    Previous_Emirates_ID: data.emiratesID || '',
    Existing_Emirates_ID_Number: data.emiratesIDNumber || '',
    Existing_Emirates_ID_Expiry_Date: data.emiratesIDExpiryDate ? formatDate(data.emiratesIDExpiryDate) : '',
    Processing_Type: data.residentVisaStamping || '',
    Service_Type: data.serviceType || '',
    paymentmethod: data.preferredPaymentMethod || '',
    VFL_Applied: data.vflApplied || false,
    incountry: data.visaApplicationType || '',
    
    // Employment Info
    en_emptype: data.typeOfEmployment || '',
    emp_startdate: data.employmentStartDate ? formatDate(data.employmentStartDate) : '',
    emp_probationperiod: data.probationPeriod || '',
    emp_leavetype: data.annualLeaveEntitlement || '',
    emp_leavecd: data.workingDays || '',
    emp_leavewd: data.calendarDays || '',
    emp_entitlement: data.ticketEntitlementPeriod || '',
    en_jobtitle: data.attestedDegree || '',
    Designation_Amendment: data.jobTitleChange || '',
    Duration_of_Employment: data.employmentDuration || '',
    Educational_Qualification: data.educationQualification || '',
    Salary_Amendment: data.salaryChange || '',
    slr_basic: data.basicSalary || '',
    en_ticket: data.returnTicket || '',
    
    // Miscellaneous
    Name: data.firstName + ' ' + data.lastName,
    Form: 'Visa',
    Partner_Code: data.partnerCode || '',
    Partner_Name: data.partnerName || '',
    Partner_Name_Initial: data.partnerNameInitial || '',
    Passport_Type: data.passportType || '',

  };
}

export async function createVisaApplication(payload: any) {
  const token = useAuthStore.getState().auth.accessToken;
  const url = `${BASE_URL}/api/visaApplication/create`;
  const headers: Record<string, string> = {};
  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }
  try {
    const response = await axios.post(url, payload, { headers });
    return response.data;
  } catch (error: any) {
    let message = 'Unknown error';
    if (error?.response?.data?.message) message = error.response.data.message;
    else if (error?.message) message = error.message;
    throw new Error(message);
  }
}

export interface VisaDocumentUpload {
  AppID: string;
  files: Array<{
    type: string;
    base64: string;
  }>;
}

export async function uploadVisaDocuments(payload: VisaDocumentUpload) {
  const token = useAuthStore.getState().auth.accessToken;
  const url = `${BASE_URL}/api/visaApplication/upload`;
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };
  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }
  try {
    const response = await axios.post(url, payload, { headers });
    return response.data;
  } catch (error: any) {
    let message = 'Unknown error';
    if (error?.response?.data?.message) message = error.response.data.message;
    else if (error?.message) message = error.message;
    throw new Error(message);
  }
}
// Helper to create visa application and upload documents in sequence
export async function submitVisaApplicationWithDocuments(formPayload: any, files: Array<{ type: string; base64: string }>) {
  // 1. Create visa application
  const createResponse = await createVisaApplication(formPayload);
  if (createResponse.status === 'success' && createResponse.ID) {
    // 2. Map frontend file types to backend-required types
    const typeMapping: { [key: string]: string } = {
      emirates_id_copy: 'Existing_EmiratesID_Copy',
      photo: 'passportSize_photo',
      supporting_documents: 'Entry_stamp', 
      passport_copy_page_1: 'Colored_passport_page1_copy',
      passport_copy_page_2: 'Colored_passport_page1_copy', 
      payment_receipt: 'proof_of_payment',

      attested_degree: 'attested_degree',
      no_attested_degree: 'no_attested_degree',
      //we can add more mappings as needed
    };

    // Map files array, avoid duplicate types, and filter out files with missing/empty type
    const seenTypes = new Set<string>();
    const mappedFiles: Array<{ type: string; base64: string }> = [];
    for (const f of files) {
      const mappedType = typeMapping[f.type] || f.type;
      if (mappedType && typeof mappedType === 'string' && mappedType.trim() !== '' && !seenTypes.has(mappedType)) {
        mappedFiles.push({ type: mappedType, base64: f.base64 });
        seenTypes.add(mappedType);
      }
    }

    // 2. Prepare files payload
    const filesPayload: VisaDocumentUpload = {
      AppID: createResponse.ID,
      files: mappedFiles,
    };
    try {
      const uploadResponse = await uploadVisaDocuments(filesPayload);
      return { createResponse, uploadResponse };
    } catch (err) {
      throw err;
    }
  } else {
    throw new Error(createResponse.message || 'Failed to create visa application');
  }
}
