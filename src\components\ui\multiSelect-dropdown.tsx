"use client";

import { ChevronDown, Search, X } from "lucide-react";
import * as React from "react";

import { Badge } from "@/components/ui/badge";
import {
  Command,
  CommandGroup,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Command as CommandPrimitive } from "cmdk";
import { useCallback, useMemo } from "react";

type Country = Record<"value" | "label", string>;

interface MultiSelectDropdownProps {
  options: Country[];
}

export default function MultiSelectDropdown({ options }: MultiSelectDropdownProps) {
  const [open, setOpen] = React.useState(false);
  const [selected, setSelected] = React.useState<Country[]>([]);
  const [inputValue, setInputValue] = React.useState("");
  const [searchQuery, setSearchQuery] = React.useState("");
  const commandListRef = React.useRef<HTMLDivElement>(null);

  const handleUnselect = useCallback((country: Country) => {
    setSelected((prev) => prev.filter((s) => s.value !== country.value));
  }, []);

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === "Backspace" && selected.length > 0) {
        setSelected((prev) => prev.slice(0, -1));
      }
    },
    [selected]
  );

  const filteredCountries = useMemo(
    () =>
      options.filter(
        (country) =>
          !selected.includes(country) &&
          country.label.toLowerCase().includes(searchQuery.toLowerCase())
      ),
    [selected, searchQuery, options]
  );

  const handleBlur = React.useCallback((e: React.FocusEvent) => {
    const commandList = commandListRef.current;
    const isCommandListClick = commandList?.contains(e.relatedTarget as Node);

    if (!isCommandListClick) {
      setOpen(false);
    }
  }, []);

  return (
    <div className="w-full">
      <Command className="overflow-visible">
        <div className="rounded-md border border-input px-3 py-2 text-sm ring-offset-background focus-within:ring-1 focus-within:ring-ring">
          <div className="flex flex-wrap items-center gap-1">
            {selected.map((country) => {
              return (
                <Badge
                  key={country.value}
                  variant="secondary"
                  className="select-none"
                >
                  {country.label}
                  <X
                    className="size-3 text-muted-foreground hover:text-foreground ml-2 cursor-pointer"
                    onMouseDown={(e) => {
                      e.preventDefault();
                    }}
                    onClick={() => {
                      handleUnselect(country);
                    }}
                  />
                </Badge>
              );
            })}
            <CommandPrimitive.Input
              onKeyDown={handleKeyDown}
              onValueChange={setInputValue}
              value={inputValue}
              onBlur={handleBlur}
              onFocus={() => setOpen(true)}
              placeholder="Select countries..."
              className="ml-2 flex-1 bg-transparent outline-none placeholder:text-muted-foreground"
            />
            <ChevronDown className="h-4 w-4 shrink-0 opacity-50" />
          </div>
        </div>
        <div className="relative mt-2">
          <CommandList>
            {open && (
              <div
                ref={commandListRef}
                className="absolute top-0 z-10 w-full rounded-md border bg-popover text-popover-foreground shadow-md outline-none"
              >
                <div className="flex items-center border-b px-3 py-2">
                  <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                  <input
                    placeholder="Search countries..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="flex h-8 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground"
                  />
                </div>
                <CommandGroup className="h-full overflow-auto">
                  {filteredCountries.length > 0 ? (
                    filteredCountries.map((country) => (
                      <CommandItem
                        key={country.value}
                        onMouseDown={(e) => {
                          e.preventDefault();
                        }}
                        onSelect={() => {
                          setInputValue("");
                          setSelected((prev) => [...prev, country]);
                          setSearchQuery("");
                        }}
                        className={"cursor-pointer"}
                      >
                        {country.label}
                      </CommandItem>
                    ))
                  ) : (
                    <div className="py-6 text-center text-sm text-muted-foreground">
                      No results found.
                    </div>
                  )}
                </CommandGroup>
              </div>
            )}
          </CommandList>
        </div>
      </Command>
    </div>
  );
}
