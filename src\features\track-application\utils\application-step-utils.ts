import { ApplicationStep, licenseApplicationSteps, visaApplicationSteps, renewalApplicationSteps } from "../enums/application-step-enum";


export interface ZohoDeal {
    Created_Time?: string;
    Share_Estimate?: boolean;
    Payment_Status?: string;
    Application_Verified?: boolean;
    TS_App_Verified?: string | null;
    POP_Received?: boolean;
    POP_Date?: string | null;
    MOA_Status?: string | null;
    Stage?: string;
    Share_PIN_Code?: string | null;
    Public_Link?: string | null;
    EC_External_Link?: string | null;
    EC_Upload_Time_Stamp?: string | null;
    EC_Uploaded?: boolean;
    Payment_Status_Timestamp?: string;
    Time_Stamp_Issued?: string;
    Time_Stamp_Requested?: string;
    Link_Generated_Timestamp?: string;
    Type?: string;
    Category?: string;
    Quote_Sent?: boolean;
    hasMoaDoc?: boolean;
    moaCreatedDate?: string;
    moaDocument_Status?: string;
    moaDocument_Completed?: string;
    Trade_License_Issue_Date?: string;
    Trade_License_Expiry_Date?: string;
    Establishment_Card_Issue_Date?: string;
    stageHistory?: Array<{
      Stage?: string;
      Modified_Time?: string;
    }>;
  }

  export interface ZohoVisa {
    id?: string;
    Stage?: string;
    Created_Time?: string;
    incountry?: 'Yes' | 'No';
    dl_evisa?: string;
    dl_statuschange?: string;
    dl_rv?: string;
    file_eidlink?: string;
    file_medlink?: string;
    file_eidlink_timestamp?: string;
    file_medlink_timestamp?: string;
    empcon_status?: string;
    E_Sign_EC_Signed?: boolean;
    signedempconlink?: string;
    Signed_E_Sign_Emp_Contract_Ext_Link?: string;
    Contract_POP_Link?: string;
    Upload_POP_Link_New?: boolean;
    Payment_Status_Finance?: string;
    Sent_Back_Reason?: string;
    Sent_Back_Notes?: string;
    Payment_Status?: string;
    EC_Received?: string;
    POP_Date?: string;
    Signed_Date?: string;
    Residence_Visa_Issue_Date?: string;
    Visa_Issued_Date?: string;
    SC_Received1?: string;
    RV_Received?: string;
    Service_Type?: string;
    }

  type ZohoApplicationType = ZohoDeal | ZohoVisa;
  
  export function determineApplicationSteps(
    application: ZohoApplicationType,
    applicationCategory: string,
    applicationType: string
  ): {
    currentStep: number;
    stepsReached: ApplicationStep[];
    stepTimestamps: Partial<Record<ApplicationStep, string>>;
    steps: ApplicationStep[];
  } {
    // Determine steps based on application category
    const steps =
      applicationCategory === 'License' && applicationType === 'New'
        ? licenseApplicationSteps
        : applicationCategory === 'License' && applicationType === 'Renewal'
        ? renewalApplicationSteps
        : applicationCategory === 'Visa'
        ? visaApplicationSteps
        : licenseApplicationSteps; 
  
        const stepsCondition =
        applicationCategory === 'License' && applicationType === 'New'
          ? licenseApplicationStatusCondition(application as ZohoDeal, steps)
          : applicationCategory === 'License' && applicationType === 'Renewal'
          ? renewalApplicationStatusCondition(application as ZohoDeal, steps)
          : applicationCategory === 'Visa'
          ? visaApplicationStatusCondition(application as ZohoVisa, steps)
          : licenseApplicationStatusCondition(application as ZohoDeal, steps);

    return stepsCondition;
  }

  export function licenseApplicationStatusCondition(deal: ZohoDeal, steps: ApplicationStep[]): {
    currentStep: number;
    stepsReached: ApplicationStep[];
    stepTimestamps: Partial<Record<ApplicationStep, string>>;
    steps: ApplicationStep[];
    } {
    const stepsReached: ApplicationStep[] = [];
    const stepTimestamps: Partial<Record<ApplicationStep, string>> = {};
  
    const stageHistory = deal.stageHistory || [];

  // get timestamp for a specific stage
  const getStageTimestamp = (targetStage: string): string | undefined => {
    const entry = stageHistory.find(
      (s) => s?.Stage?.toLowerCase() === targetStage.toLowerCase()
    );
    return entry?.Modified_Time;
  };
    // Step 1: License Application Submitted
    if (deal.Created_Time) {
      stepsReached.push(ApplicationStep.LicenseApplicationSubmitted);
      stepTimestamps[ApplicationStep.LicenseApplicationSubmitted] = deal.Created_Time;
    }

    // Step 2: Estimate Sent
    if (deal.Share_Estimate || deal.Payment_Status === 'Due') {
      stepsReached.push(ApplicationStep.EstimateSent);
      stepTimestamps[ApplicationStep.EstimateSent] = deal.Payment_Status_Timestamp || deal.Created_Time || '';
    }

    // Step 3: Application Verified
    if (deal.Application_Verified || deal.TS_App_Verified) {
      stepsReached.push(ApplicationStep.ApplicationVerified);
      stepTimestamps[ApplicationStep.ApplicationVerified] = deal.TS_App_Verified || '';
    }

    // Step 4: Received Proof of Payment
    if (deal.POP_Received || deal.POP_Date) {
      stepsReached.push(ApplicationStep.ReceivedProofOfPayment);
      stepTimestamps[ApplicationStep.ReceivedProofOfPayment] = deal.POP_Date || '';
    }

    // Step 5: KYC Sent
    const kycSentDate = getStageTimestamp('KYC Sent');
    if (kycSentDate) {
      stepsReached.push(ApplicationStep.KYCSent);
      stepTimestamps[ApplicationStep.KYCSent] = kycSentDate;
    }

    // Step 6: KYC Approved
    const kycApprovedDate = getStageTimestamp('KYC Approved');
    if (kycApprovedDate) {
      stepsReached.push(ApplicationStep.KYCApproved);
      stepTimestamps[ApplicationStep.KYCApproved] = kycApprovedDate;
    }

    // Step 7: Summary Sent for E-Sign
    const summarySentForESignDate = getStageTimestamp('Summary Sent For E-Sign');
    if (summarySentForESignDate) {
      stepsReached.push(ApplicationStep.SummarySentForESign);
      stepTimestamps[ApplicationStep.SummarySentForESign] = summarySentForESignDate;
    }

    // Step 8: Summary Signed
    const summarySignedDate = getStageTimestamp('Summary Signed');
    if (summarySignedDate) {
      stepsReached.push(ApplicationStep.SummarySigned);
      stepTimestamps[ApplicationStep.SummarySigned] = summarySignedDate;
    }

    // Step 9: Company Name Approved
    const companyNameApprovedDate = getStageTimestamp('Company Name Reserved');
    if (companyNameApprovedDate) {
      stepsReached.push(ApplicationStep.CompanyNameApproved);
      stepTimestamps[ApplicationStep.CompanyNameApproved] = companyNameApprovedDate;
    }

    // Step 10: Resolution Signed
    const resolutionSignedDate = getStageTimestamp('E-Legal Signed');
    if (resolutionSignedDate) {
      stepsReached.push(ApplicationStep.ResolutionSigned);
      stepTimestamps[ApplicationStep.ResolutionSigned] = resolutionSignedDate;
    }

    // Step 11: MOA Sent (NOTE: not mapped in your sample yet — placeholder)
    if (getStageTimestamp('MOA Signed') || deal.MOA_Status== "Signed") {
      stepsReached.push(ApplicationStep.MOASigned);
      stepTimestamps[ApplicationStep.MOASigned] = getStageTimestamp('MOA Signed') || "";
    }

    // Step 12: License Documents Issued
    if (deal.Share_PIN_Code && deal.Public_Link) {
      stepsReached.push(ApplicationStep.LicenseDocumentsIssued);
      stepTimestamps[ApplicationStep.LicenseDocumentsIssued] = deal.Link_Generated_Timestamp || '';
    }

    // Step 13: Establishment Card Sent
    if (
      deal.EC_External_Link ||
      deal.EC_Upload_Time_Stamp ||
      deal.EC_Uploaded
    ) {
      stepsReached.push(ApplicationStep.EstablishmentCardSent);
      stepTimestamps[ApplicationStep.EstablishmentCardSent] = deal.EC_Upload_Time_Stamp || '';
    }


    // Find the last reached step index
    const currentStep = Math.max(
      ...stepsReached.map((step) => steps.indexOf(step)),
      0
    );
  
    return { currentStep, stepsReached ,stepTimestamps, steps};
  }

  export function visaApplicationStatusCondition(
    deal: ZohoVisa,
    steps: ApplicationStep[],
  ): {
    currentStep: number;
    stepsReached: ApplicationStep[];
    stepTimestamps: Partial<Record<ApplicationStep, string>>;
    steps: ApplicationStep[];
  } {
    const stepsReached: ApplicationStep[] = [];
    const stepTimestamps: Partial<Record<ApplicationStep, string>> = {};
  
    const inCountry = deal.incountry === 'Yes';
  
    const signedEC =
      deal.empcon_status === 'Signed' ||
      deal.Signed_E_Sign_Emp_Contract_Ext_Link != null ||
      deal.signedempconlink != null;
  
    const popReceived =
      !!deal.Contract_POP_Link ||
      !!deal.Upload_POP_Link_New ||
      (deal.Payment_Status?.includes('Full Payment') ||
        deal.Payment_Status?.includes('VFL -'));
  
    const applicationReceived = !!deal.Created_Time;
    const eVisaIssued = !!deal.dl_evisa;
    const statusChangeIssued = inCountry && !!deal.dl_statuschange;
    const eidFormCompleted = !!deal.file_eidlink;
    const medicalFitnessCompleted = !!deal.file_medlink;
    const residenceVisaStage = eidFormCompleted && medicalFitnessCompleted;
    const residenceVisaIssued = !!deal.dl_rv;

    // Filter steps based on conditions - remove StatusChangeIssued if not in-country
    const filteredSteps = inCountry 
      ? steps 
      : steps.filter(step => step !== ApplicationStep.StatusChangeIssued);
  
    // 1. Application Received
    if (applicationReceived) {
      stepsReached.push(ApplicationStep.ApplicationReceived);
      stepTimestamps[ApplicationStep.ApplicationReceived] = deal.Created_Time || '';
    }

    // 2. Signed EC
    if (signedEC) {
      stepsReached.push(ApplicationStep.SignedEC);
      stepTimestamps[ApplicationStep.SignedEC] =
        deal.Signed_Date || '';
    }
  
    // 3. POP Received
    if (popReceived) {
      stepsReached.push(ApplicationStep.POPReceived);
      stepTimestamps[ApplicationStep.POPReceived] = deal.POP_Date || '';
    }
  
    // 4. E-Visa Issued
    if (eVisaIssued) {
      stepsReached.push(ApplicationStep.EVisaIssued);
      stepTimestamps[ApplicationStep.EVisaIssued] = deal.Visa_Issued_Date ? deal.Visa_Issued_Date : deal.EC_Received || '';
    }
  
    // 5. Status Change Issued (only if in-country)
    if (statusChangeIssued) {
      stepsReached.push(ApplicationStep.StatusChangeIssued);
      stepTimestamps[ApplicationStep.StatusChangeIssued] = deal.SC_Received1 || '';
    }
  
    // 6. EID Form Completed
    if (eidFormCompleted) {
      stepsReached.push(ApplicationStep.EIDFormCompleted);
      stepTimestamps[ApplicationStep.EIDFormCompleted] =
        deal.file_eidlink_timestamp || '';
    }
  
    // 7. Medical Fitness Test Completed
    if (medicalFitnessCompleted) {
      stepsReached.push(ApplicationStep.MedicalFitnessTestCompleted);
      stepTimestamps[ApplicationStep.MedicalFitnessTestCompleted] =
        deal.file_medlink_timestamp || '';
    }
  
    // 8. Residence Visa Stage
    if (residenceVisaStage) {
      stepsReached.push(ApplicationStep.ResidenceVisaStage);
      stepTimestamps[ApplicationStep.ResidenceVisaStage] =
        deal.file_eidlink_timestamp || deal.file_medlink_timestamp || '';
    }
  
    // 9. Residence Visa Issued
    if (residenceVisaIssued) {
      stepsReached.push(ApplicationStep.ResidenceVisaIssued);
      stepTimestamps[ApplicationStep.ResidenceVisaIssued] = deal.Residence_Visa_Issue_Date ? deal.Residence_Visa_Issue_Date : deal.RV_Received || '';
    }

    // Find the last reached step index
    const currentStep = Math.max(
      ...stepsReached.map((step) => filteredSteps.indexOf(step)),
      0
    );

    return { currentStep, stepsReached, stepTimestamps, steps: filteredSteps };
  }


  export function renewalApplicationStatusCondition(deal: ZohoDeal, steps: ApplicationStep[]): {
    currentStep: number;
    stepsReached: ApplicationStep[];
    stepTimestamps: Partial<Record<ApplicationStep, string>>;
    steps: ApplicationStep[];
    } {
    const stepsReached: ApplicationStep[] = [];
    const stepTimestamps: Partial<Record<ApplicationStep, string>> = {};

    // Step 1: Renewal Application Received
    const renewalCreatedTime = deal.Created_Time;
    if (renewalCreatedTime) {
      stepsReached.push(ApplicationStep.RenewalApplicationReceived);
      stepTimestamps[ApplicationStep.RenewalApplicationReceived] = renewalCreatedTime;
    }

    // Step 2: Estimate Sent
    if (deal.Quote_Sent) {
      stepsReached.push(ApplicationStep.EstimateSent);
      stepTimestamps[ApplicationStep.EstimateSent] = deal.Payment_Status_Timestamp || deal.Created_Time || '';
    }

    // Step 3: Application Verified
    if (deal.Application_Verified || deal.TS_App_Verified) {
      stepsReached.push(ApplicationStep.ApplicationVerified);
      stepTimestamps[ApplicationStep.ApplicationVerified] = deal.TS_App_Verified || '';
    }

    // Step 4: MOA Sent
    if (deal.hasMoaDoc) {
      stepsReached.push(ApplicationStep.MOASent);
      stepTimestamps[ApplicationStep.MOASent] = deal.moaCreatedDate || '';
    }

    // Step 5: POP Received
    if (deal.POP_Received || deal.POP_Date) {
      stepsReached.push(ApplicationStep.POPReceived);
      stepTimestamps[ApplicationStep.POPReceived] = deal.POP_Date || '';
    }

    // Step 6: MOA Signed
    if (deal.moaDocument_Status === 'SIGNED') {
      stepsReached.push(ApplicationStep.MOASigned);
      stepTimestamps[ApplicationStep.MOASigned] = deal.moaDocument_Completed;
    }

    // Step 7: License Documents Issued
    if (deal.Trade_License_Issue_Date || deal.Trade_License_Expiry_Date) {
      stepsReached.push(ApplicationStep.LicenseDocumentsIssued);
      stepTimestamps[ApplicationStep.LicenseDocumentsIssued] = deal.Link_Generated_Timestamp || deal.Trade_License_Issue_Date || deal.Trade_License_Expiry_Date;
    }

    // Step 8: Establishment Card Issued
    if (deal.Establishment_Card_Issue_Date) {
      stepsReached.push(ApplicationStep.EstablishmentCardIssued);
      stepTimestamps[ApplicationStep.EstablishmentCardIssued] = deal.Establishment_Card_Issue_Date;
    }
  
    // Find the last reached step index
    const currentStep = Math.max(
      ...stepsReached.map((step) => steps.indexOf(step)),
      0
    );
  
    return { currentStep, stepsReached ,stepTimestamps, steps};
  }