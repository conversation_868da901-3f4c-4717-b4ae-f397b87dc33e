import { Link } from '@tanstack/react-router'
import { MoveRight } from 'lucide-react'

type FzButtonProps = {
  link: string
  label?: string
  className?: string
  light?: boolean
}

const FzButton = ({ link, label = 'Learn More', className = '', light = false }: FzButtonProps) => {
  return (
    <Link target='_blank' to={link} className={`font-extralight uppercase font-wotfard group transition-all duration-300 ${className}`}>
      <div className='relative inline-flex items-center justify-start px-[30px]'>
        <div className="absolute inset-0 flex items-center justify-start">
          <span
            className={`w-14 h-14 rounded-full transition-all duration-300 ease-in-out group-hover:w-full ${
              light
                ? 'bg-white/30 group-hover:bg-white'
                : 'bg-ifza-500 group-hover:bg-neutral-800'
            }`}
          ></span>
        </div>

        <span 
          className={`relative z-10 transition-colors duration-300 
          ${light ? 'text-white group-hover:text-black' : 'text-black group-hover:text-white' }
          `}>
          {label}
        </span>

        <MoveRight
          strokeWidth={1.3}
          className={`relative z-10 ml-4 size-8 transition-all duration-300 group-hover:translate-x-[10px] ${
            light ? 'text-white group-hover:text-black' : 'text-black group-hover:text-white'
          }`}
        />
      </div>
    </Link>
  )
}

export default FzButton
