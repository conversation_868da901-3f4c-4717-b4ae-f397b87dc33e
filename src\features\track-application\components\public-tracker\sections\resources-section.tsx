import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"

import SectionTitle from '@/features/track-application/components/public-tracker/custom-ui/section-title'
import { accordionData } from "@/features/track-application/data/resources-data"


const ResourcesSection = () => {
  return (
    <section id='resources' className='container w-full gap-8 py-16 lg:py-32'>
      <SectionTitle
        title="Quick Access to Essential Resources"
        description="Explore the full range of services offered by IFZA to support you and your business every step of the way."
        className="mb-8"
      />

      <div className="w-full lg:w-10/12 xl:w-8/12 mx-auto">
          <Accordion
            type="single"
            collapsible
            className="w-full group"
            defaultValue="item-1"
          >
            {accordionData.map((item, i) => {
              const formattedIndex = String(i + 1).padStart(2, '0');
              const ResourcesContent = item.content;

              return (
                <AccordionItem key={item.title} value={`item-${item.title}`}>
                  <AccordionTrigger>
                    <div className="flex md:items-center gap-4 md:gap-6">
                      <span className="w-14 text-3xl md:text-4xl font-tt-firs-neue font-light">
                        {formattedIndex}.
                      </span>
                      <span className="text-lg font-tt-firs-neue font-normal uppercase">
                        {item.title}
                      </span>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className='text-md'>
                    <ResourcesContent />
                  </AccordionContent>
                </AccordionItem>
              );
            })}
          </Accordion>
      </div>
    </section>
  )
}

export default ResourcesSection