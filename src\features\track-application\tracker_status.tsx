import { Header } from '@/components/layout/header'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { ThemeSwitch } from '@/components/theme-switch'
import { licenseApplicationSteps, renewalApplicationSteps, visaApplicationSteps } from '@/features/track-application/enums/application-step-enum'
import TrackerBody from './components/tracker-entire-body'


const currentStep = 2
//index for the steps start from 0 
// if currentStep= 2 , then first 2 steps are completed and third step is the actual current step


export default function TrackerStatusPage() {
  const applicationType = 'License';
  const steps =
    applicationType === 'License'
      ? licenseApplicationSteps
      : applicationType === 'Visa'
      ? visaApplicationSteps
      : renewalApplicationSteps;
  return (
    <>
      <Header>
        <div className="ml-auto flex items-center space-x-4">
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <TrackerBody steps={steps} 
      currentStep={currentStep} 
      applicationType={applicationType} 
      status="Estimate Sent" 
      applicationNumber="RA-**********" 
      applicationDate={new Date()} 
      trackNewApplicationUrl="/track-application"
      />
    </>
  )
}