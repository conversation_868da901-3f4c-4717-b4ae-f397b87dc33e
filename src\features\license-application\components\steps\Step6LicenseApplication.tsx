import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import AddUBO from '../../addUBO';
import { FilePenLine, Lightbulb } from 'lucide-react';

interface UboDeclarationStepProps {
  form: any;
}

const Step6LicenseApplication: React.FC<UboDeclarationStepProps> = ({ form }) => {
  return (
    <Card className='mt-6'>
      <CardContent>
        <Form {...form}>
          <form className='space-y-4 fz-form'>
            <CardHeader className='px-0 pt-0 pb-0'>
              <CardTitle>
                ULTIMATE BENEFICIAL OWNERSHIP ( Step 6 of 6 )
              </CardTitle>
            </CardHeader>
            {/* Alert */}
            <Alert className='border-dashed border-primary mt-2 mb-2 alert-bg-warning dark:bg-primary/40'>
              <AlertDescription>
                This declaration is made in accordance with UAE Cabinet
                Resolution No. 58 of 2020 Concerning Procedures for
                Regulating Ultimate Beneficial Ownership (the “UBO
                Decision”).
              </AlertDescription>
            </Alert>
            {/* Company Statement */}
            <FormField
              control={form.control}
              name='companyStatementValue'
              render={({ field }) => (
                <FormItem data-error-field="companyStatementValue">
                  <FormLabel>
                    1. The company agrees to the statement below :{' '}
                    <span className='text-red-500'>*</span>
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      className='flex align-center'
                      value={field.value}
                      onValueChange={field.onChange}
                    >
                      <RadioGroupItem
                        className='mt-1'
                        value='Yes'
                        id='yes'
                      />
                      <label htmlFor='yes'>Yes</label>
                      <RadioGroupItem
                        className='mt-1'
                        value='No'
                        id='no'
                      />
                      <label htmlFor='no'>No</label>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {/* Alert */}
            <Alert className='border-dashed border-primary mt-4 alert-bg-warning dark:bg-primary/40'>
              <AlertDescription>
                The Licensee certifies that the Company Member(s)
                (shareholder(s)) described in the License Application Form
                is/are the UBO of the Licensee and if there is more than
                one Company Member, then the Company Members are the UBO
                in the same proportion as their shareholdings.{' '}
              </AlertDescription>
            </Alert>
            {/* when "No" is selected */}
            {form.watch('companyStatementValue') === 'No' && (
              <>
                <h3 className='text-xl mt-4 mb-4 font-semibold'>OR</h3>
                <FormField
                  control={form.control}
                  name='agreement'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        2. The company agrees to the statement below :
                      </FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder='Select' />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value='Yes'>Yes</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {/* Alert */}
                <Alert className='border-dashed border-primary mt-4 alert-bg-warning dark:bg-primary/40'>
                  <AlertDescription>
                    The Licensee certifies that the following person(s)
                    is/are the UBO of the Licensee. The full details of
                    the UBO are set out below.
                  </AlertDescription>
                </Alert>
                {/*Add UBO(s) */}
                <h3 className='text-xl mt-4 mb-4 font-semibold'>
                  Add UBO(s)
                </h3>
                <AddUBO />
              </>
            )}
            <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
              <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
              </span>
              <div className='flex flex-col ml-2'>
                <AlertDescription>
                  <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    We hereby declare that the information provided in
                    this declaration is true and accurate and if such
                    information changes, we will promptly notify
                    International Free Zone Authority FZCO (“IFZA”).
                  </p>
                  <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    We confirm that if any of the UBO information should
                    change, we will file an amended declaration within 15
                    days of becoming aware of such change in accordance
                    with Article 8(1) and Article 10(1) of the UBO
                    Decision.
                  </p>
                  <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    We acknowledge that if any information provided by
                    me/us is subsequently found to be untrue, inaccurate
                    or misleading, IFZA may suspend or terminate our
                    license. We hereby authorize IFZA to make any
                    enquiries from any person or entity, it may deem
                    necessary in connection with this declaration.{' '}
                  </p>
                  <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    {' '}
                    We shall maintain a Register of Beneficial Owners that
                    the Licensee is required to maintain in accordance
                    with Article 8 of the UBO Decision.{' '}
                  </p>
                  <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    {' '}
                    We shall maintain a Register of Partners or
                    Shareholders that the Licensee is required to maintain
                    in accordance with Article 10 of the UBO Decision.
                  </p>
                  <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    We authorize IFZA to transmit this UBO Information, in
                    such form as IFZA shall determine, to the concerned
                    regulatory authorities as set out in the UBO Decision.{' '}
                  </p>
                  <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    {' '}
                    The signatory to this document has all necessary
                    authority to provide this declaration for and on
                    behalf of the Licensee.{' '}
                  </p>
                </AlertDescription>
              </div>
            </Alert>
            {/* UBO Declaration */}
            <FormField
              control={form.control}
              name='declaration'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    UBO Declaration{' '}
                    <span className='text-red-500'>*</span>
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger name='declaration'>
                        <SelectValue placeholder='Select' />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value='Yes, we agree with the statement above'>
                        Yes, we agree with the statement above
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            {/* Alert */}
            <Alert className='border-dashed border-primary mt-4 mb-4 alert-bg-warning dark:bg-primary/40'>
              <AlertDescription>
                I, the General Manager, confirm that the information given
                in the form is true, complete and accurate. The
                authorization is irrevocable until a written notification
                is submitted to International Free Zone Authority (IFZA).
              </AlertDescription>
            </Alert>
            {/* Confirmation */}
            <FormField
              control={form.control}
              name='confirmation'
              render={({ field }) => (
                <FormItem className='mb-4'>
                  <FormLabel>
                    Confirmation <span className='text-red-500'>*</span>
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger name='confirmation'>
                        <SelectValue placeholder='Select' />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value='Yes, Agreed'>
                        Yes, Agreed
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            {/* General Manager/Authorized Signatory Name */}
            <FormField
              control={form.control}
              name='authorizedSignatoryName'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    General Manager/Authorized Signatory Name{' '}
                    <span className='text-red-500'>*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      value={field.value || ''}
                      // disabled
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {/* General Manager/Authorized Signatory Email */}
            <FormField
              control={form.control}
              name='authorizedSignatoryEmail'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    General Manager/Authorized Signatory Email{' '}
                    <span className='text-red-500'>*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      type='email'
                      {...field}
                      value={field.value || ''}
                      // disabled
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Alert className='flex items-start border-dashed border-blue-500 mt-2 bg-slate-50 dark:bg-primary/20'>
              <span className='bg-blue-50 p-2 rounded inline-block dark:text-primary-dark'>
                <Lightbulb className='w-4 h-4 stroke-blue-500 dark:text-primary-dark' />
              </span>
              <div className='flex space-y-2 flex-col ml-3'>
                <AlertDescription>
                  <p className='text-sm text-slate-800 dark:text-slate-400'>
                    IFZA offers assistance with bookkeeping, financial
                    statement preparation, Corporate Tax and VAT support
                    (including registrations). Refer to our current
                    pricelist and services :{' '}
                    <a
                      href='https://cdn.ifza.com/marketing/IFZA_Corporate_Consulting_Price%20List_Oct24.pdf'
                      target='_blank'
                      rel='noopener noreferrer'
                      className='text-blue-500 underline'
                    >
                      IFZA Corporate Consulting Price List
                    </a>
                  </p>
                </AlertDescription>
              </div>
            </Alert>
            {/* Do you require assistance with Bookkeeping and Financial Statement Preparation? */}
            <FormField
              control={form.control}
              name='preparation'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Do you require assistance with Bookkeeping and
                    Financial Statement Preparation ?
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      className='flex align-center'
                      value={field.value}
                      onValueChange={field.onChange}
                    >
                      <RadioGroupItem
                        className='mt-1'
                        value='Yes'
                        id='yes'
                      />
                      <label htmlFor='yes'>Yes</label>
                      <RadioGroupItem
                        className='mt-1'
                        value='No'
                        id='no'
                      />
                      <label htmlFor='no'>No</label>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {/* Do you require support for Corporate Tax or VAT registration? */}
            <FormField
              control={form.control}
              name='registration'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Do you require support for Corporate Tax or VAT
                    registration ?
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      className='flex align-center'
                      value={field.value}
                      onValueChange={field.onChange}
                    >
                      <RadioGroupItem
                        className='mt-1'
                        value='Yes'
                        id='option1'
                      />
                      <label htmlFor='option1'>Yes</label>
                      <RadioGroupItem
                        className='mt-1'
                        value='No'
                        id='option2'
                      />
                      <label htmlFor='option2'>No</label>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {/* Instructions Text */}
            {(form.watch('preparation') === 'Yes' ||
              form.watch('registration') === 'Yes') && (
              <>
                <AlertDescription>
                  {' '}
                  Please sign-up to one of our packages using the link and
                  we will contact you :{' '}
                  <a
                    href='https://forms.zohopublic.com/ifzafjr/form/CorporateConsulting/formperma/7asmlZVKC1KDSjzdDVx8kuQCjGmql4rKZ7xFf-8MgP8'
                    target='_blank'
                    rel='noopener noreferrer'
                    className='text-blue-500 underline'
                  >
                    Corporate Consulting Sign-up
                  </a>
                </AlertDescription>
              </>
            )}
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default Step6LicenseApplication;
