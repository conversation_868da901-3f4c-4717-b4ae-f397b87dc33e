import React, { useEffect } from 'react'
import axios from 'axios'
import { useFormContext, useWatch } from 'react-hook-form'
import { FilePenLine } from 'lucide-react'
import { BASE_URL } from '@/utils/network'
import { toast } from '@/hooks/use-toast'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Checkbox } from '@/components/ui/checkbox'
import { CountryDropdown } from '@/components/ui/country-dropdown'
import { DateTimePicker } from '@/components/ui/datetime-picker'
import FileUploadField from '@/components/ui/fileUpload'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Tooltip,
  TooltipTrigger,
  TooltipContent,
} from '@/components/ui/tooltip'
import { nationalityOverrides, transliterate } from '../../schemas/translation'
import countries from 'i18n-iso-countries'
import enLocale from 'i18n-iso-countries/langs/en.json'

countries.registerLocale(enLocale)
import { ApplicationFormValues } from '../../types/application-form-types'
import { countryNameOverrides } from '@/components/ui/country-dropdown'

interface Step3FormProps {
  setDate: React.Dispatch<React.SetStateAction<Date | undefined>>
}

const Step3VisaApplication: React.FC<Step3FormProps> = () => {
  const form = useFormContext<ApplicationFormValues>()
  const nationality = useWatch({
    control: form.control,
    name: 'nationality',
  })
  const religion = useWatch({ control: form.control, name: 'religion' })
  const placeOfIssue = useWatch({
    control: form.control,
    name: 'placeOfIssue',
  })
  const cityOfBirth = useWatch({
    control: form.control,
    name: 'cityOfBirth',
  })
  const motherFullName = useWatch({
    control: form.control,
    name: 'motherFullName',
  })
  useEffect(() => {
    form.setValue('placeOfIssueArabic', transliterate(placeOfIssue || ''))
    form.trigger('placeOfIssueArabic')
  }, [placeOfIssue])
  useEffect(() => {
    form.setValue('cityOfBirthArabic', transliterate(cityOfBirth || ''))
    form.trigger('cityOfBirthArabic')
  }, [cityOfBirth])
  useEffect(() => {
    form.setValue('motherFullNameArabic', transliterate(motherFullName || ''))
    form.trigger('motherFullNameArabic')
  }, [motherFullName])
  // Log all form values for debugging
  console.log('Step 3 Form Values:', form.watch())

  return (
    <>
      <form className='space-y-4 fz-form'>
        <div className='flex flex-wrap space-between gap-y-4'>
          {/* Colored Passport Copy Page 1 */}
          <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
            <FormField
              control={form.control}
              name='coloredPassport'
              render={({ field }) => {
                const [isUploading, setIsUploading] = React.useState(false)

                return (
                  <FormItem data-field='coloredPassport'>
                    <FormLabel>
                      Colored Passport Copy Page 1{' '}
                      <span className='text-red-500'>*</span>
                    </FormLabel>
                    <FormControl>
                      <div>
                        <FileUploadField
                          accept='image/*'
                          value={field.value}
                          onchoose={(file) => {
                            if (!file) {
                              form.setValue('coloredPassport', null)
                              form.trigger('coloredPassport')
                              return
                            }

                            setIsUploading(true)
                            const reader = new FileReader()

                            reader.onloadend = async () => {
                              const base64 = (reader.result as string).split(
                                ','
                              )[1]

                              try {
                                const response = await axios.post(
                                  `${BASE_URL}/api/utils/GetPassport`,
                                  { base64image: base64 }
                                )

                                let result = response.data

                                if (
                                  typeof result.responseMessage === 'string'
                                ) {
                                  try {
                                    result.responseMessage = JSON.parse(
                                      result.responseMessage
                                    )
                                  } catch (e) {
                                    console.error(
                                      'Error parsing responseMessage:',
                                      e
                                    )
                                  }
                                }

                                form.setValue('coloredPassport', file)
                                form.trigger('coloredPassport')

                                if (
                                  JSON.stringify(result).includes(
                                    'ContainerList'
                                  ) &&
                                  result.responseMessage?.ContainerList?.List
                                ) {
                                  const list =
                                    result.responseMessage.ContainerList.List
                                  let mp: Record<string, string> = {}
                                  let doctypeFound = false

                                  for (const item of list) {
                                    const docType =
                                      item.OneCandidate?.FDSIDList?.dDescription
                                    console.log('Detected docType:', docType)
                                    if (docType === 'Passport')
                                      doctypeFound = true

                                    if (doctypeFound && item.Text?.fieldList) {
                                      for (const field of item.Text.fieldList) {
                                        if (field.lcid === 0) {
                                          mp[field.fieldName] = field.value
                                        }
                                      }
                                    }
                                  }

                                  console.log('Extracted passport fields:', mp)

                                  if (doctypeFound) {
                                    if (mp['Given Names']) {
                                      form.setValue(
                                        'firstName2',
                                        mp['Given Names']
                                      )
                                      form.trigger('firstName2')
                                    }
                                    if (mp['Surname']) {
                                      form.setValue('lastName2', mp['Surname'])
                                      form.trigger('lastName2')
                                    }
                                    if (mp['Document Number']) {
                                      form.setValue(
                                        'passportNumber',
                                        mp['Document Number']
                                      )
                                      form.trigger('passportNumber')
                                    }

                                    if (mp['Date of Birth']) {
                                      const dob = new Date(mp['Date of Birth'])
                                      if (!isNaN(dob.getTime())) {
                                        form.setValue('dateOfBirth', dob)
                                        form.trigger('dateOfBirth')
                                      }
                                    }

                                    if (mp['Date of Expiry']) {
                                      const expiry = new Date(
                                        mp['Date of Expiry']
                                      )
                                      if (!isNaN(expiry.getTime())) {
                                        form.setValue(
                                          'passportExpiryDate',
                                          expiry
                                        )
                                        form.trigger('passportExpiryDate')
                                      }
                                    }

                                    if (mp['Date of Issue']) {
                                      const issue = new Date(
                                        mp['Date of Issue']
                                      )
                                      if (!isNaN(issue.getTime())) {
                                        form.setValue(
                                          'passportIssueDate',
                                          issue
                                        )
                                        form.trigger('passportIssueDate')
                                      }
                                    }

                                    if (mp['Place of Issue']) {
                                      form.setValue(
                                        'placeOfIssue',
                                        mp['Place of Issue']
                                      )
                                      form.trigger('placeOfIssue')
                                    }
                                    if (mp['Place of Birth']) {
                                      form.setValue(
                                        'cityOfBirth',
                                        mp['Place of Birth']
                                      )
                                      form.trigger('cityOfBirth')
                                    }

                                    if (mp['Fathers Name']) {
                                      form.setValue(
                                        'fatherFullName',
                                        mp['Fathers Name']
                                      )
                                      form.trigger('fatherFullName')
                                    }

                                    if (mp['Mothers Name']) {
                                      form.setValue(
                                        'motherFullName',
                                        mp['Mothers Name']
                                      )
                                      form.trigger('motherFullName')
                                    }

                                    if (mp['Issuing State Name']) {
                                      const rawNationality = mp[
                                        'Issuing State Name'
                                      ]
                                        .trim()
                                        .toLowerCase()
                                      const overridden =
                                        countryNameOverrides[rawNationality]
                                      const normalizedNationality =
                                        overridden || mp['Issuing State Name']

                                      form.setValue(
                                        'countryOfIssuance',
                                        normalizedNationality
                                      )
                                      form.trigger('countryOfIssuance')
                                    }

                                    if (mp['Nationality']) {
                                      let rawNationality = mp['Nationality']
                                        .toString()
                                        .trim()
                                        .toLowerCase()

                                      rawNationality = rawNationality.replace(
                                        /\s+/g,
                                        ' '
                                      )

                                      let normalizedNationality =
                                        countryNameOverrides[rawNationality] ||
                                        nationalityOverrides[rawNationality] ||
                                        mp['Nationality']

                                      form.setValue(
                                        'nationality',
                                        normalizedNationality
                                      )
                                      form.trigger('nationality')
                                    }

                                    if (mp['Sex']) {
                                      const gender =
                                        mp['Sex'] === 'M'
                                          ? 'Male'
                                          : mp['Sex'] === 'F'
                                            ? 'Female'
                                            : undefined

                                      if (gender) {
                                        form.setValue('gender', gender)
                                        form.trigger('gender')
                                      }
                                    }
                                  } else {
                                    toast({
                                      title: 'Invalid Passport',
                                      description:
                                        'Please upload a clear scan of a valid passport.',
                                      variant: 'destructive',
                                    })
                                  }
                                } else {
                                  toast({
                                    title: 'Invalid Response',
                                    description:
                                      'Unexpected data format received.',
                                    variant: 'destructive',
                                  })
                                }
                              } catch (error) {
                                console.error(
                                  'Error uploading passport:',
                                  error
                                )
                                toast({
                                  title: 'Error',
                                  description:
                                    'Could not extract data from the passport.',
                                  variant: 'destructive',
                                })
                              } finally {
                                setIsUploading(false)
                              }
                            }

                            reader.readAsDataURL(file)
                          }}
                        />
                        {isUploading && (
                          <div className='relative flex items-center gap-2 mt-1 text-primary dark:text-primary-dark text-sm'>
                            <svg
                              className='animate-spin h-4 w-4'
                              viewBox='0 0 24 24'
                            >
                              <circle
                                className='opacity-25'
                                cx='12'
                                cy='12'
                                r='10'
                                stroke='#c7a51f'
                                strokeWidth='4'
                                fill='none'
                              />
                              <path
                                className='opacity-75'
                                fill='#c7a51f'
                                d='M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z'
                              />
                            </svg>
                            <span>Extracting passport data...</span>
                          </div>
                        )}
                      </div>
                    </FormControl>
                    <FormDescription>
                      Please upload page 1 of your passport. We accept only
                      image/photo in the format of .jpg OR .jpeg
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )
              }}
            />
          </div>

          {/* Nationality */}
          <div className='flex-1'>
            <FormField
              control={form.control}
              name='nationality'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Nationality <span className='text-red-500'>*</span>
                  </FormLabel>
                  <FormControl>
                    <CountryDropdown
                      data-field='nationality'
                      placeholder='Country'
                      defaultValue={field.value}
                      onChange={(country) => {
                        field.onChange(country.name)
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Colored Passport Copy Page 2 : Show this field when 'Country of Issuance' is 'Syria OR India OR Turkey' */}
        {(nationality === 'Syria' ||
          nationality === 'India' ||
          nationality === 'Turkey') && (
          <>
            <div className='mb-4'>
              <FormField
                control={form.control}
                name='coloredPassport2'
                render={({ field }) => {
                  const [isUploading, setIsUploading] = React.useState(false)

                  return (
                    <FormItem data-field='coloredPassport2' >
                      <FormLabel>
                        Colored Passport Copy Page 2{' '}
                        <span className='text-red-500'>*</span>
                      </FormLabel>
                      <FormControl>
                        <div>
                          <FileUploadField
                            accept='image/*'
                            value={field.value}
                            onchoose={(file) => {
                              if (!file) {
                                form.setValue('coloredPassport2', null)
                                form.trigger('coloredPassport2')
                                return
                              }

                              setIsUploading(true)
                              const reader = new FileReader()

                              reader.onloadend = async () => {
                                const base64 = (reader.result as string).split(
                                  ','
                                )[1]

                                try {
                                  const response = await axios.post(
                                    `${BASE_URL}/api/utils/GetPassport`,
                                    { base64image: base64 }
                                  )

                                  let result = response.data

                                  if (
                                    typeof result.responseMessage === 'string'
                                  ) {
                                    try {
                                      result.responseMessage = JSON.parse(
                                        result.responseMessage
                                      )
                                    } catch (e) {
                                      console.error(
                                        'Error parsing responseMessage:',
                                        e
                                      )
                                    }
                                  }

                                  form.setValue('coloredPassport2', file)
                                  form.trigger('coloredPassport2')

                                  if (
                                    JSON.stringify(result).includes(
                                      'ContainerList'
                                    ) &&
                                    result.responseMessage?.ContainerList?.List
                                  ) {
                                    const list =
                                      result.responseMessage.ContainerList.List
                                    let mp: Record<string, string> = {}
                                    let doctypeFound = false

                                    for (const item of list) {
                                      const docType =
                                        item.OneCandidate?.FDSIDList
                                          ?.dDescription
                                      console.log('Detected docType:', docType)
                                      if (docType === 'Passport')
                                        doctypeFound = true

                                      if (
                                        doctypeFound &&
                                        item.Text?.fieldList
                                      ) {
                                        for (const field of item.Text
                                          .fieldList) {
                                          if (field.lcid === 0) {
                                            mp[field.fieldName] = field.value
                                          }
                                        }
                                      }
                                    }

                                    console.log(
                                      'Extracted passport fields:',
                                      mp
                                    )

                                    if (doctypeFound) {
                                      if (mp['Given Names']) {
                                        form.setValue(
                                          'firstName2',
                                          mp['Given Names']
                                        )
                                        form.trigger('firstName2')
                                      }
                                      if (mp['Surname']) {
                                        form.setValue(
                                          'lastName2',
                                          mp['Surname']
                                        )
                                        form.trigger('lastName2')
                                      }
                                      if (mp['Document Number']) {
                                        form.setValue(
                                          'passportNumber',
                                          mp['Document Number']
                                        )
                                        form.trigger('passportNumber')
                                      }

                                      if (mp['Date of Birth']) {
                                        const dob = new Date(
                                          mp['Date of Birth']
                                        )
                                        if (!isNaN(dob.getTime())) {
                                          form.setValue('dateOfBirth', dob)
                                          form.trigger('dateOfBirth')
                                        }
                                      }

                                      if (mp['Date of Expiry']) {
                                        const expiry = new Date(
                                          mp['Date of Expiry']
                                        )
                                        if (!isNaN(expiry.getTime())) {
                                          form.setValue(
                                            'passportExpiryDate',
                                            expiry
                                          )
                                          form.trigger('passportExpiryDate')
                                        }
                                      }

                                      if (mp['Date of Issue']) {
                                        const issue = new Date(
                                          mp['Date of Issue']
                                        )
                                        if (!isNaN(issue.getTime())) {
                                          form.setValue(
                                            'passportIssueDate',
                                            issue
                                          )
                                          form.trigger('passportIssueDate')
                                        }
                                      }

                                      if (mp['Place of Issue']) {
                                        form.setValue(
                                          'placeOfIssue',
                                          mp['Place of Issue']
                                        )
                                        form.trigger('placeOfIssue')
                                      }
                                      if (mp['Place of Birth']) {
                                        form.setValue(
                                          'cityOfBirth',
                                          mp['Place of Birth']
                                        )
                                        form.trigger('cityOfBirth')
                                      }

                                      if (mp['Fathers Name']) {
                                        form.setValue(
                                          'fatherFullName',
                                          mp['Fathers Name']
                                        )
                                        form.trigger('fatherFullName')
                                      }

                                      if (mp['Mothers Name']) {
                                        form.setValue(
                                          'motherFullName',
                                          mp['Mothers Name']
                                        )
                                        form.trigger('motherFullName')
                                      }

                                      if (mp['Issuing State Name']) {
                                        const rawNationality = mp[
                                          'Issuing State Name'
                                        ]
                                          .trim()
                                          .toLowerCase()
                                        const overridden =
                                          countryNameOverrides[rawNationality]
                                        const normalizedNationality =
                                          overridden || mp['Issuing State Name']

                                        form.setValue(
                                          'countryOfIssuance',
                                          normalizedNationality
                                        )
                                        form.trigger('countryOfIssuance')
                                      }

                                      if (mp['Nationality']) {
                                        let rawNationality = mp['Nationality']
                                          .toString()
                                          .trim()
                                          .toLowerCase()

                                        rawNationality = rawNationality.replace(
                                          /\s+/g,
                                          ' '
                                        )

                                        let normalizedNationality =
                                          countryNameOverrides[
                                            rawNationality
                                          ] ||
                                          nationalityOverrides[
                                            rawNationality
                                          ] ||
                                          mp['Nationality']

                                        form.setValue(
                                          'nationality',
                                          normalizedNationality
                                        )
                                        form.trigger('nationality')
                                      }

                                      if (mp['Sex']) {
                                        const gender =
                                          mp['Sex'] === 'M'
                                            ? 'Male'
                                            : mp['Sex'] === 'F'
                                              ? 'Female'
                                              : undefined

                                        if (gender) {
                                          form.setValue('gender', gender)
                                          form.trigger('gender')
                                        }
                                      }
                                    } else {
                                      toast({
                                        title: 'Invalid Passport',
                                        description:
                                          'Please upload a clear scan of a valid passport.',
                                        variant: 'destructive',
                                      })
                                    }
                                  } else {
                                    toast({
                                      title: 'Invalid Response',
                                      description:
                                        'Unexpected data format received.',
                                      variant: 'destructive',
                                    })
                                  }
                                } catch (error) {
                                  console.error(
                                    'Error uploading passport:',
                                    error
                                  )
                                  toast({
                                    title: 'Error',
                                    description:
                                      'Could not extract data from the passport.',
                                    variant: 'destructive',
                                  })
                                } finally {
                                  setIsUploading(false)
                                }
                              }

                              reader.readAsDataURL(file)
                            }}
                          />
                          {isUploading && (
                            <div className='relative flex items-center gap-2 mt-1 text-primary dark:text-primary-dark text-sm'>
                              <svg
                                className='animate-spin h-4 w-4'
                                viewBox='0 0 24 24'
                              >
                                <circle
                                  className='opacity-25'
                                  cx='12'
                                  cy='12'
                                  r='10'
                                  stroke='#c7a51f'
                                  strokeWidth='4'
                                  fill='none'
                                />
                                <path
                                  className='opacity-75'
                                  fill='#c7a51f'
                                  d='M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z'
                                />
                              </svg>
                              <span>Extracting passport data...</span>
                            </div>
                          )}
                        </div>
                      </FormControl>
                      <FormDescription>
                        Please upload page 2 of your passport. We accept only
                        image/photo in the format of .jpg OR .jpeg
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )
                }}
              />
            </div>
          </>
        )}

        {/* Title Dropdown */}
        <div className='flex items-start space-x-2 mb-4'>
          <FormField
            control={form.control}
            name='title2'
            render={({ field }) => (
              <FormItem data-error-field='title2'>
                <FormLabel>
                  Name <span className='text-red-500'>*</span>
                </FormLabel>
                <FormControl>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <SelectTrigger className='w-[100px]'>
                      <SelectValue placeholder='Select Title' />
                    </SelectTrigger>
                    <SelectContent position='popper'>
                      <SelectItem value='Mr.'>Mr.</SelectItem>
                      <SelectItem value='Mrs.'>Mrs.</SelectItem>
                      <SelectItem value='Ms.'>Ms.</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {/* First Name Input Field */}
          <div className='w-full'>
            <FormField
              control={form.control}
              name='firstName2'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>First Name</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder='Enter First Name' />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Last Name Input Field */}
          <div className='w-full'>
            <FormField
              control={form.control}
              name='lastName2'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Last Name</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder='Enter Last Name' />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Passport Number */}
        <div className='mb-4'>
          <FormField
            control={form.control}
            name='passportNumber'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Passport Number <span className='text-red-500'>*</span>
                </FormLabel>
                <FormControl>
                  <Input {...field} placeholder='Enter Passport Number' />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Place of Issue */}

        <div className='flex flex-wrap space-between gap-y-4'>
          <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
            <FormField
              control={form.control}
              name='placeOfIssue'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Place of Issue <span className='text-red-500'>*</span>{' '}
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <span
                          className='text-yellow-600 cursor-pointer'
                          style={{ fontSize: '1.1rem', lineHeight: '1' }}
                        >
                          🛈
                        </span>
                      </TooltipTrigger>
                      <TooltipContent className='max-w-xs text-sm alert-bg-warning border border-yellow-300 rounded shadow-lg p-2 text-black'>
                        Issuing place of the passport.
                      </TooltipContent>
                    </Tooltip>
                  </FormLabel>
                  <FormControl>
                    <Input {...field} placeholder='Enter Place of Issue' />
                  </FormControl>
                  <FormDescription>
                    Issuing place of the passport
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Place of Issue (Arabic)*/}
          <div className='flex-1'>
            <FormField
              control={form.control}
              name='placeOfIssueArabic'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Place of Issue (Arabic)</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder='Enter' />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Passport Type */}
        <div className='mb-4'>
          <FormField
            control={form.control}
            name='passportType'
            render={({ field }) => (
              <FormItem data-error-field='passportType'>
                <FormLabel>
                  Passport Type <span className='text-red-500'>*</span>
                </FormLabel>
                <FormControl>
                  <Select
                    disabled={
                      form.watch('nationality') !== 'Syria' &&
                      form.watch('nationality') !== 'Lebanon'
                    }
                    value={field.value}
                    onValueChange={field.onChange}
                  >
                    <SelectTrigger
                      ref={field.ref}
                      className={
                        form.watch('nationality') !== 'Syria' &&
                        form.watch('nationality') !== 'Lebanon'
                          ? 'bg-gray-100 cursor-not-allowed'
                          : ''
                      }
                    >
                      <SelectValue placeholder='Select' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='Ordinary'>Ordinary</SelectItem>
                      {(form.watch('nationality') === 'Syria' ||
                        form.watch('nationality') === 'Lebanon') && (
                        <SelectItem value='Travel Document'>
                          Travel Document
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormDescription>
                  Please note only Ordinary Passports are accepted for Visas, it
                  is not allowed to apply for Visas on Diplomatic passports.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
          <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
            <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
          </span>
          <div className='flex flex-col ml-2'>
            <AlertTitle>
              You agree to the statement below by checking the box :
            </AlertTitle>

            <AlertDescription>
              <p className="text-sm dark:text-red-500 pl-4 relative before:content-['1.'] before:absolute before:left-0 before:top-0">
                The passport provided is a normal/ordinary passport.
              </p>
              <p className="text-sm dark:text-red-500 pl-4 relative before:content-['2.'] before:absolute before:left-0 before:top-0">
                It is not a travel document, provisional, and/or diplomatic
                passport.
              </p>
              <p className="text-sm dark:text-red-500 pl-4 relative before:content-['3.'] before:absolute before:left-0 before:top-0">
                It is the responsibility of the partner/client to ensure that
                the correct passport is provided at the time of application.
              </p>
              <p className="text-sm dark:text-red-500 pl-4 relative before:content-['4.'] before:absolute before:left-0 before:top-0">
                Any Visa(s) delayed or rejected because of not complying to the
                statements above will be the sole responsibility of the
                partner/client.{' '}
              </p>
            </AlertDescription>
            <p className="text-sm dark:text-red-500 pl-4 relative before:content-['5.'] before:absolute before:left-0 before:top-0">
              No refunds/provisions will be possible in case of non-compliance.{' '}
            </p>
          </div>
        </Alert>

        {/* Agreement to passport rules */}
        <FormField
          control={form.control}
          name='agreementPassportRules'
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Agreement to passport rules{' '}
                <span className='text-red-500'>*</span>
              </FormLabel>

              <div className='flex items-start space-x-2 border border-dashed border-yellow-400 rounded-md p-4 bg-yellow-50 dark:bg-primary/20'>
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={(checked: boolean) =>
                      field.onChange(checked)
                    }
                    className='mt-1'
                    data-field='agreementPassportRules'
                  />
                </FormControl>
                <FormLabel className='text-base text-primary dark:text-primary-light'>
                  <div>Agreed</div>
                </FormLabel>
              </div>

              <FormMessage />
            </FormItem>
          )}
        />
        {/* Country of Issuance */}
        <div className='flex-1'>
          <FormField
            control={form.control}
            name='countryOfIssuance'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Passport Country of Issue{' '}
                  <span className='text-red-500'>*</span>
                </FormLabel>
                <FormControl>
                  <CountryDropdown
                    data-field='countryOfIssuance'
                    placeholder='Country'
                    defaultValue={field.value}
                    onChange={(country) => {
                      field.onChange(country.name)
                    }}
                  />
                </FormControl>
                <FormDescription>
                  Issuing country of the passport
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className='flex flex-wrap space-between gap-y-4'>
          {/* Passport Issue Date */}
          <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
            <FormField
              control={form.control}
              name='passportIssueDate'
              render={({ field }) => (
                <FormItem data-error-field='passportIssueDate'>
                  <FormLabel>
                    Passport Issue Date <span className='text-red-500'>*</span>
                  </FormLabel>
                  <FormControl>
                    <DateTimePicker
                      key={
                        field.value
                          ? new Date(field.value).toISOString()
                          : 'empty'
                      }
                      granularity='day'
                      value={field.value ? new Date(field.value) : undefined}
                      onChange={(val) => field.onChange(val)}
                      displayFormat={{ hour24: 'dd MMMM yyyy' }}
                    />
                  </FormControl>
                  <FormDescription>dd/MM/yyyy</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Passport Expiry Date */}
          <div className='flex-1'>
            <FormField
              control={form.control}
              name='passportExpiryDate'
              render={({ field }) => (
                <FormItem data-error-field='passportExpiryDate'>
                  <FormLabel>
                    Passport Expiry Date <span className='text-red-500'>*</span>
                  </FormLabel>
                  <FormControl>
                    <DateTimePicker
                      key={
                        field.value
                          ? new Date(field.value).toISOString()
                          : 'empty'
                      }
                      granularity='day'
                      value={field.value ? new Date(field.value) : undefined}
                      onChange={(val) => field.onChange(val)}
                      displayFormat={{ hour24: 'dd MMMM yyyy' }}
                    />
                  </FormControl>

                  <FormDescription>
                    dd/MM/yyyy <br />
                    Please note that your passport should be valid for 7 months
                    plus at the time of application. The Visa stamping process
                    may not be completed otherwise.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        <div className='flex flex-wrap space-between gap-y-4'>
          {/* City of Birth */}

          <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
            {' '}
            <FormField
              control={form.control}
              name='cityOfBirth'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    City of Birth <span className='text-red-500'>*</span>
                  </FormLabel>
                  <FormControl>
                    <Input {...field} placeholder='Enter City of Birth' />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* City of Birth (Arabic) */}

          <div className='flex-1'>
            {' '}
            <FormField
              control={form.control}
              name='cityOfBirthArabic'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>City of Birth (Arabic)</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder='Enter' />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
        <div className='flex flex-wrap space-between gap-y-4'>
          {/* Country of Birth */}
          <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
            <FormField
              control={form.control}
              name='countryOfBirth'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Country of Birth <span className='text-red-500'>*</span>
                  </FormLabel>
                  <FormControl>
                    <CountryDropdown
                      data-field='countryOfBirth'
                      placeholder='Country'
                      defaultValue={field.value as string}
                      onChange={(country) => {
                        field.onChange(country.name)
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Date of Birth */}
          <div className='flex-1'>
            <FormField
              control={form.control}
              name='dateOfBirth'
              render={({ field }) => (
                <FormItem data-error-field='dateOfBirth'>
                  <FormLabel>
                    Date of Birth <span className='text-red-500'>*</span>
                  </FormLabel>
                  <FormControl>
                    <DateTimePicker
                      key={
                        field.value
                          ? new Date(field.value).toISOString()
                          : 'empty'
                      }
                      granularity='day'
                      value={field.value ? new Date(field.value) : undefined}
                      onChange={(val) => {
                        field.onChange(val)
                      }}
                      displayFormat={{
                        hour24: 'dd MMMM yyyy',
                      }}
                    />
                  </FormControl>
                  <FormDescription>dd/MM/yyyy</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        <div className='flex flex-wrap space-between gap-y-4'>
          {/* Gender */}

          <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
            {' '}
            <FormField
              control={form.control}
              name='gender'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Gender <span className='text-red-500'>*</span>
                  </FormLabel>
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger>
                      <SelectValue placeholder='Select gender' />
                    </SelectTrigger>
                    <SelectContent position='popper'>
                      <SelectItem value='Male'>Male</SelectItem>
                      <SelectItem value='Female'>Female</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Previous Nationality */}

          <div className='flex-1'>
            {' '}
            <FormField
              control={form.control}
              name='previousNationality'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Previous Nationality <span className='text-red-500'>*</span>
                  </FormLabel>
                  <FormControl>
                    <CountryDropdown
                      data-field='previousNationality'
                      placeholder='Country'
                      defaultValue={field.value}
                      onChange={(country) => {
                        field.onChange(country.name)
                      }}
                    />
                  </FormControl>
                  <FormDescription>
                    If Visa applicant does not have previous nationality, please
                    select current nationality as previous nationality.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
        <div className='flex flex-wrap space-between gap-y-4'>
          {/* Marital Status */}

          <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
            {' '}
            <FormField
              control={form.control}
              name='maritalStatus'
              render={({ field }) => (
                <FormItem data-error-field='maritalStatus'>
                  <FormLabel>
                    Marital Status <span className='text-red-500'>*</span>
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select ' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='Married'>Married</SelectItem>
                      <SelectItem value='Single'>Single</SelectItem>
                      <SelectItem value='Divorced'>Divorced</SelectItem>
                      <SelectItem value='Widowed'>Widowed</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Religion */}
          <div className='flex-1'>
            <FormField
              control={form.control}
              name='religion'
              render={({ field }) => (
                <FormItem data-error-field='religion'>
                  <FormLabel>
                    Religion <span className='text-red-500'>*</span>
                  </FormLabel>
                  <FormControl>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <SelectTrigger>
                        <SelectValue placeholder='Select Religion' />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='Islam'>Islam</SelectItem>
                        <SelectItem value='Christianity'>
                          Christianity
                        </SelectItem>
                        <SelectItem value='Hinduism'>Hinduism</SelectItem>
                        <SelectItem value='Buddhism'>Buddhism</SelectItem>
                        <SelectItem value='Sikhism'>Sikhism</SelectItem>
                        <SelectItem value='Judaism'>Judaism</SelectItem>
                        <SelectItem value='Bahaei'>Bahaei</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
        {religion === 'Islam' && (
          <>
            <div className='mb-4'>
              <FormField
                control={form.control}
                name='religionSubCategory'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Religion Sub-Category{' '}
                      <span style={{ color: 'red' }}>*</span>
                    </FormLabel>
                    <FormControl>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder='Select ' />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value='Sunni'>Sunni</SelectItem>
                          <SelectItem value='Shia'>Shia</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </>
        )}

        {/* Father's Full Name */}
        <div className='mb-4'>
          <FormField
            control={form.control}
            name='fatherFullName'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Father's Full Name <span style={{ color: 'red' }}>*</span>
                </FormLabel>
                <FormControl>
                  <Input {...field} placeholder='Enter' />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div className='flex flex-wrap space-between gap-y-4'>
          {/* Mother's Full Name */}

          <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
            <FormField
              control={form.control}
              name='motherFullName'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Mother's Full Name <span className='text-red-500'>*</span>
                  </FormLabel>
                  <FormControl>
                    <Input {...field} placeholder='Enter' />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Mother's Full Name (Arabic) */}

          <div className='flex-1'>
            <FormField
              control={form.control}
              name='motherFullNameArabic'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Mother's Full Name (Arabic)</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder='Enter' />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
      </form>
    </>
  )
}

export default Step3VisaApplication
