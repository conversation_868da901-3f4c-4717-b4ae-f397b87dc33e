// import { BASE_URL } from '@/utils/network';
// import Axios from 'axios';

// export const checkCompany = async (companyName = "") => {
//   try {
//    const response = await Axios.post(`${BASE_URL}/api/utils/company-name/validate`, {companyName: companyName})
//    return response.data;
//   } catch (error) {
//     console.error('Error checking company:', error);
//     throw error;
//   }
// }

import { BASE_URL } from '@/utils/network';
import Axios from 'axios';


export const checkCompany = async (companyName = "") => {
  try {
    const response = await Axios.post(`${BASE_URL}/api/utils/company-name/validate`, {
      companyName,
    });

    const data = response.data;

    const valid = typeof data.valid === 'boolean'
      ? data.valid
      : data.message?.toLowerCase().includes('valid') || (data.score ?? 0) > 0;

    const result = {
      valid,
      message: data.message,
      score: data.score ?? 0,
      similar: data.similar || [],
    };

    return result;
  } catch (error: any) {
    const responseData = error?.response?.data;

    const result = {
      valid: false,
      message: responseData?.message || 'Something went wrong.',
      score: responseData?.score ?? 0,
      similar: responseData?.similar || [],
    };

    return result;
  }
};
