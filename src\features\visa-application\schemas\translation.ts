export function transliterate(input: string): string {
  let base = input.toUpperCase()

  const replaceAll = (
    str: string,
    search: string,
    replacement: string
  ): string => str.split(search).join(replacement)

  // Compound replacements
  base = replaceAll(base, 'TION', 'شن')
  base = replaceAll(base, 'THE', 'ذا')
  base = replaceAll(base, 'PP', 'ب')
  base = replaceAll(base, 'CH', 'ش')
  base = replaceAll(base, 'SH', 'ش')
  base = replaceAll(base, 'TH', 'ذ')
  base = replaceAll(base, 'CK', 'ك')
  base = replaceAll(base, 'PH', 'ف')
  base = replaceAll(base, 'GH', 'غ')
  base = replaceAll(base, 'AI', 'اي')
  base = replaceAll(base, 'OU', 'او')

  // Single letter replacements
  base = replaceAll(base, 'A', 'ا')
  base = replaceAll(base, 'B', 'ب')
  base = replaceAll(base, 'C', 'ك')
  base = replaceAll(base, 'D', 'د')
  base = replaceAll(base, 'E', 'ي')
  base = replaceAll(base, 'F', 'ف')
  base = replaceAll(base, 'G', 'ج')
  base = replaceAll(base, 'H', 'ه')
  base = replaceAll(base, 'I', 'ي')
  base = replaceAll(base, 'J', 'ي')
  base = replaceAll(base, 'K', 'ك')
  base = replaceAll(base, 'L', 'ل')
  base = replaceAll(base, 'M', 'م')
  base = replaceAll(base, 'N', 'ن')
  base = replaceAll(base, 'O', 'و')
  base = replaceAll(base, 'P', 'ب')
  base = replaceAll(base, 'Q', 'ك')
  base = replaceAll(base, 'R', 'ر')
  base = replaceAll(base, 'S', 'س')
  base = replaceAll(base, 'T', 'ت')
  base = replaceAll(base, 'U', 'ا')
  base = replaceAll(base, 'V', 'ف')
  base = replaceAll(base, 'W', 'و')
  base = replaceAll(base, 'X', 'كس')
  base = replaceAll(base, 'Y', 'ي')
  base = replaceAll(base, 'Z', 'ز')

  return base
}

export const specialCharacters = new Set([
  '/',
  ')',
  '{',
  '}',
  '.',
  '!',
  '@',
  '#',
  '%',
  '^',
  '&',
  '*',
  '(',
  '_',
  ':',
  ';',
  '`',
  '[',
  ']',
  '\\',
  '|',
  'ä',
  'ö',
  'ü',
  'ß',
  "'",
  '"',
  '+',
  '=',
  '<',
  '>',
  '?',
  '؟',
  '¥',
  'á',
  'è',
  'ë',
  'ê',
  'å',
  'ò',
  '£',
  '~',
  'Ñ',
  ',',
  '¿',
  '¬',
  '«',
  '»',
  'Ç',
  'ÿ',
  'û',
  'ù',
  '$',
])

export function containsSpecialCharacters(text: string): boolean {
  return [...text].some((char) => specialCharacters.has(char))
}
export const nationalityOverrides: Record<string, string> = {
  indian: 'India',
  pakistani: 'Pakistan',
  bangladeshi: 'Bangladesh',
  emirati: 'United Arab Emirates',
  egyptian: 'Egypt',
  lebanese: 'Lebanon',
  jordanian: 'Jordan',
  palestinian: 'Palestine, state of',
  saudi: 'Saudi Arabia',
  iranian: 'Iran (islamic republic of)',
  iraqi: 'Iraq',
  afghan: 'Afghanistan',
  turkish: 'Turkey',
  british: 'United Kingdom',
  american: 'United States',
  filipino: 'Philippines',
  nepali: 'Nepal',
  sudanese: 'Sudan',
  tunisian: 'Tunisia',
  moroccan: 'Morocco',
  algerian: 'Algeria',
  somali: 'Somalia',
  ethiopian: 'Ethiopia',
  burmese: 'Myanmar',
  congolese: 'Congo, Democratic Republic of the',
}
