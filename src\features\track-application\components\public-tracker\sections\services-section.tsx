
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from '@/components/ui/carousel'

import SectionTitle from '@/features/track-application/components/public-tracker/custom-ui/section-title'
import FzCard from '@/features/track-application/components/public-tracker/custom-ui/fz-card'

import { cardServicesData } from '@/features/track-application/data/services-data'


const ServicesSection = () => {
  return (
    <section id='services' className='w-full gap-8 py-16 lg:py-24 bg-slate-100 dark:bg-neutral-900 overflow-x-hidden'>
        <div className='container w-full'>
            <SectionTitle
                title={'Taking Your Business To Greater Heights'}
                description='Take advantage of the incredible opportunities that come with locating your business in the UAE. Set up your company in Dubai and enjoy exceptional benefits with IFZA'
                className='mb-8'
                />
            
            <Carousel className="container w-full px-0">

              <CarouselContent className="-ml-1">
                {cardServicesData.map((card, index) => (
                  <CarouselItem key={index} className={`${index !== 0 ? 'pl-6' : ''} md:basis-1/2 lg:basis-1/3`}>
                      <FzCard {...card} />
                  </CarouselItem>
                ))}
              </CarouselContent>

              <CarouselPrevious />
              <CarouselNext />

            </Carousel>
        </div>
    </section>
  )
}

export default ServicesSection