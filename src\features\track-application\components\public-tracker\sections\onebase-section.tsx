import FzButton from '@/features/track-application/components/public-tracker/custom-ui/fz-button'

import oneBaseBg from '@/assets/images/public-tracker/1base-banner-bg.jpg'
import fzLogo from '@/assets/images/public-tracker/1base-logo.svg'

const OnebaseBannerSection = () => {
  return (
    <section>
        <div className="container my-24 xl:my-32">
            <div className='group relative transition-all duration-200 ease-linear px-8 md:px-14 py-12 rounded-2xl text-white flex items-center justify-center'
                style={{
                backgroundImage: `url(${oneBaseBg})`,
                backgroundRepeat: 'no-repeat',
                backgroundSize: 'cover',
                backgroundPosition: '80% 80%',
                }}>
                <div className='z-10 flex flex-col lg:flex-row lg:items-center lg:justify-between gap-8'>
                    <div className='w-full lg:w-7/12'>
                        <img src={fzLogo} alt="" />
                        <p className='capitalize mt-6 font-light text-lg'>Professional CRM setup services to help you manage relationships, increase productivity, and drive growth.</p>
                    </div>
                    
                    <FzButton link={'http://1base.io/'} label='Contact Us' light className='after:absolute after:inset-0 after:content-[""]' />
                </div>
            </div>
        </div>
    </section>
  )
}

export default OnebaseBannerSection