import { formatDateToShortMonth } from "../utils/coomonFunctions";

interface ApplicationDesc {
  step: string;
  timestamp?: string;
} 

export default function ApplicationDescription({ step, timestamp }: ApplicationDesc) {
  return (
    <div className="mb-8 h-[30px] flex flex-col justify-center align-center">
      <h4 className="text-sm font-semibold">{step}</h4>
      
      {timestamp && (
        <p className="text-xs text-gray-500">{formatDateToShortMonth(new Date(timestamp))}</p>
      )}
    </div>
  );
}