interface SectionTitleProps {
  title: string;
  description?: string;
  className?: string,
}

const SectionTitle = ({ title, description, className }: SectionTitleProps) => {
  return (
    <div className={`${className} mb-12`}>
        <h2 className='font-tt-firs-neue text-center text-3xl xl:text-4xl uppercase mb-4'>
            {title}
        </h2>
      {description && <p className="text-center text-slate-600 dark:text-neutral-400 font-light max-w-2xl mx-auto mb-8">{description}</p>}
    </div>
  );
};

export default SectionTitle;