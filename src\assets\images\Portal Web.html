<!DOCTYPE html>
<!-- saved from url=(0022)http://localhost:5173/ -->
<html lang="en" class="light"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <script type="module">import { injectIntoGlobalHook } from "/@react-refresh";
injectIntoGlobalHook(window);
window.$RefreshReg$ = () => {};
window.$RefreshSig$ = () => (type) => type;</script>

    <script type="module" src="./Portal Web_files/client"></script>

    
    <style type="text/css">:root, :host {
  --fa-font-solid: normal 900 1em/1 "Font Awesome 6 Free";
  --fa-font-regular: normal 400 1em/1 "Font Awesome 6 Free";
  --fa-font-light: normal 300 1em/1 "Font Awesome 6 Pro";
  --fa-font-thin: normal 100 1em/1 "Font Awesome 6 Pro";
  --fa-font-duotone: normal 900 1em/1 "Font Awesome 6 Duotone";
  --fa-font-brands: normal 400 1em/1 "Font Awesome 6 Brands";
  --fa-font-sharp-solid: normal 900 1em/1 "Font Awesome 6 Sharp";
  --fa-font-sharp-regular: normal 400 1em/1 "Font Awesome 6 Sharp";
  --fa-font-sharp-light: normal 300 1em/1 "Font Awesome 6 Sharp";
  --fa-font-sharp-thin: normal 100 1em/1 "Font Awesome 6 Sharp";
  --fa-font-sharp-duotone-solid: normal 900 1em/1 "Font Awesome 6 Sharp Duotone";
}

svg:not(:root).svg-inline--fa, svg:not(:host).svg-inline--fa {
  overflow: visible;
  box-sizing: content-box;
}

.svg-inline--fa {
  display: var(--fa-display, inline-block);
  height: 1em;
  overflow: visible;
  vertical-align: -0.125em;
}
.svg-inline--fa.fa-2xs {
  vertical-align: 0.1em;
}
.svg-inline--fa.fa-xs {
  vertical-align: 0em;
}
.svg-inline--fa.fa-sm {
  vertical-align: -0.0714285705em;
}
.svg-inline--fa.fa-lg {
  vertical-align: -0.2em;
}
.svg-inline--fa.fa-xl {
  vertical-align: -0.25em;
}
.svg-inline--fa.fa-2xl {
  vertical-align: -0.3125em;
}
.svg-inline--fa.fa-pull-left {
  margin-right: var(--fa-pull-margin, 0.3em);
  width: auto;
}
.svg-inline--fa.fa-pull-right {
  margin-left: var(--fa-pull-margin, 0.3em);
  width: auto;
}
.svg-inline--fa.fa-li {
  width: var(--fa-li-width, 2em);
  top: 0.25em;
}
.svg-inline--fa.fa-fw {
  width: var(--fa-fw-width, 1.25em);
}

.fa-layers svg.svg-inline--fa {
  bottom: 0;
  left: 0;
  margin: auto;
  position: absolute;
  right: 0;
  top: 0;
}

.fa-layers-counter, .fa-layers-text {
  display: inline-block;
  position: absolute;
  text-align: center;
}

.fa-layers {
  display: inline-block;
  height: 1em;
  position: relative;
  text-align: center;
  vertical-align: -0.125em;
  width: 1em;
}
.fa-layers svg.svg-inline--fa {
  transform-origin: center center;
}

.fa-layers-text {
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  transform-origin: center center;
}

.fa-layers-counter {
  background-color: var(--fa-counter-background-color, #ff253a);
  border-radius: var(--fa-counter-border-radius, 1em);
  box-sizing: border-box;
  color: var(--fa-inverse, #fff);
  line-height: var(--fa-counter-line-height, 1);
  max-width: var(--fa-counter-max-width, 5em);
  min-width: var(--fa-counter-min-width, 1.5em);
  overflow: hidden;
  padding: var(--fa-counter-padding, 0.25em 0.5em);
  right: var(--fa-right, 0);
  text-overflow: ellipsis;
  top: var(--fa-top, 0);
  transform: scale(var(--fa-counter-scale, 0.25));
  transform-origin: top right;
}

.fa-layers-bottom-right {
  bottom: var(--fa-bottom, 0);
  right: var(--fa-right, 0);
  top: auto;
  transform: scale(var(--fa-layers-scale, 0.25));
  transform-origin: bottom right;
}

.fa-layers-bottom-left {
  bottom: var(--fa-bottom, 0);
  left: var(--fa-left, 0);
  right: auto;
  top: auto;
  transform: scale(var(--fa-layers-scale, 0.25));
  transform-origin: bottom left;
}

.fa-layers-top-right {
  top: var(--fa-top, 0);
  right: var(--fa-right, 0);
  transform: scale(var(--fa-layers-scale, 0.25));
  transform-origin: top right;
}

.fa-layers-top-left {
  left: var(--fa-left, 0);
  right: auto;
  top: var(--fa-top, 0);
  transform: scale(var(--fa-layers-scale, 0.25));
  transform-origin: top left;
}

.fa-1x {
  font-size: 1em;
}

.fa-2x {
  font-size: 2em;
}

.fa-3x {
  font-size: 3em;
}

.fa-4x {
  font-size: 4em;
}

.fa-5x {
  font-size: 5em;
}

.fa-6x {
  font-size: 6em;
}

.fa-7x {
  font-size: 7em;
}

.fa-8x {
  font-size: 8em;
}

.fa-9x {
  font-size: 9em;
}

.fa-10x {
  font-size: 10em;
}

.fa-2xs {
  font-size: 0.625em;
  line-height: 0.1em;
  vertical-align: 0.225em;
}

.fa-xs {
  font-size: 0.75em;
  line-height: 0.0833333337em;
  vertical-align: 0.125em;
}

.fa-sm {
  font-size: 0.875em;
  line-height: 0.0714285718em;
  vertical-align: 0.0535714295em;
}

.fa-lg {
  font-size: 1.25em;
  line-height: 0.05em;
  vertical-align: -0.075em;
}

.fa-xl {
  font-size: 1.5em;
  line-height: 0.0416666682em;
  vertical-align: -0.125em;
}

.fa-2xl {
  font-size: 2em;
  line-height: 0.03125em;
  vertical-align: -0.1875em;
}

.fa-fw {
  text-align: center;
  width: 1.25em;
}

.fa-ul {
  list-style-type: none;
  margin-left: var(--fa-li-margin, 2.5em);
  padding-left: 0;
}
.fa-ul > li {
  position: relative;
}

.fa-li {
  left: calc(-1 * var(--fa-li-width, 2em));
  position: absolute;
  text-align: center;
  width: var(--fa-li-width, 2em);
  line-height: inherit;
}

.fa-border {
  border-color: var(--fa-border-color, #eee);
  border-radius: var(--fa-border-radius, 0.1em);
  border-style: var(--fa-border-style, solid);
  border-width: var(--fa-border-width, 0.08em);
  padding: var(--fa-border-padding, 0.2em 0.25em 0.15em);
}

.fa-pull-left {
  float: left;
  margin-right: var(--fa-pull-margin, 0.3em);
}

.fa-pull-right {
  float: right;
  margin-left: var(--fa-pull-margin, 0.3em);
}

.fa-beat {
  animation-name: fa-beat;
  animation-delay: var(--fa-animation-delay, 0s);
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 1s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, ease-in-out);
}

.fa-bounce {
  animation-name: fa-bounce;
  animation-delay: var(--fa-animation-delay, 0s);
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 1s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.28, 0.84, 0.42, 1));
}

.fa-fade {
  animation-name: fa-fade;
  animation-delay: var(--fa-animation-delay, 0s);
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 1s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));
}

.fa-beat-fade {
  animation-name: fa-beat-fade;
  animation-delay: var(--fa-animation-delay, 0s);
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 1s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));
}

.fa-flip {
  animation-name: fa-flip;
  animation-delay: var(--fa-animation-delay, 0s);
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 1s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, ease-in-out);
}

.fa-shake {
  animation-name: fa-shake;
  animation-delay: var(--fa-animation-delay, 0s);
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 1s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, linear);
}

.fa-spin {
  animation-name: fa-spin;
  animation-delay: var(--fa-animation-delay, 0s);
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 2s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, linear);
}

.fa-spin-reverse {
  --fa-animation-direction: reverse;
}

.fa-pulse,
.fa-spin-pulse {
  animation-name: fa-spin;
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 1s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, steps(8));
}

@media (prefers-reduced-motion: reduce) {
  .fa-beat,
.fa-bounce,
.fa-fade,
.fa-beat-fade,
.fa-flip,
.fa-pulse,
.fa-shake,
.fa-spin,
.fa-spin-pulse {
    animation-delay: -1ms;
    animation-duration: 1ms;
    animation-iteration-count: 1;
    transition-delay: 0s;
    transition-duration: 0s;
  }
}
@keyframes fa-beat {
  0%, 90% {
    transform: scale(1);
  }
  45% {
    transform: scale(var(--fa-beat-scale, 1.25));
  }
}
@keyframes fa-bounce {
  0% {
    transform: scale(1, 1) translateY(0);
  }
  10% {
    transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);
  }
  30% {
    transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));
  }
  50% {
    transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);
  }
  57% {
    transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));
  }
  64% {
    transform: scale(1, 1) translateY(0);
  }
  100% {
    transform: scale(1, 1) translateY(0);
  }
}
@keyframes fa-fade {
  50% {
    opacity: var(--fa-fade-opacity, 0.4);
  }
}
@keyframes fa-beat-fade {
  0%, 100% {
    opacity: var(--fa-beat-fade-opacity, 0.4);
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(var(--fa-beat-fade-scale, 1.125));
  }
}
@keyframes fa-flip {
  50% {
    transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));
  }
}
@keyframes fa-shake {
  0% {
    transform: rotate(-15deg);
  }
  4% {
    transform: rotate(15deg);
  }
  8%, 24% {
    transform: rotate(-18deg);
  }
  12%, 28% {
    transform: rotate(18deg);
  }
  16% {
    transform: rotate(-22deg);
  }
  20% {
    transform: rotate(22deg);
  }
  32% {
    transform: rotate(-12deg);
  }
  36% {
    transform: rotate(12deg);
  }
  40%, 100% {
    transform: rotate(0deg);
  }
}
@keyframes fa-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.fa-rotate-90 {
  transform: rotate(90deg);
}

.fa-rotate-180 {
  transform: rotate(180deg);
}

.fa-rotate-270 {
  transform: rotate(270deg);
}

.fa-flip-horizontal {
  transform: scale(-1, 1);
}

.fa-flip-vertical {
  transform: scale(1, -1);
}

.fa-flip-both,
.fa-flip-horizontal.fa-flip-vertical {
  transform: scale(-1, -1);
}

.fa-rotate-by {
  transform: rotate(var(--fa-rotate-angle, 0));
}

.fa-stack {
  display: inline-block;
  vertical-align: middle;
  height: 2em;
  position: relative;
  width: 2.5em;
}

.fa-stack-1x,
.fa-stack-2x {
  bottom: 0;
  left: 0;
  margin: auto;
  position: absolute;
  right: 0;
  top: 0;
  z-index: var(--fa-stack-z-index, auto);
}

.svg-inline--fa.fa-stack-1x {
  height: 1em;
  width: 1.25em;
}
.svg-inline--fa.fa-stack-2x {
  height: 2em;
  width: 2.5em;
}

.fa-inverse {
  color: var(--fa-inverse, #fff);
}

.sr-only,
.fa-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.sr-only-focusable:not(:focus),
.fa-sr-only-focusable:not(:focus) {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.svg-inline--fa .fa-primary {
  fill: var(--fa-primary-color, currentColor);
  opacity: var(--fa-primary-opacity, 1);
}

.svg-inline--fa .fa-secondary {
  fill: var(--fa-secondary-color, currentColor);
  opacity: var(--fa-secondary-opacity, 0.4);
}

.svg-inline--fa.fa-swap-opacity .fa-primary {
  opacity: var(--fa-secondary-opacity, 0.4);
}

.svg-inline--fa.fa-swap-opacity .fa-secondary {
  opacity: var(--fa-primary-opacity, 1);
}

.svg-inline--fa mask .fa-primary,
.svg-inline--fa mask .fa-secondary {
  fill: black;
}

.fad.fa-inverse,
.fa-duotone.fa-inverse {
  color: var(--fa-inverse, #fff);
}</style><link rel="icon" type="image/svg+xml" href="http://localhost:5173/images/favicon.svg">
    <link rel="icon" type="image/png" href="http://localhost:5173/images/favicon.png">
    <link rel="preconnect" href="https://fonts.googleapis.com/">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="">
    <link href="./Portal Web_files/css2" rel="stylesheet">

    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Primary Meta Tags -->
    <title>Portal Web</title>
    <meta name="title" content="Portal Web">  
    <meta name="description" content="IFZA Admin Dashboard">

    <meta name="theme-color" content="#fff">
  <style type="text/css" data-vite-dev-id="C:/Users/<USER>/Work/portal-web/src/index.css">*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*//*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}
  :root {
    --bg-body: #F8FAFC;
    --header-bg: #fff;
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: rgba(148, 163, 184, 1);
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --bg-body: #020817;
    --header-bg: #020817;
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  /* styles.css */
  html{
    font-family: "Inter", serif;
    font-optical-sizing: auto;
    font-weight: 400;
    font-style: normal;
  }

  .CollapsibleContent {
    overflow: hidden;
  }
  .CollapsibleContent[data-state='open'] {
    animation: slideDown 300ms ease-out;
  }
  .CollapsibleContent[data-state='closed'] {
    animation: slideUp 300ms ease-out;
  }

  @keyframes slideDown {
    from {
      height: 0;
    }
    to {
      height: var(--radix-collapsible-content-height);
    }
  }

  @keyframes slideUp {
    from {
      height: var(--radix-collapsible-content-height);
    }
    to {
      height: 0;
    }
  }

  /* Prevent focus zoom on mobile devices */
  @media screen and (max-width: 767px) {
    input,
    select,
    textarea {
      font-size: 16px !important;
    }
  }
  * {
  border-color: hsl(var(--border));
}
  html {
  overflow-x: hidden;
}
  body {
  min-height: 100svh;
  width: 100%;
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  background-color: var(--bg-body);
}
.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 2rem;
  padding-left: 2rem;
}
@media (min-width: 1400px) {

  .container {
    max-width: 1400px;
  }
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.pointer-events-none {
  pointer-events: none;
}
.pointer-events-auto {
  pointer-events: auto;
}
.invisible {
  visibility: hidden;
}
.fixed {
  position: fixed;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.sticky {
  position: sticky;
}
.inset-0 {
  inset: 0px;
}
.inset-x-0 {
  left: 0px;
  right: 0px;
}
.inset-y-0 {
  top: 0px;
  bottom: 0px;
}
.bottom-0 {
  bottom: 0px;
}
.left-0 {
  left: 0px;
}
.left-1 {
  left: 0.25rem;
}
.left-1\.5 {
  left: 0.375rem;
}
.left-2 {
  left: 0.5rem;
}
.left-44 {
  left: 11rem;
}
.left-6 {
  left: 1.5rem;
}
.left-\[50\%\] {
  left: 50%;
}
.left-full {
  left: 100%;
}
.right-0 {
  right: 0px;
}
.right-1 {
  right: 0.25rem;
}
.right-2 {
  right: 0.5rem;
}
.right-3 {
  right: 0.75rem;
}
.right-4 {
  right: 1rem;
}
.right-\[0\.3rem\] {
  right: 0.3rem;
}
.top-0 {
  top: 0px;
}
.top-1 {
  top: 0.25rem;
}
.top-1\.5 {
  top: 0.375rem;
}
.top-1\/2 {
  top: 50%;
}
.top-2\.5 {
  top: 0.625rem;
}
.top-3\.5 {
  top: 0.875rem;
}
.top-4 {
  top: 1rem;
}
.top-\[0\.3rem\] {
  top: 0.3rem;
}
.top-\[50\%\] {
  top: 50%;
}
.z-10 {
  z-index: 10;
}
.z-20 {
  z-index: 20;
}
.z-50 {
  z-index: 50;
}
.z-\[100\] {
  z-index: 100;
}
.z-\[999\] {
  z-index: 999;
}
.col-span-1 {
  grid-column: span 1 / span 1;
}
.col-span-2 {
  grid-column: span 2 / span 2;
}
.col-span-4 {
  grid-column: span 4 / span 4;
}
.col-start-2 {
  grid-column-start: 2;
}
.col-start-3 {
  grid-column-start: 3;
}
.row-span-2 {
  grid-row: span 2 / span 2;
}
.row-start-2 {
  grid-row-start: 2;
}
.m-auto {
  margin: auto;
}
.-mx-1 {
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}
.-mx-3 {
  margin-left: -0.75rem;
  margin-right: -0.75rem;
}
.-mx-4 {
  margin-left: -1rem;
  margin-right: -1rem;
}
.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.mx-3\.5 {
  margin-left: 0.875rem;
  margin-right: 0.875rem;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.-ml-2 {
  margin-left: -0.5rem;
}
.-ml-3 {
  margin-left: -0.75rem;
}
.-mr-1 {
  margin-right: -0.25rem;
}
.-mr-4 {
  margin-right: -1rem;
}
.mb-1 {
  margin-bottom: 0.25rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mb-8 {
  margin-bottom: 2rem;
}
.ml-2 {
  margin-left: 0.5rem;
}
.ml-3 {
  margin-left: 0.75rem;
}
.ml-auto {
  margin-left: auto;
}
.mr-1 {
  margin-right: 0.25rem;
}
.mr-2 {
  margin-right: 0.5rem;
}
.mt-1 {
  margin-top: 0.25rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-4 {
  margin-top: 1rem;
}
.mt-6 {
  margin-top: 1.5rem;
}
.mt-auto {
  margin-top: auto;
}
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.block {
  display: block;
}
.inline-block {
  display: inline-block;
}
.flex {
  display: flex;
}
.inline-flex {
  display: inline-flex;
}
.\!table {
  display: table !important;
}
.table {
  display: table;
}
.grid {
  display: grid;
}
.hidden {
  display: none;
}
.aspect-square {
  aspect-ratio: 1 / 1;
}
.size-10 {
  width: 2.5rem;
  height: 2.5rem;
}
.size-2 {
  width: 0.5rem;
  height: 0.5rem;
}
.size-4 {
  width: 1rem;
  height: 1rem;
}
.size-6 {
  width: 1.5rem;
  height: 1.5rem;
}
.size-8 {
  width: 2rem;
  height: 2rem;
}
.size-9 {
  width: 2.25rem;
  height: 2.25rem;
}
.size-\[1\.2rem\] {
  width: 1.2rem;
  height: 1.2rem;
}
.size-full {
  width: 100%;
  height: 100%;
}
.h-10 {
  height: 2.5rem;
}
.h-12 {
  height: 3rem;
}
.h-14 {
  height: 3.5rem;
}
.h-2 {
  height: 0.5rem;
}
.h-2\.5 {
  height: 0.625rem;
}
.h-24 {
  height: 6rem;
}
.h-3\.5 {
  height: 0.875rem;
}
.h-4 {
  height: 1rem;
}
.h-40 {
  height: 10rem;
}
.h-5 {
  height: 1.25rem;
}
.h-6 {
  height: 1.5rem;
}
.h-7 {
  height: 1.75rem;
}
.h-72 {
  height: 18rem;
}
.h-8 {
  height: 2rem;
}
.h-9 {
  height: 2.25rem;
}
.h-\[1px\] {
  height: 1px;
}
.h-\[26\.25rem\] {
  height: 26.25rem;
}
.h-\[var\(--radix-select-trigger-height\)\] {
  height: var(--radix-select-trigger-height);
}
.h-full {
  height: 100%;
}
.h-px {
  height: 1px;
}
.h-svh {
  height: 100svh;
}
.max-h-96 {
  max-height: 24rem;
}
.max-h-\[300px\] {
  max-height: 300px;
}
.max-h-screen {
  max-height: 100vh;
}
.min-h-0 {
  min-height: 0px;
}
.min-h-\[60px\] {
  min-height: 60px;
}
.min-h-svh {
  min-height: 100svh;
}
.w-10 {
  width: 2.5rem;
}
.w-16 {
  width: 4rem;
}
.w-2 {
  width: 0.5rem;
}
.w-2\.5 {
  width: 0.625rem;
}
.w-3\.5 {
  width: 0.875rem;
}
.w-3\/4 {
  width: 75%;
}
.w-36 {
  width: 9rem;
}
.w-4 {
  width: 1rem;
}
.w-40 {
  width: 10rem;
}
.w-5 {
  width: 1.25rem;
}
.w-56 {
  width: 14rem;
}
.w-6 {
  width: 1.5rem;
}
.w-7 {
  width: 1.75rem;
}
.w-72 {
  width: 18rem;
}
.w-8 {
  width: 2rem;
}
.w-9 {
  width: 2.25rem;
}
.w-\[--radix-dropdown-menu-trigger-width\] {
  width: var(--radix-dropdown-menu-trigger-width);
}
.w-\[--sidebar-width\] {
  width: var(--sidebar-width);
}
.w-\[100px\] {
  width: 100px;
}
.w-\[150px\] {
  width: 150px;
}
.w-\[160px\] {
  width: 160px;
}
.w-\[1px\] {
  width: 1px;
}
.w-\[200px\] {
  width: 200px;
}
.w-\[240px\] {
  width: 240px;
}
.w-\[340px\] {
  width: 340px;
}
.w-\[70px\] {
  width: 70px;
}
.w-\[80px\] {
  width: 80px;
}
.w-\[inherit\] {
  width: inherit;
}
.w-auto {
  width: auto;
}
.w-fit {
  width: -moz-fit-content;
  width: fit-content;
}
.w-full {
  width: 100%;
}
.w-max {
  width: -moz-max-content;
  width: max-content;
}
.min-w-0 {
  min-width: 0px;
}
.min-w-40 {
  min-width: 10rem;
}
.min-w-5 {
  min-width: 1.25rem;
}
.min-w-56 {
  min-width: 14rem;
}
.min-w-\[8rem\] {
  min-width: 8rem;
}
.min-w-\[var\(--radix-select-trigger-width\)\] {
  min-width: var(--radix-select-trigger-width);
}
.max-w-32 {
  max-width: 8rem;
}
.max-w-36 {
  max-width: 9rem;
}
.max-w-52 {
  max-width: 13rem;
}
.max-w-72 {
  max-width: 18rem;
}
.max-w-\[--skeleton-width\] {
  max-width: var(--skeleton-width);
}
.max-w-full {
  max-width: 100%;
}
.max-w-lg {
  max-width: 32rem;
}
.max-w-md {
  max-width: 28rem;
}
.flex-1 {
  flex: 1 1 0%;
}
.flex-none {
  flex: none;
}
.shrink-0 {
  flex-shrink: 0;
}
.flex-grow {
  flex-grow: 1;
}
.caption-bottom {
  caption-side: bottom;
}
.border-collapse {
  border-collapse: collapse;
}
.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-px {
  --tw-translate-x: -1px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-52 {
  --tw-translate-y: -13rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-\[-50\%\] {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-px {
  --tw-translate-x: 1px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-\[-50\%\] {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-\[2px\] {
  --tw-translate-y: 2px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-0 {
  --tw-rotate: 0deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-90 {
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-0 {
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-125 {
  --tw-scale-x: 1.25;
  --tw-scale-y: 1.25;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-90 {
  --tw-scale-x: .9;
  --tw-scale-y: .9;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-95 {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes pulse {

  50% {
    opacity: .5;
  }
}
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes spin {

  to {
    transform: rotate(360deg);
  }
}
.animate-spin {
  animation: spin 1s linear infinite;
}
.cursor-default {
  cursor: default;
}
.cursor-pointer {
  cursor: pointer;
}
.touch-none {
  touch-action: none;
}
.select-none {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.resize-none {
  resize: none;
}
.appearance-none {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-6 {
  grid-template-columns: repeat(6, minmax(0, 1fr));
}
.flex-row {
  flex-direction: row;
}
.flex-col {
  flex-direction: column;
}
.flex-col-reverse {
  flex-direction: column-reverse;
}
.flex-wrap {
  flex-wrap: wrap;
}
.items-start {
  align-items: flex-start;
}
.items-end {
  align-items: flex-end;
}
.items-center {
  align-items: center;
}
.justify-start {
  justify-content: flex-start;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-1 {
  gap: 0.25rem;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-3 {
  gap: 0.75rem;
}
.gap-4 {
  gap: 1rem;
}
.gap-6 {
  gap: 1.5rem;
}
.gap-8 {
  gap: 2rem;
}
.gap-x-2 {
  -moz-column-gap: 0.5rem;
       column-gap: 0.5rem;
}
.gap-x-4 {
  -moz-column-gap: 1rem;
       column-gap: 1rem;
}
.gap-y-1 {
  row-gap: 0.25rem;
}
.gap-y-2 {
  row-gap: 0.5rem;
}
.space-x-0 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0px * var(--tw-space-x-reverse));
  margin-left: calc(0px * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-0 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0px * var(--tw-space-y-reverse));
}
.space-y-0\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.125rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.125rem * var(--tw-space-y-reverse));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-1\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));
}
.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}
.self-start {
  align-self: flex-start;
}
.self-end {
  align-self: flex-end;
}
.overflow-auto {
  overflow: auto;
}
.overflow-hidden {
  overflow: hidden;
}
.\!overflow-x-auto {
  overflow-x: auto !important;
}
.overflow-x-auto {
  overflow-x: auto;
}
.overflow-y-auto {
  overflow-y: auto;
}
.overflow-x-hidden {
  overflow-x: hidden;
}
.overflow-y-hidden {
  overflow-y: hidden;
}
.scroll-smooth {
  scroll-behavior: smooth;
}
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.text-ellipsis {
  text-overflow: ellipsis;
}
.whitespace-nowrap {
  white-space: nowrap;
}
.text-wrap {
  text-wrap: wrap;
}
.text-nowrap {
  text-wrap: nowrap;
}
.break-words {
  overflow-wrap: break-word;
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-\[16px_16px_0_16px\] {
  border-radius: 16px 16px 0 16px;
}
.rounded-\[16px_16px_16px_0\] {
  border-radius: 16px 16px 16px 0;
}
.rounded-\[inherit\] {
  border-radius: inherit;
}
.rounded-full {
  border-radius: 9999px;
}
.rounded-lg {
  border-radius: var(--radius);
}
.rounded-md {
  border-radius: calc(var(--radius) - 2px);
}
.rounded-sm {
  border-radius: calc(var(--radius) - 4px);
}
.rounded-xl {
  border-radius: 0.75rem;
}
.rounded-t-md {
  border-top-left-radius: calc(var(--radius) - 2px);
  border-top-right-radius: calc(var(--radius) - 2px);
}
.rounded-tl {
  border-top-left-radius: 0.25rem;
}
.border {
  border-width: 1px;
}
.border-2 {
  border-width: 2px;
}
.border-b {
  border-bottom-width: 1px;
}
.border-l {
  border-left-width: 1px;
}
.border-r {
  border-right-width: 1px;
}
.border-t {
  border-top-width: 1px;
}
.border-dashed {
  border-style: dashed;
}
.border-blue-300 {
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));
}
.border-destructive {
  border-color: hsl(var(--destructive));
}
.border-destructive\/10 {
  border-color: hsl(var(--destructive) / 0.1);
}
.border-destructive\/50 {
  border-color: hsl(var(--destructive) / 0.5);
}
.border-input {
  border-color: hsl(var(--input));
}
.border-muted {
  border-color: hsl(var(--muted));
}
.border-neutral-300 {
  --tw-border-opacity: 1;
  border-color: rgb(212 212 212 / var(--tw-border-opacity, 1));
}
.border-primary {
  border-color: hsl(var(--primary));
}
.border-red-500 {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}
.border-sidebar-border {
  border-color: hsl(var(--sidebar-border));
}
.border-sky-300 {
  --tw-border-opacity: 1;
  border-color: rgb(125 211 252 / var(--tw-border-opacity, 1));
}
.border-teal-200 {
  --tw-border-opacity: 1;
  border-color: rgb(153 246 228 / var(--tw-border-opacity, 1));
}
.border-transparent {
  border-color: transparent;
}
.border-l-transparent {
  border-left-color: transparent;
}
.border-t-transparent {
  border-top-color: transparent;
}
.bg-\[\#ecedef\] {
  --tw-bg-opacity: 1;
  background-color: rgb(236 237 239 / var(--tw-bg-opacity, 1));
}
.bg-accent {
  background-color: hsl(var(--accent));
}
.bg-background {
  background-color: hsl(var(--background));
}
.bg-black\/80 {
  background-color: rgb(0 0 0 / 0.8);
}
.bg-blue-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}
.bg-border {
  background-color: hsl(var(--border));
}
.bg-card {
  background-color: hsl(var(--card));
}
.bg-destructive {
  background-color: hsl(var(--destructive));
}
.bg-destructive\/10 {
  background-color: hsl(var(--destructive) / 0.1);
}
.bg-inherit {
  background-color: inherit;
}
.bg-muted {
  background-color: hsl(var(--muted));
}
.bg-muted\/25 {
  background-color: hsl(var(--muted) / 0.25);
}
.bg-muted\/50 {
  background-color: hsl(var(--muted) / 0.5);
}
.bg-neutral-300\/40 {
  background-color: rgb(212 212 212 / 0.4);
}
.bg-popover {
  background-color: hsl(var(--popover));
}
.bg-primary {
  background-color: hsl(var(--primary));
}
.bg-primary-foreground {
  background-color: hsl(var(--primary-foreground));
}
.bg-primary\/10 {
  background-color: hsl(var(--primary) / 0.1);
}
.bg-primary\/85 {
  background-color: hsl(var(--primary) / 0.85);
}
.bg-secondary {
  background-color: hsl(var(--secondary));
}
.bg-sidebar {
  background-color: hsl(var(--sidebar-background));
}
.bg-sidebar-border {
  background-color: hsl(var(--sidebar-border));
}
.bg-sidebar-primary {
  background-color: hsl(var(--sidebar-primary));
}
.bg-sky-200\/40 {
  background-color: rgb(186 230 253 / 0.4);
}
.bg-slate-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(148 163 184 / var(--tw-bg-opacity, 1));
}
.bg-slate-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));
}
.bg-slate-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(2 6 23 / var(--tw-bg-opacity, 1));
}
.bg-teal-100\/30 {
  background-color: rgb(204 251 241 / 0.3);
}
.bg-transparent {
  background-color: transparent;
}
.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.bg-zinc-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(24 24 27 / var(--tw-bg-opacity, 1));
}
.fill-current {
  fill: currentColor;
}
.fill-primary {
  fill: hsl(var(--primary));
}
.stroke-destructive {
  stroke: hsl(var(--destructive));
}
.stroke-muted-foreground {
  stroke: hsl(var(--muted-foreground));
}
.stroke-slate-500 {
  stroke: #64748b;
}
.p-0 {
  padding: 0px;
}
.p-0\.5 {
  padding: 0.125rem;
}
.p-1 {
  padding: 0.25rem;
}
.p-10 {
  padding: 2.5rem;
}
.p-2 {
  padding: 0.5rem;
}
.p-3 {
  padding: 0.75rem;
}
.p-4 {
  padding: 1rem;
}
.p-6 {
  padding: 1.5rem;
}
.p-\[1px\] {
  padding: 1px;
}
.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.px-1\.5 {
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}
.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}
.py-0 {
  padding-top: 0px;
  padding-bottom: 0px;
}
.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}
.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.pb-16 {
  padding-bottom: 4rem;
}
.pb-2 {
  padding-bottom: 0.5rem;
}
.pb-3 {
  padding-bottom: 0.75rem;
}
.pb-4 {
  padding-bottom: 1rem;
}
.pl-2 {
  padding-left: 0.5rem;
}
.pl-3 {
  padding-left: 0.75rem;
}
.pl-8 {
  padding-left: 2rem;
}
.pr-1 {
  padding-right: 0.25rem;
}
.pr-2 {
  padding-right: 0.5rem;
}
.pr-4 {
  padding-right: 1rem;
}
.pr-6 {
  padding-right: 1.5rem;
}
.pr-8 {
  padding-right: 2rem;
}
.pt-0 {
  padding-top: 0px;
}
.pt-1 {
  padding-top: 0.25rem;
}
.pt-2 {
  padding-top: 0.5rem;
}
.pt-4 {
  padding-top: 1rem;
}
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.align-middle {
  vertical-align: middle;
}
.font-mono {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}
.text-\[0\.8rem\] {
  font-size: 0.8rem;
}
.text-\[10px\] {
  font-size: 10px;
}
.text-\[7rem\] {
  font-size: 7rem;
}
.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}
.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-bold {
  font-weight: 700;
}
.font-light {
  font-weight: 300;
}
.font-medium {
  font-weight: 500;
}
.font-normal {
  font-weight: 400;
}
.font-semibold {
  font-weight: 600;
}
.uppercase {
  text-transform: uppercase;
}
.lowercase {
  text-transform: lowercase;
}
.capitalize {
  text-transform: capitalize;
}
.italic {
  font-style: italic;
}
.tabular-nums {
  --tw-numeric-spacing: tabular-nums;
  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);
}
.leading-none {
  line-height: 1;
}
.leading-tight {
  line-height: 1.25;
}
.tracking-tight {
  letter-spacing: -0.025em;
}
.tracking-widest {
  letter-spacing: 0.1em;
}
.\!text-red-500 {
  --tw-text-opacity: 1 !important;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1)) !important;
}
.text-accent-foreground {
  color: hsl(var(--accent-foreground));
}
.text-card-foreground {
  color: hsl(var(--card-foreground));
}
.text-current {
  color: currentColor;
}
.text-destructive {
  color: hsl(var(--destructive));
}
.text-destructive-foreground {
  color: hsl(var(--destructive-foreground));
}
.text-foreground {
  color: hsl(var(--foreground));
}
.text-foreground\/50 {
  color: hsl(var(--foreground) / 0.5);
}
.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.text-muted-foreground {
  color: hsl(var(--muted-foreground));
}
.text-muted-foreground\/70 {
  color: hsl(var(--muted-foreground) / 0.7);
}
.text-muted-foreground\/80 {
  color: hsl(var(--muted-foreground) / 0.8);
}
.text-popover-foreground {
  color: hsl(var(--popover-foreground));
}
.text-primary {
  color: hsl(var(--primary));
}
.text-primary-foreground {
  color: hsl(var(--primary-foreground));
}
.text-primary-foreground\/75 {
  color: hsl(var(--primary-foreground) / 0.75);
}
.text-secondary-foreground {
  color: hsl(var(--secondary-foreground));
}
.text-sidebar-foreground {
  color: hsl(var(--sidebar-foreground));
}
.text-sidebar-foreground\/70 {
  color: hsl(var(--sidebar-foreground) / 0.7);
}
.text-sidebar-primary-foreground {
  color: hsl(var(--sidebar-primary-foreground));
}
.text-sky-900 {
  --tw-text-opacity: 1;
  color: rgb(12 74 110 / var(--tw-text-opacity, 1));
}
.text-teal-900 {
  --tw-text-opacity: 1;
  color: rgb(19 78 74 / var(--tw-text-opacity, 1));
}
.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.underline {
  text-decoration-line: underline;
}
.decoration-dashed {
  text-decoration-style: dashed;
}
.underline-offset-4 {
  text-underline-offset: 4px;
}
.opacity-0 {
  opacity: 0;
}
.opacity-100 {
  opacity: 1;
}
.opacity-50 {
  opacity: 0.5;
}
.opacity-60 {
  opacity: 0.6;
}
.opacity-70 {
  opacity: 0.7;
}
.opacity-90 {
  opacity: 0.9;
}
.opacity-95 {
  opacity: 0.95;
}
.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_0_0_1px_hsl\(var\(--sidebar-border\)\)\] {
  --tw-shadow: 0 0 0 1px hsl(var(--sidebar-border));
  --tw-shadow-colored: 0 0 0 1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-none {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.outline {
  outline-style: solid;
}
.ring-0 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-sidebar-ring {
  --tw-ring-color: hsl(var(--sidebar-ring));
}
.ring-offset-background {
  --tw-ring-offset-color: hsl(var(--background));
}
.drop-shadow-\[0_1px_2px_rgb\(0_0_0_\/_0\.1\)\] {
  --tw-drop-shadow: drop-shadow(0 1px 2px rgb(0 0 0 / 0.1));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-\[left\2c right\2c width\] {
  transition-property: left,right,width;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-\[margin\2c opa\] {
  transition-property: margin,opa;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-\[width\2c height\2c padding\] {
  transition-property: width,height,padding;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-\[width\] {
  transition-property: width;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-200 {
  transition-duration: 200ms;
}
.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-linear {
  transition-timing-function: linear;
}
@keyframes enter {

  from {
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}
@keyframes exit {

  to {
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}
.animate-in {
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}
.fade-in-0 {
  --tw-enter-opacity: 0;
}
.zoom-in-95 {
  --tw-enter-scale: .95;
}
.duration-200 {
  animation-duration: 200ms;
}
.ease-in-out {
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-linear {
  animation-timing-function: linear;
}
/* Hide scrollbar for Chrome, Safari and Opera */
.no-scrollbar::-webkit-scrollbar {
    display: none;
  }
/* Hide scrollbar for IE, Edge and Firefox */
.no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
.faded-bottom::after {
  pointer-events: none;
  position: absolute;
  bottom: 0px;
  left: 0px;
  display: none;
  height: 8rem;
  width: 100%;
  content: var(--tw-content);
  background-image: linear-gradient(180deg, transparent 10%, hsl(var(--background)) 70%);
}
@media (min-width: 768px) {

  .faded-bottom::after {
    content: var(--tw-content);
    display: block;
  }
}


.bg-background {
  background-color: var(--bg-body);
}
.bg-header{
  background-color: var(--header-bg);
}
.file\:border-0::file-selector-button {
  border-width: 0px;
}
.file\:bg-transparent::file-selector-button {
  background-color: transparent;
}
.file\:text-sm::file-selector-button {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.file\:font-medium::file-selector-button {
  font-weight: 500;
}
.file\:text-foreground::file-selector-button {
  color: hsl(var(--foreground));
}
.placeholder\:text-muted-foreground::-moz-placeholder {
  color: hsl(var(--muted-foreground));
}
.placeholder\:text-muted-foreground::placeholder {
  color: hsl(var(--muted-foreground));
}
.after\:absolute::after {
  content: var(--tw-content);
  position: absolute;
}
.after\:-inset-2::after {
  content: var(--tw-content);
  inset: -0.5rem;
}
.after\:inset-y-0::after {
  content: var(--tw-content);
  top: 0px;
  bottom: 0px;
}
.after\:left-1\/2::after {
  content: var(--tw-content);
  left: 50%;
}
.after\:w-\[2px\]::after {
  content: var(--tw-content);
  width: 2px;
}
.focus-within\:relative:focus-within {
  position: relative;
}
.focus-within\:z-20:focus-within {
  z-index: 20;
}
.focus-within\:outline-none:focus-within {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus-within\:ring-1:focus-within {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus-within\:ring-ring:focus-within {
  --tw-ring-color: hsl(var(--ring));
}
.hover\:border-accent:hover {
  border-color: hsl(var(--accent));
}
.hover\:bg-accent:hover {
  background-color: hsl(var(--accent));
}
.hover\:bg-blue-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}
.hover\:bg-destructive\/80:hover {
  background-color: hsl(var(--destructive) / 0.8);
}
.hover\:bg-destructive\/90:hover {
  background-color: hsl(var(--destructive) / 0.9);
}
.hover\:bg-muted:hover {
  background-color: hsl(var(--muted));
}
.hover\:bg-muted\/50:hover {
  background-color: hsl(var(--muted) / 0.5);
}
.hover\:bg-primary:hover {
  background-color: hsl(var(--primary));
}
.hover\:bg-primary\/80:hover {
  background-color: hsl(var(--primary) / 0.8);
}
.hover\:bg-primary\/90:hover {
  background-color: hsl(var(--primary) / 0.9);
}
.hover\:bg-secondary:hover {
  background-color: hsl(var(--secondary));
}
.hover\:bg-secondary\/75:hover {
  background-color: hsl(var(--secondary) / 0.75);
}
.hover\:bg-secondary\/80:hover {
  background-color: hsl(var(--secondary) / 0.8);
}
.hover\:bg-sidebar-accent:hover {
  background-color: hsl(var(--sidebar-accent));
}
.hover\:bg-transparent:hover {
  background-color: transparent;
}
.hover\:text-accent-foreground:hover {
  color: hsl(var(--accent-foreground));
}
.hover\:text-foreground:hover {
  color: hsl(var(--foreground));
}
.hover\:text-primary:hover {
  color: hsl(var(--primary));
}
.hover\:text-primary-foreground:hover {
  color: hsl(var(--primary-foreground));
}
.hover\:text-sidebar-accent-foreground:hover {
  color: hsl(var(--sidebar-accent-foreground));
}
.hover\:underline:hover {
  text-decoration-line: underline;
}
.hover\:decoration-solid:hover {
  text-decoration-style: solid;
}
.hover\:opacity-100:hover {
  opacity: 1;
}
.hover\:opacity-75:hover {
  opacity: 0.75;
}
.hover\:shadow-\[0_0_0_1px_hsl\(var\(--sidebar-accent\)\)\]:hover {
  --tw-shadow: 0 0 0 1px hsl(var(--sidebar-accent));
  --tw-shadow-colored: 0 0 0 1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-md:hover {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:after\:bg-sidebar-border:hover::after {
  content: var(--tw-content);
  background-color: hsl(var(--sidebar-border));
}
.focus\:translate-y-3:focus {
  --tw-translate-y: 0.75rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.focus\:transform:focus {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.focus\:bg-accent:focus {
  background-color: hsl(var(--accent));
}
.focus\:bg-primary:focus {
  background-color: hsl(var(--primary));
}
.focus\:text-accent-foreground:focus {
  color: hsl(var(--accent-foreground));
}
.focus\:text-primary-foreground:focus {
  color: hsl(var(--primary-foreground));
}
.focus\:opacity-100:focus {
  opacity: 1;
}
.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus\:ring-1:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-ring:focus {
  --tw-ring-color: hsl(var(--ring));
}
.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}
.focus-visible\:outline-none:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus-visible\:ring-1:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus-visible\:ring-2:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus-visible\:ring-ring:focus-visible {
  --tw-ring-color: hsl(var(--ring));
}
.focus-visible\:ring-sidebar-ring:focus-visible {
  --tw-ring-color: hsl(var(--sidebar-ring));
}
.focus-visible\:ring-offset-2:focus-visible {
  --tw-ring-offset-width: 2px;
}
.focus-visible\:ring-offset-background:focus-visible {
  --tw-ring-offset-color: hsl(var(--background));
}
.active\:bg-sidebar-accent:active {
  background-color: hsl(var(--sidebar-accent));
}
.active\:text-sidebar-accent-foreground:active {
  color: hsl(var(--sidebar-accent-foreground));
}
.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}
.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}
.disabled\:opacity-50:disabled {
  opacity: 0.5;
}
.group\/menu-item:focus-within .group-focus-within\/menu-item\:opacity-100 {
  opacity: 1;
}
.group\/row:hover .group-hover\/row\:bg-muted {
  background-color: hsl(var(--muted));
}
.group\/menu-item:hover .group-hover\/menu-item\:opacity-100 {
  opacity: 1;
}
.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}
.group.destructive .group-\[\.destructive\]\:border-muted\/40 {
  border-color: hsl(var(--muted) / 0.4);
}
.group.destructive .group-\[\.destructive\]\:text-red-300 {
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}
.group.destructive .group-\[\.destructive\]\:hover\:border-destructive\/30:hover {
  border-color: hsl(var(--destructive) / 0.3);
}
.group.destructive .group-\[\.destructive\]\:hover\:bg-destructive:hover {
  background-color: hsl(var(--destructive));
}
.group.destructive .group-\[\.destructive\]\:hover\:text-destructive-foreground:hover {
  color: hsl(var(--destructive-foreground));
}
.group.destructive .group-\[\.destructive\]\:hover\:text-red-50:hover {
  --tw-text-opacity: 1;
  color: rgb(254 242 242 / var(--tw-text-opacity, 1));
}
.group.destructive .group-\[\.destructive\]\:focus\:ring-destructive:focus {
  --tw-ring-color: hsl(var(--destructive));
}
.group.destructive .group-\[\.destructive\]\:focus\:ring-red-400:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(248 113 113 / var(--tw-ring-opacity, 1));
}
.group.destructive .group-\[\.destructive\]\:focus\:ring-offset-red-600:focus {
  --tw-ring-offset-color: #dc2626;
}
.peer\/menu-button:hover ~ .peer-hover\/menu-button\:text-sidebar-accent-foreground {
  color: hsl(var(--sidebar-accent-foreground));
}
.peer:disabled ~ .peer-disabled\:cursor-not-allowed {
  cursor: not-allowed;
}
.peer:disabled ~ .peer-disabled\:opacity-70 {
  opacity: 0.7;
}
.peer\/header.header-fixed ~ .peer-\[\.header-fixed\]\/header\:mt-16 {
  margin-top: 4rem;
}
.has-\[\[data-variant\=inset\]\]\:bg-sidebar:has([data-variant=inset]) {
  background-color: hsl(var(--sidebar-background));
}
.group\/menu-item:has([data-sidebar=menu-action]) .group-has-\[\[data-sidebar\=menu-action\]\]\/menu-item\:pr-8 {
  padding-right: 2rem;
}
.aria-disabled\:pointer-events-none[aria-disabled="true"] {
  pointer-events: none;
}
.aria-disabled\:opacity-50[aria-disabled="true"] {
  opacity: 0.5;
}
.aria-selected\:bg-accent[aria-selected="true"] {
  background-color: hsl(var(--accent));
}
.aria-selected\:bg-accent\/50[aria-selected="true"] {
  background-color: hsl(var(--accent) / 0.5);
}
.aria-selected\:text-accent-foreground[aria-selected="true"] {
  color: hsl(var(--accent-foreground));
}
.aria-selected\:text-muted-foreground[aria-selected="true"] {
  color: hsl(var(--muted-foreground));
}
.aria-selected\:opacity-100[aria-selected="true"] {
  opacity: 1;
}
.data-\[disabled\=true\]\:pointer-events-none[data-disabled="true"] {
  pointer-events: none;
}
.data-\[disabled\]\:pointer-events-none[data-disabled] {
  pointer-events: none;
}
.data-\[side\=bottom\]\:translate-y-1[data-side="bottom"] {
  --tw-translate-y: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[side\=left\]\:-translate-x-1[data-side="left"] {
  --tw-translate-x: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[side\=right\]\:translate-x-1[data-side="right"] {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[side\=top\]\:-translate-y-1[data-side="top"] {
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[state\=checked\]\:translate-x-4[data-state="checked"] {
  --tw-translate-x: 1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[state\=unchecked\]\:translate-x-0[data-state="unchecked"] {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[swipe\=cancel\]\:translate-x-0[data-swipe="cancel"] {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[swipe\=end\]\:translate-x-\[var\(--radix-toast-swipe-end-x\)\][data-swipe="end"] {
  --tw-translate-x: var(--radix-toast-swipe-end-x);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[swipe\=move\]\:translate-x-\[var\(--radix-toast-swipe-move-x\)\][data-swipe="move"] {
  --tw-translate-x: var(--radix-toast-swipe-move-x);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[active\=true\]\:bg-sidebar-accent[data-active="true"] {
  background-color: hsl(var(--sidebar-accent));
}
.data-\[selected\=true\]\:bg-accent[data-selected="true"] {
  background-color: hsl(var(--accent));
}
.data-\[state\=active\]\:bg-background[data-state="active"] {
  background-color: hsl(var(--background));
}
.data-\[state\=checked\]\:bg-primary[data-state="checked"] {
  background-color: hsl(var(--primary));
}
.data-\[state\=open\]\:bg-accent[data-state="open"] {
  background-color: hsl(var(--accent));
}
.data-\[state\=open\]\:bg-muted[data-state="open"] {
  background-color: hsl(var(--muted));
}
.data-\[state\=open\]\:bg-secondary[data-state="open"] {
  background-color: hsl(var(--secondary));
}
.data-\[state\=open\]\:bg-sidebar-accent[data-state="open"] {
  background-color: hsl(var(--sidebar-accent));
}
.data-\[state\=selected\]\:bg-muted[data-state="selected"] {
  background-color: hsl(var(--muted));
}
.data-\[state\=unchecked\]\:bg-input[data-state="unchecked"] {
  background-color: hsl(var(--input));
}
.data-\[active\=true\]\:font-medium[data-active="true"] {
  font-weight: 500;
}
.data-\[active\=true\]\:text-sidebar-accent-foreground[data-active="true"] {
  color: hsl(var(--sidebar-accent-foreground));
}
.data-\[selected\=true\]\:text-accent-foreground[data-selected="true"] {
  color: hsl(var(--accent-foreground));
}
.data-\[state\=active\]\:text-foreground[data-state="active"] {
  color: hsl(var(--foreground));
}
.data-\[state\=checked\]\:text-primary-foreground[data-state="checked"] {
  color: hsl(var(--primary-foreground));
}
.data-\[state\=open\]\:text-muted-foreground[data-state="open"] {
  color: hsl(var(--muted-foreground));
}
.data-\[state\=open\]\:text-sidebar-accent-foreground[data-state="open"] {
  color: hsl(var(--sidebar-accent-foreground));
}
.data-\[disabled\=true\]\:opacity-50[data-disabled="true"] {
  opacity: 0.5;
}
.data-\[disabled\]\:opacity-50[data-disabled] {
  opacity: 0.5;
}
.data-\[state\=open\]\:opacity-100[data-state="open"] {
  opacity: 1;
}
.data-\[state\=active\]\:shadow[data-state="active"] {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.data-\[swipe\=move\]\:transition-none[data-swipe="move"] {
  transition-property: none;
}
.data-\[state\=closed\]\:duration-300[data-state="closed"] {
  transition-duration: 300ms;
}
.data-\[state\=open\]\:duration-500[data-state="open"] {
  transition-duration: 500ms;
}
.data-\[state\=open\]\:animate-in[data-state="open"] {
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}
.data-\[state\=closed\]\:animate-out[data-state="closed"] {
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}
.data-\[swipe\=end\]\:animate-out[data-swipe="end"] {
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}
.data-\[state\=closed\]\:fade-out-0[data-state="closed"] {
  --tw-exit-opacity: 0;
}
.data-\[state\=closed\]\:fade-out-80[data-state="closed"] {
  --tw-exit-opacity: 0.8;
}
.data-\[state\=open\]\:fade-in-0[data-state="open"] {
  --tw-enter-opacity: 0;
}
.data-\[state\=closed\]\:zoom-out-95[data-state="closed"] {
  --tw-exit-scale: .95;
}
.data-\[state\=open\]\:zoom-in-95[data-state="open"] {
  --tw-enter-scale: .95;
}
.data-\[side\=bottom\]\:slide-in-from-top-2[data-side="bottom"] {
  --tw-enter-translate-y: -0.5rem;
}
.data-\[side\=left\]\:slide-in-from-right-2[data-side="left"] {
  --tw-enter-translate-x: 0.5rem;
}
.data-\[side\=right\]\:slide-in-from-left-2[data-side="right"] {
  --tw-enter-translate-x: -0.5rem;
}
.data-\[side\=top\]\:slide-in-from-bottom-2[data-side="top"] {
  --tw-enter-translate-y: 0.5rem;
}
.data-\[state\=closed\]\:slide-out-to-bottom[data-state="closed"] {
  --tw-exit-translate-y: 100%;
}
.data-\[state\=closed\]\:slide-out-to-left[data-state="closed"] {
  --tw-exit-translate-x: -100%;
}
.data-\[state\=closed\]\:slide-out-to-left-1\/2[data-state="closed"] {
  --tw-exit-translate-x: -50%;
}
.data-\[state\=closed\]\:slide-out-to-right[data-state="closed"] {
  --tw-exit-translate-x: 100%;
}
.data-\[state\=closed\]\:slide-out-to-right-full[data-state="closed"] {
  --tw-exit-translate-x: 100%;
}
.data-\[state\=closed\]\:slide-out-to-top[data-state="closed"] {
  --tw-exit-translate-y: -100%;
}
.data-\[state\=closed\]\:slide-out-to-top-\[48\%\][data-state="closed"] {
  --tw-exit-translate-y: -48%;
}
.data-\[state\=open\]\:slide-in-from-bottom[data-state="open"] {
  --tw-enter-translate-y: 100%;
}
.data-\[state\=open\]\:slide-in-from-left[data-state="open"] {
  --tw-enter-translate-x: -100%;
}
.data-\[state\=open\]\:slide-in-from-left-1\/2[data-state="open"] {
  --tw-enter-translate-x: -50%;
}
.data-\[state\=open\]\:slide-in-from-right[data-state="open"] {
  --tw-enter-translate-x: 100%;
}
.data-\[state\=open\]\:slide-in-from-top[data-state="open"] {
  --tw-enter-translate-y: -100%;
}
.data-\[state\=open\]\:slide-in-from-top-\[48\%\][data-state="open"] {
  --tw-enter-translate-y: -48%;
}
.data-\[state\=open\]\:slide-in-from-top-full[data-state="open"] {
  --tw-enter-translate-y: -100%;
}
.data-\[state\=closed\]\:duration-300[data-state="closed"] {
  animation-duration: 300ms;
}
.data-\[state\=open\]\:duration-500[data-state="open"] {
  animation-duration: 500ms;
}
.data-\[state\=open\]\:hover\:bg-sidebar-accent:hover[data-state="open"] {
  background-color: hsl(var(--sidebar-accent));
}
.data-\[state\=open\]\:hover\:text-sidebar-accent-foreground:hover[data-state="open"] {
  color: hsl(var(--sidebar-accent-foreground));
}
.group[data-collapsible="offcanvas"] .group-data-\[collapsible\=offcanvas\]\:left-\[calc\(var\(--sidebar-width\)\*-1\)\] {
  left: calc(var(--sidebar-width) * -1);
}
.group[data-collapsible="offcanvas"] .group-data-\[collapsible\=offcanvas\]\:right-\[calc\(var\(--sidebar-width\)\*-1\)\] {
  right: calc(var(--sidebar-width) * -1);
}
.group[data-side="left"] .group-data-\[side\=left\]\:-right-4 {
  right: -1rem;
}
.group[data-side="right"] .group-data-\[side\=right\]\:left-0 {
  left: 0px;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:-mt-8 {
  margin-top: -2rem;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:hidden {
  display: none;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:\!size-8 {
  width: 2rem !important;
  height: 2rem !important;
}
.group\/body[data-scroll-locked="1"] .group-data-\[scroll-locked\=1\]\/body\:h-full {
  height: 100%;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:w-\[--sidebar-width-icon\] {
  width: var(--sidebar-width-icon);
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)_\+_theme\(spacing\.4\)\)\] {
  width: calc(var(--sidebar-width-icon) + 1rem);
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)_\+_theme\(spacing\.4\)_\+2px\)\] {
  width: calc(var(--sidebar-width-icon) + 1rem + 2px);
}
.group[data-collapsible="offcanvas"] .group-data-\[collapsible\=offcanvas\]\:w-0 {
  width: 0px;
}
.group[data-collapsible="offcanvas"] .group-data-\[collapsible\=offcanvas\]\:translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group[data-side="right"] .group-data-\[side\=right\]\:rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group\/collapsible[data-state="open"] .group-data-\[state\=open\]\/collapsible\:rotate-90 {
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:overflow-hidden {
  overflow: hidden;
}
.group[data-variant="floating"] .group-data-\[variant\=floating\]\:rounded-lg {
  border-radius: var(--radius);
}
.group[data-variant="floating"] .group-data-\[variant\=floating\]\:border {
  border-width: 1px;
}
.group[data-side="left"] .group-data-\[side\=left\]\:border-r {
  border-right-width: 1px;
}
.group[data-side="right"] .group-data-\[side\=right\]\:border-l {
  border-left-width: 1px;
}
.group[data-variant="floating"] .group-data-\[variant\=floating\]\:border-sidebar-border {
  border-color: hsl(var(--sidebar-border));
}
.group\/row[data-state="selected"] .group-data-\[state\=selected\]\/row\:bg-muted {
  background-color: hsl(var(--muted));
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:\!p-0 {
  padding: 0px !important;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:\!p-2 {
  padding: 0.5rem !important;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:opacity-0 {
  opacity: 0;
}
.group[data-variant="floating"] .group-data-\[variant\=floating\]\:shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.group[data-collapsible="offcanvas"] .group-data-\[collapsible\=offcanvas\]\:after\:left-full::after {
  content: var(--tw-content);
  left: 100%;
}
.group[data-collapsible="offcanvas"] .group-data-\[collapsible\=offcanvas\]\:hover\:bg-sidebar:hover {
  background-color: hsl(var(--sidebar-background));
}
.group\/body[data-scroll-locked="1"] .group-data-\[scroll-locked\=1\]\/body\:has-\[main\.fixed-main\]\:h-svh:has(main.fixed-main) {
  height: 100svh;
}
.peer\/menu-button[data-size="default"] ~ .peer-data-\[size\=default\]\/menu-button\:top-1\.5 {
  top: 0.375rem;
}
.peer\/menu-button[data-size="lg"] ~ .peer-data-\[size\=lg\]\/menu-button\:top-2\.5 {
  top: 0.625rem;
}
.peer\/menu-button[data-size="sm"] ~ .peer-data-\[size\=sm\]\/menu-button\:top-1 {
  top: 0.25rem;
}
.peer[data-variant="inset"] ~ .peer-data-\[variant\=inset\]\:min-h-\[calc\(100svh-theme\(spacing\.4\)\)\] {
  min-height: calc(100svh - 1rem);
}
.peer[data-state="collapsed"] ~ .peer-data-\[state\=collapsed\]\:w-\[calc\(100\%-var\(--sidebar-width-icon\)-1rem\)\] {
  width: calc(100% - var(--sidebar-width-icon) - 1rem);
}
.peer[data-state="expanded"] ~ .peer-data-\[state\=expanded\]\:w-\[calc\(100\%-var\(--sidebar-width\)\)\] {
  width: calc(100% - var(--sidebar-width));
}
.peer\/menu-button[data-active="true"] ~ .peer-data-\[active\=true\]\/menu-button\:text-sidebar-accent-foreground {
  color: hsl(var(--sidebar-accent-foreground));
}
.dark\:-rotate-90:is(.dark *) {
  --tw-rotate: -90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.dark\:rotate-0:is(.dark *) {
  --tw-rotate: 0deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.dark\:scale-0:is(.dark *) {
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.dark\:scale-100:is(.dark *) {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.dark\:border-r:is(.dark *) {
  border-right-width: 1px;
}
.dark\:border-blue-700:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(29 78 216 / var(--tw-border-opacity, 1));
}
.dark\:border-destructive:is(.dark *) {
  border-color: hsl(var(--destructive));
}
.dark\:bg-blue-950:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(23 37 84 / var(--tw-bg-opacity, 1));
}
.dark\:bg-destructive\/50:is(.dark *) {
  background-color: hsl(var(--destructive) / 0.5);
}
.dark\:text-primary:is(.dark *) {
  color: hsl(var(--primary));
}
.dark\:text-sky-100:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(224 242 254 / var(--tw-text-opacity, 1));
}
.dark\:text-teal-200:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(153 246 228 / var(--tw-text-opacity, 1));
}
.dark\:drop-shadow-\[0_1px_2px_rgb\(255_255_255_\/_0\.1\)\]:is(.dark *) {
  --tw-drop-shadow: drop-shadow(0 1px 2px rgb(255 255 255 / 0.1));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.dark\:hover\:bg-blue-900:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(30 58 138 / var(--tw-bg-opacity, 1));
}
@media (min-width: 640px) {

  .sm\:static {
    position: static;
  }

  .sm\:bottom-0 {
    bottom: 0px;
  }

  .sm\:right-0 {
    right: 0px;
  }

  .sm\:top-auto {
    top: auto;
  }

  .sm\:z-auto {
    z-index: auto;
  }

  .sm\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .sm\:my-0 {
    margin-top: 0px;
    margin-bottom: 0px;
  }

  .sm\:my-4 {
    margin-top: 1rem;
    margin-bottom: 1rem;
  }

  .sm\:mt-0 {
    margin-top: 0px;
  }

  .sm\:block {
    display: block;
  }

  .sm\:flex {
    display: flex;
  }

  .sm\:inline-flex {
    display: inline-flex;
  }

  .sm\:hidden {
    display: none;
  }

  .sm\:size-5 {
    width: 1.25rem;
    height: 1.25rem;
  }

  .sm\:h-8 {
    height: 2rem;
  }

  .sm\:w-4 {
    width: 1rem;
  }

  .sm\:w-48 {
    width: 12rem;
  }

  .sm\:w-56 {
    width: 14rem;
  }

  .sm\:w-\[350px\] {
    width: 350px;
  }

  .sm\:w-\[480px\] {
    width: 480px;
  }

  .sm\:max-w-72 {
    max-width: 18rem;
  }

  .sm\:max-w-lg {
    max-width: 32rem;
  }

  .sm\:max-w-md {
    max-width: 28rem;
  }

  .sm\:max-w-sm {
    max-width: 24rem;
  }

  .sm\:scale-100 {
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:flex-col {
    flex-direction: column;
  }

  .sm\:items-center {
    align-items: center;
  }

  .sm\:justify-end {
    justify-content: flex-end;
  }

  .sm\:gap-0 {
    gap: 0px;
  }

  .sm\:gap-4 {
    gap: 1rem;
  }

  .sm\:space-x-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.5rem * var(--tw-space-x-reverse));
    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-x-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1.5rem * var(--tw-space-x-reverse));
    margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .sm\:rounded-lg {
    border-radius: var(--radius);
  }

  .sm\:bg-muted {
    background-color: hsl(var(--muted));
  }

  .sm\:p-0 {
    padding: 0px;
  }

  .sm\:pr-12 {
    padding-right: 3rem;
  }

  .sm\:text-left {
    text-align: left;
  }

  .sm\:shadow-none {
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }

  .data-\[state\=open\]\:sm\:slide-in-from-bottom-full[data-state="open"] {
    --tw-enter-translate-y: 100%;
  }
}
@media (min-width: 768px) {

  .md\:block {
    display: block;
  }

  .md\:flex {
    display: flex;
  }

  .md\:table-cell {
    display: table-cell;
  }

  .md\:hidden {
    display: none;
  }

  .md\:w-40 {
    width: 10rem;
  }

  .md\:max-w-\[31rem\] {
    max-width: 31rem;
  }

  .md\:max-w-\[420px\] {
    max-width: 420px;
  }

  .md\:flex-none {
    flex: none;
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:space-y-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
  }

  .md\:pb-16 {
    padding-bottom: 4rem;
  }

  .md\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .md\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .md\:opacity-0 {
    opacity: 0;
  }

  .after\:md\:hidden::after {
    content: var(--tw-content);
    display: none;
  }

  .peer[data-variant="inset"] ~ .md\:peer-data-\[variant\=inset\]\:m-2 {
    margin: 0.5rem;
  }

  .peer[data-state="collapsed"][data-variant="inset"] ~ .md\:peer-data-\[state\=collapsed\]\:peer-data-\[variant\=inset\]\:ml-2 {
    margin-left: 0.5rem;
  }

  .peer[data-variant="inset"] ~ .md\:peer-data-\[variant\=inset\]\:ml-0 {
    margin-left: 0px;
  }

  .peer[data-variant="inset"] ~ .md\:peer-data-\[variant\=inset\]\:rounded-xl {
    border-radius: 0.75rem;
  }

  .peer[data-variant="inset"] ~ .md\:peer-data-\[variant\=inset\]\:shadow {
    --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
}
@media (min-width: 1024px) {

  .lg\:sticky {
    position: sticky;
  }

  .lg\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .lg\:col-span-4 {
    grid-column: span 4 / span 4;
  }

  .lg\:my-6 {
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .lg\:flex {
    display: flex;
  }

  .lg\:inline-flex {
    display: inline-flex;
  }

  .lg\:hidden {
    display: none;
  }

  .lg\:size-10 {
    width: 2.5rem;
    height: 2.5rem;
  }

  .lg\:size-11 {
    width: 2.75rem;
    height: 2.75rem;
  }

  .lg\:h-10 {
    height: 2.5rem;
  }

  .lg\:w-1\/5 {
    width: 20%;
  }

  .lg\:w-56 {
    width: 14rem;
  }

  .lg\:w-6 {
    width: 1.5rem;
  }

  .lg\:w-72 {
    width: 18rem;
  }

  .lg\:w-\[250px\] {
    width: 250px;
  }

  .lg\:max-w-none {
    max-width: none;
  }

  .lg\:max-w-xl {
    max-width: 36rem;
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:grid-cols-7 {
    grid-template-columns: repeat(7, minmax(0, 1fr));
  }

  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:flex-col {
    flex-direction: column;
  }

  .lg\:gap-2 {
    gap: 0.5rem;
  }

  .lg\:gap-4 {
    gap: 1rem;
  }

  .lg\:space-x-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0px * var(--tw-space-x-reverse));
    margin-left: calc(0px * calc(1 - var(--tw-space-x-reverse)));
  }

  .lg\:space-x-12 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(3rem * var(--tw-space-x-reverse));
    margin-left: calc(3rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .lg\:space-x-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1.5rem * var(--tw-space-x-reverse));
    margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .lg\:space-x-8 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(2rem * var(--tw-space-x-reverse));
    margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .lg\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .lg\:space-y-1 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
  }

  .lg\:p-8 {
    padding: 2rem;
  }

  .lg\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .lg\:px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .lg\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .lg\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .lg\:drop-shadow-none {
    --tw-drop-shadow: drop-shadow(0 0 #0000);
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
  }
}
@media (min-width: 1280px) {

  .xl\:w-64 {
    width: 16rem;
  }
}
@media (min-width: 1536px) {

  .\32xl\:w-80 {
    width: 20rem;
  }
}
.\[\&\+div\]\:text-xs+div {
  font-size: 0.75rem;
  line-height: 1rem;
}
.\[\&\:has\(\>\.day-range-end\)\]\:rounded-r-md:has(>.day-range-end) {
  border-top-right-radius: calc(var(--radius) - 2px);
  border-bottom-right-radius: calc(var(--radius) - 2px);
}
.\[\&\:has\(\>\.day-range-start\)\]\:rounded-l-md:has(>.day-range-start) {
  border-top-left-radius: calc(var(--radius) - 2px);
  border-bottom-left-radius: calc(var(--radius) - 2px);
}
.\[\&\:has\(\[aria-selected\]\)\]\:rounded-md:has([aria-selected]) {
  border-radius: calc(var(--radius) - 2px);
}
.\[\&\:has\(\[aria-selected\]\)\]\:bg-accent:has([aria-selected]) {
  background-color: hsl(var(--accent));
}
.first\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-l-md:has([aria-selected]):first-child {
  border-top-left-radius: calc(var(--radius) - 2px);
  border-bottom-left-radius: calc(var(--radius) - 2px);
}
.last\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-r-md:has([aria-selected]):last-child {
  border-top-right-radius: calc(var(--radius) - 2px);
  border-bottom-right-radius: calc(var(--radius) - 2px);
}
.\[\&\:has\(\[aria-selected\]\.day-outside\)\]\:bg-accent\/50:has([aria-selected].day-outside) {
  background-color: hsl(var(--accent) / 0.5);
}
.\[\&\:has\(\[aria-selected\]\.day-range-end\)\]\:rounded-r-md:has([aria-selected].day-range-end) {
  border-top-right-radius: calc(var(--radius) - 2px);
  border-bottom-right-radius: calc(var(--radius) - 2px);
}
.\[\&\:has\(\[data-state\=checked\]\)\>div\]\:border-primary:has([data-state=checked])>div {
  border-color: hsl(var(--primary));
}
.\[\&\:has\(\[role\=checkbox\]\)\]\:pr-0:has([role=checkbox]) {
  padding-right: 0px;
}
.\[\&\>\[role\=checkbox\]\]\:translate-y-\[2px\]>[role=checkbox] {
  --tw-translate-y: 2px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.\[\&\>button\]\:hidden>button {
  display: none;
}
.\[\&\>span\:last-child\]\:truncate>span:last-child {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.\[\&\>span\]\:line-clamp-1>span {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.\[\&\>svg\+div\]\:translate-y-\[-3px\]>svg+div {
  --tw-translate-y: -3px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.\[\&\>svg\]\:absolute>svg {
  position: absolute;
}
.\[\&\>svg\]\:left-4>svg {
  left: 1rem;
}
.\[\&\>svg\]\:top-4>svg {
  top: 1rem;
}
.\[\&\>svg\]\:size-4>svg {
  width: 1rem;
  height: 1rem;
}
.\[\&\>svg\]\:shrink-0>svg {
  flex-shrink: 0;
}
.\[\&\>svg\]\:text-destructive>svg {
  color: hsl(var(--destructive));
}
.\[\&\>svg\]\:text-foreground>svg {
  color: hsl(var(--foreground));
}
.\[\&\>svg\]\:text-sidebar-accent-foreground>svg {
  color: hsl(var(--sidebar-accent-foreground));
}
.\[\&\>svg\~\*\]\:pl-7>svg~* {
  padding-left: 1.75rem;
}
.\[\&\>tr\]\:last\:border-b-0:last-child>tr {
  border-bottom-width: 0px;
}
.\[\&_\[cmdk-group-heading\]\]\:px-2 [cmdk-group-heading] {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.\[\&_\[cmdk-group-heading\]\]\:py-1\.5 [cmdk-group-heading] {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.\[\&_\[cmdk-group-heading\]\]\:text-xs [cmdk-group-heading] {
  font-size: 0.75rem;
  line-height: 1rem;
}
.\[\&_\[cmdk-group-heading\]\]\:font-medium [cmdk-group-heading] {
  font-weight: 500;
}
.\[\&_\[cmdk-group-heading\]\]\:text-muted-foreground [cmdk-group-heading] {
  color: hsl(var(--muted-foreground));
}
.\[\&_\[cmdk-group\]\:not\(\[hidden\]\)_\~\[cmdk-group\]\]\:pt-0 [cmdk-group]:not([hidden]) ~[cmdk-group] {
  padding-top: 0px;
}
.\[\&_\[cmdk-group\]\]\:px-2 [cmdk-group] {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.\[\&_\[cmdk-input-wrapper\]_svg\]\:h-5 [cmdk-input-wrapper] svg {
  height: 1.25rem;
}
.\[\&_\[cmdk-input-wrapper\]_svg\]\:w-5 [cmdk-input-wrapper] svg {
  width: 1.25rem;
}
.\[\&_\[cmdk-input\]\]\:h-12 [cmdk-input] {
  height: 3rem;
}
.\[\&_\[cmdk-item\]\]\:px-2 [cmdk-item] {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.\[\&_\[cmdk-item\]\]\:py-3 [cmdk-item] {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.\[\&_\[cmdk-item\]_svg\]\:h-5 [cmdk-item] svg {
  height: 1.25rem;
}
.\[\&_\[cmdk-item\]_svg\]\:w-5 [cmdk-item] svg {
  width: 1.25rem;
}
.\[\&_p\]\:leading-relaxed p {
  line-height: 1.625;
}
.\[\&_svg\]\:pointer-events-none svg {
  pointer-events: none;
}
.\[\&_svg\]\:invisible svg {
  visibility: hidden;
}
.\[\&_svg\]\:size-4 svg {
  width: 1rem;
  height: 1rem;
}
.\[\&_svg\]\:shrink-0 svg {
  flex-shrink: 0;
}
.\[\&_tr\:last-child\]\:border-0 tr:last-child {
  border-width: 0px;
}
.\[\&_tr\]\:border-b tr {
  border-bottom-width: 1px;
}
[data-side=left][data-collapsible=offcanvas] .\[\[data-side\=left\]\[data-collapsible\=offcanvas\]_\&\]\:-right-2 {
  right: -0.5rem;
}
[data-side=left][data-state=collapsed] .\[\[data-side\=left\]\[data-state\=collapsed\]_\&\]\:cursor-e-resize {
  cursor: e-resize;
}
[data-side=left] .\[\[data-side\=left\]_\&\]\:cursor-w-resize {
  cursor: w-resize;
}
[data-side=right][data-collapsible=offcanvas] .\[\[data-side\=right\]\[data-collapsible\=offcanvas\]_\&\]\:-left-2 {
  left: -0.5rem;
}
[data-side=right][data-state=collapsed] .\[\[data-side\=right\]\[data-state\=collapsed\]_\&\]\:cursor-w-resize {
  cursor: w-resize;
}
[data-side=right] .\[\[data-side\=right\]_\&\]\:cursor-e-resize {
  cursor: e-resize;
}</style><style id="_goober"> .go905199096{direction:ltr;position:fixed;bottom:0;right:0;z-index:99999;width:100%;max-height:90%;border-top:1px solid #344054;transform-origin:top;}.go283257069{cursor:pointer;display:flex;flex-direction:column;background-color:transparent;border:none;font-family:ui-sans-serif, Inter, system-ui, sans-serif, sans-serif;gap:calc(var(--tsrd-font-size) * 0.125);padding:0px;}.go283257069:hover{opacity:0.7;}.go283257069:focus-visible{outline-offset:4px;border-radius:calc(var(--tsrd-font-size) * 0.125);outline:2px solid #1849A9;}.go151905477{font-size:var(--tsrd-font-size);font-weight:700;line-height:calc(var(--tsrd-font-size) * 1);white-space:nowrap;color:#d0d5dd;}.go2259959382{font-weight:600;font-size:calc(var(--tsrd-font-size) * 0.75);background:linear-gradient(to right, #84cc16, #10b981);background-clip:text;-webkit-background-clip:text;line-height:1;-webkit-text-fill-color:transparent;white-space:nowrap;}.go1688509379{display:flex;font-size:calc(var(--tsrd-font-size) * 0.875);font-family:ui-sans-serif, Inter, system-ui, sans-serif, sans-serif;background-color:#191c24;color:#d0d5dd;}@media (max-width: 700px){.go1688509379{flex-direction:column;}}@media (max-width: 600px){.go1688509379{font-size:calc(var(--tsrd-font-size) * 0.75);}}.go1505018836{position:absolute;left:0;top:0;width:100%;height:4px;cursor:row-resize;z-index:100000;}.go1505018836:hover{background-color:#9B8AFBe5;}.go1468579934{flex:1 1 500px;min-height:40%;max-height:100%;overflow:auto;border-right:1px solid #344054;display:flex;flex-direction:column;}.go3747950898{overflow-y:auto;flex:1;}.go2041761433{padding:calc(var(--tsrd-font-size) * 0.5);}.go1648261519{display:flex;align-items:center;padding:calc(var(--tsrd-font-size) * 0.5) calc(var(--tsrd-font-size) * 0.625);gap:calc(var(--tsrd-font-size) * 0.625);border-bottom:#292e3d 1px solid;}.go2273422943{font-family:ui-sans-serif, Inter, system-ui, sans-serif, sans-serif;position:sticky;top:0;z-index:2;background-color:#212530;padding:0px calc(var(--tsrd-font-size) * 0.5);font-weight:500;font-size:calc(var(--tsrd-font-size) * 0.75);min-height:calc(var(--tsrd-font-size) * 2);line-height:calc(var(--tsrd-font-size) * 1);text-align:left;display:flex;align-items:center;}.go728099125{background:#7A2E0Eb3;color:#FEC84B;display:inline-block;padding:0px calc(var(--tsrd-font-size) * 0.625);border-radius:9999px;font-size:calc(var(--tsrd-font-size) * 0.75);font-weight:400;border:1px solid #FEC84B;}.go337815874{color:#FEC84B;}.go3889393570{padding:calc(var(--tsrd-font-size) * 0.375) calc(var(--tsrd-font-size) * 0.5);display:flex;align-items:center;justify-content:space-between;font-size:calc(var(--tsrd-font-size) * 0.75);}.go3620606704{display:flex;align-items:center;border:1px solid #667085;border-radius:calc(var(--tsrd-font-size) * 0.25);overflow:hidden;}.go3993070631{flex:1;justify-content:flex-end;display:flex;align-items:center;font-weight:400;color:#98a2b3;}.go1957354353{flex:1;line-height:calc(var(--tsrd-font-size) * 1);}.go2821925789{flex:1 1 500px;overflow:auto;display:flex;flex-direction:column;height:100%;border-right:1px solid #344054;}@media (max-width: 700px){.go2821925789{border-top:2px solid #344054;}}.go2851609767{flex:1 1 500px;min-height:40%;max-height:100%;overflow:auto;display:flex;flex-direction:column;}.go427126709{overflow-x:auto;overflow-y:visible;}.go263506863{color:#98a2b3;font-size:calc(var(--tsrd-font-size) * 0.75);line-height:calc(var(--tsrd-font-size) * 1);}.go1246236501{font-size:calc(var(--tsrd-font-size) * 0.75);line-height:calc(var(--tsrd-font-size) * 1);}.go1560856704{flex:1 1 auto;overflow-y:auto;}.go1860426633{flex:1 1 auto;overflow-y:auto;max-height:50%;}.go2050333436{flex:1;justify-content:flex-end;display:flex;}.go1035001778{display:flex;flex-direction:column;padding:calc(var(--tsrd-font-size) * 0.5);font-size:calc(var(--tsrd-font-size) * 0.75);color:#d0d5dd;line-height:calc(var(--tsrd-font-size) * 1.25);}.go2148009764{display:flex;justify-content:flex-end;flex:1;}.go2225354009{display:flex;}.go1767807125{background:#191c24;padding:calc(var(--tsrd-font-size) * 0.25) calc(var(--tsrd-font-size) * 0.5) calc(var(--tsrd-font-size) * 0.25) calc(var(--tsrd-font-size) * 0.375);border-radius:calc(var(--tsrd-font-size) * 0.375);position:fixed;z-index:99999;display:inline-flex;width:fit-content;cursor:pointer;appearance:none;border:1px solid #667085;gap:8px;align-items:center;font-size:calc(var(--tsrd-font-size) * 0.75);transition:all 0.25s ease-out;}.go1767807125:hover{background:#292e3d;}.go1561890071{font-weight:600;font-size:calc(var(--tsrd-font-size) * 0.75);background:linear-gradient(to right, #98f30c, #00f4a3);background-clip:text;-webkit-background-clip:text;line-height:1;-webkit-text-fill-color:transparent;white-space:nowrap;}.go2821754102{width:1px;background:#475467;height:100%;border-radius:999999px;color:transparent;}.go1460193506{position:relative;width:calc(var(--tsrd-font-size) * 1.25);height:calc(var(--tsrd-font-size) * 1.25);background:pink;border-radius:999999px;overflow:hidden;}.go2552515089{width:calc(var(--tsrd-font-size) * 1.25);height:calc(var(--tsrd-font-size) * 1.25);position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);filter:blur(3px) saturate(1.8) contrast(2);}.go3455555371{width:calc(var(--tsrd-font-size) * 1);height:calc(var(--tsrd-font-size) * 1);position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);}.go2224423957{position:absolute;cursor:pointer;z-index:100001;display:flex;align-items:center;justify-content:center;outline:none;background-color:#191c24;top:0;right:calc(var(--tsrd-font-size) * 0.5);transform:translate(0, -100%);border-right:#394056 1px solid;border-left:#394056 1px solid;border-top:#394056 1px solid;border-bottom:none;border-radius:calc(var(--tsrd-font-size) * 0.25) calc(var(--tsrd-font-size) * 0.25) 0px 0px;padding:calc(var(--tsrd-font-size) * 0.25) calc(var(--tsrd-font-size) * 0.375) calc(var(--tsrd-font-size) * 0.125) calc(var(--tsrd-font-size) * 0.375);}.go2224423957:hover{background-color:#292e3d;}.go2224423957::after{content:' ';position:absolute;top:100%;left:-calc(var(--tsrd-font-size) * 0.625);height:calc(var(--tsrd-font-size) * 0.375);width:calc(100% + calc(var(--tsrd-font-size) * 1.25));}.go959131266{color:#98a2b3;width:calc(var(--tsrd-font-size) * 0.5);height:calc(var(--tsrd-font-size) * 0.5);}.go1639696032{visibility:hidden;}.go3383159955{transition:all 0.4s ease;}.go844172998{pointer-events:none;transform:translateY(516px);}.go2968029118{bottom:calc(var(--tsrd-font-size) * 0.5);right:calc(var(--tsrd-font-size) * 0.5);}.go2279492678{opacity:1;pointer-events:auto;visibility:visible;}.go3643664686{appearance:none;border:none;font-size:12px;padding:4px 8px;background:transparent;cursor:pointer;font-family:ui-sans-serif, Inter, system-ui, sans-serif, sans-serif;font-weight:500;}.go2107851696{background:#313749;color:#d0d5dd;}.go1232428028{border-right:1px solid #667085;}.go862916629{color:#667085;background:#11131833;}.go898539426{font-family:ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;font-size:calc(var(--tsrd-font-size) * 0.75);line-height:calc(var(--tsrd-font-size) * 1.25);outline:none;word-break:break-word;}.go384656671{cursor:pointer;color:inherit;font:inherit;outline:inherit;background:transparent;border:none;padding:0;}.go2272256769{display:inline-flex;align-items:center;justify-content:center;width:calc(var(--tsrd-font-size) * 0.75);height:calc(var(--tsrd-font-size) * 0.75);padding-left:3px;box-sizing:content-box;}.go1058205856{display:flex;gap:calc(var(--tsrd-font-size) * 0.25);align-items:center;cursor:pointer;color:inherit;font:inherit;outline:inherit;background:transparent;border:none;padding:0;}.go1863549978{color:#9B8AFB;}.go1244333964{margin-left:calc(var(--tsrd-font-size) * 0.5);padding-left:calc(var(--tsrd-font-size) * 0.5);border-left:2px solid #313749;}.go2916152260{color:#667085;font-size:calc(var(--tsrd-font-size) * 0.625);padding-left:calc(var(--tsrd-font-size) * 0.25);}.go1577198724{appearance:none;border:0;cursor:pointer;background:transparent;color:inherit;padding:0;font-family:ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;font-size:calc(var(--tsrd-font-size) * 0.75);}.go3879208559{transform:rotate(90deg);transition:transform 0.1s ease;}.go1560190766{transform:rotate(0deg);transition:transform 0.1s ease;}.go3515246205{display:flex;border-bottom:1px solid #313749;align-items:center;padding:calc(var(--tsrd-font-size) * 0.25) calc(var(--tsrd-font-size) * 0.5);gap:calc(var(--tsrd-font-size) * 0.5);font-size:calc(var(--tsrd-font-size) * 0.75);color:#d0d5dd;cursor:pointer;line-height:calc(var(--tsrd-font-size) * 1);}.go3690131259{flex:0 0 auto;width:calc(var(--tsrd-font-size) * 0.75);height:calc(var(--tsrd-font-size) * 0.75);background:#3E1C96;border:1px solid #7A5AF8;border-radius:9999px;transition:all 0.25s ease-out;box-sizing:border-box;}.go339260511{flex:1 0 auto;display:flex;justify-content:space-between;align-items:center;font-size:calc(var(--tsrd-font-size) * 0.75);line-height:calc(var(--tsrd-font-size) * 1);}.go3874423951{margin-left:0;border-left:;}.go4274307370{flex:0 0 auto;width:calc(var(--tsrd-font-size) * 0.75);height:calc(var(--tsrd-font-size) * 0.75);background:#054F31;border:1px solid #12B76A;border-radius:9999px;transition:all 0.25s ease-out;box-sizing:border-box;}.go3954429975{margin-left:calc(var(--tsrd-font-size) * 0.875);border-left:solid 1px #344054;}.go1091339057{display:flex;border-bottom:1px solid #313749;align-items:center;padding:calc(var(--tsrd-font-size) * 0.25) calc(var(--tsrd-font-size) * 0.5);gap:calc(var(--tsrd-font-size) * 0.5);font-size:calc(var(--tsrd-font-size) * 0.75);color:#d0d5dd;cursor:default;line-height:calc(var(--tsrd-font-size) * 1);}.go1960959592{flex:0 0 auto;width:calc(var(--tsrd-font-size) * 0.75);height:calc(var(--tsrd-font-size) * 0.75);background:#101828;border:1px solid #667085;border-radius:9999px;transition:all 0.25s ease-out;box-sizing:border-box;}.go3595285728{background:#344054;border-color:#98a2b3;}.go3*********{color:#98a2b3;}.go2042539364{background:#292e3d;}.go1702703567{flex:0 0 auto;width:calc(var(--tsrd-font-size) * 0.75);height:calc(var(--tsrd-font-size) * 0.75);background:#194185;border:1px solid #2E90FA;border-radius:9999px;transition:all 0.25s ease-out;box-sizing:border-box;}.go3489369143{z-index:100000;position:fixed;padding:4px;text-align:left;display:flex;align-items:center;justify-content:center;border-radius:9999px;box-shadow:0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);overflow:hidden;}.go3489369143 div{position:absolute;top:-8px;left:-8px;right:-8px;bottom:-8px;border-radius:9999px;filter:blur(6px) saturate(1.2) contrast(1.1);}.go3489369143 div svg{position:absolute;width:100%;height:100%;}.go3489369143:focus-within{outline-offset:2px;outline:3px solid #039855;}.go3489369143 button{position:relative;z-index:1;padding:0;border-radius:9999px;background-color:transparent;border:none;height:40px;display:flex;width:40px;overflow:hidden;cursor:pointer;outline:none;}.go3489369143 button svg{position:absolute;width:100%;height:100%;}.go2812612974{position:fixed;z-index:9999;display:flex;gap:calc(var(--tsqd-font-size) * 0.125);}.go2812612974 *{box-sizing:border-box;text-transform:none;}.go2812612974 *::-webkit-scrollbar{width:7px;}.go2812612974 *::-webkit-scrollbar-track{background:transparent;}.go2812612974 *::-webkit-scrollbar-thumb{background:#414962;}.go2812612974 *::-webkit-scrollbar-thumb:hover{background:#394056;}.go3213103248{z-index:9999;display:flex;height:100%;gap:calc(var(--tsqd-font-size) * 0.125);}.go3213103248 *{box-sizing:border-box;text-transform:none;}.go3213103248 *::-webkit-scrollbar{width:7px;}.go3213103248 *::-webkit-scrollbar-track{background:transparent;}.go3213103248 *::-webkit-scrollbar-thumb{background:#414962;}.go3213103248 *::-webkit-scrollbar-thumb:hover{background:#394056;}.go1754112896{bottom:12px;right:12px;}.go1942079657{bottom:12px;left:12px;}.go2560641923{top:12px;left:12px;}.go4099359618{top:12px;right:12px;}.go613855381{position:relative;}.go540873609{top:0;right:0;left:0;max-height:90%;min-height:calc(var(--tsqd-font-size) * 3.5);border-bottom:#394056 1px solid;}.go3487837329{bottom:0;right:0;left:0;max-height:90%;min-height:calc(var(--tsqd-font-size) * 3.5);border-top:#394056 1px solid;}.go2240694395{bottom:0;right:0;top:0;border-left:#394056 1px solid;max-width:90%;}.go4063640663{bottom:0;left:0;top:0;border-right:#394056 1px solid;max-width:90%;}.go2885158556{position:absolute;cursor:pointer;z-index:5;display:flex;align-items:center;justify-content:center;outline:none;background-color:#191c24;}.go2885158556:hover{background-color:#292e3d;}.go2885158556:focus-visible{outline:2px solid #1570EF;}.go2885158556 svg{color:#98a2b3;width:calc(var(--tsqd-font-size) * 0.5);height:calc(var(--tsqd-font-size) * 0.5);}.go4210254915{bottom:0;right:calc(var(--tsqd-font-size) * 0.5);transform:translate(0, 100%);border-right:#394056 1px solid;border-left:#394056 1px solid;border-top:none;border-bottom:#394056 1px solid;border-radius:0px 0px calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.25);padding:calc(var(--tsqd-font-size) * 0.125) calc(var(--tsqd-font-size) * 0.375) calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.375);}.go4210254915::after{content:' ';position:absolute;bottom:100%;left:-calc(var(--tsqd-font-size) * 0.625);height:calc(var(--tsqd-font-size) * 0.375);width:calc(100% + calc(var(--tsqd-font-size) * 1.25));}.go4210254915 svg{transform:rotate(180deg);}.go1302489688{top:0;right:calc(var(--tsqd-font-size) * 0.5);transform:translate(0, -100%);border-right:#394056 1px solid;border-left:#394056 1px solid;border-top:#394056 1px solid;border-bottom:none;border-radius:calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.25) 0px 0px;padding:calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.375) calc(var(--tsqd-font-size) * 0.125) calc(var(--tsqd-font-size) * 0.375);}.go1302489688::after{content:' ';position:absolute;top:100%;left:-calc(var(--tsqd-font-size) * 0.625);height:calc(var(--tsqd-font-size) * 0.375);width:calc(100% + calc(var(--tsqd-font-size) * 1.25));}.go2158810208{bottom:calc(var(--tsqd-font-size) * 0.5);left:0;transform:translate(-100%, 0);border-right:none;border-left:#394056 1px solid;border-top:#394056 1px solid;border-bottom:#394056 1px solid;border-radius:calc(var(--tsqd-font-size) * 0.25) 0px 0px calc(var(--tsqd-font-size) * 0.25);padding:calc(var(--tsqd-font-size) * 0.375) calc(var(--tsqd-font-size) * 0.125) calc(var(--tsqd-font-size) * 0.375) calc(var(--tsqd-font-size) * 0.25);}.go2158810208::after{content:' ';position:absolute;left:100%;height:calc(100% + calc(var(--tsqd-font-size) * 1.25));width:calc(var(--tsqd-font-size) * 0.375);}.go2158810208 svg{transform:rotate(-90deg);}.go1366919888{bottom:calc(var(--tsqd-font-size) * 0.5);right:0;transform:translate(100%, 0);border-left:none;border-right:#394056 1px solid;border-top:#394056 1px solid;border-bottom:#394056 1px solid;border-radius:0px calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.25) 0px;padding:calc(var(--tsqd-font-size) * 0.375) calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.375) calc(var(--tsqd-font-size) * 0.125);}.go1366919888::after{content:' ';position:absolute;right:100%;height:calc(100% + calc(var(--tsqd-font-size) * 1.25));width:calc(var(--tsqd-font-size) * 0.375);}.go1366919888 svg{transform:rotate(90deg);}.go2506729803{flex:1 1 700px;background-color:#191c24;display:flex;flex-direction:column;}.go2506729803 *{font-family:ui-sans-serif, Inter, system-ui, sans-serif, sans-serif;}.go774879075{position:absolute;transition:background-color 0.125s ease;z-index:4;}.go774879075:hover{background-color:#9B8AFBe5;}.go1191431590{bottom:0;width:100%;height:3px;cursor:ns-resize;}.go253692464{top:0;width:100%;height:3px;cursor:ns-resize;}.go1064895163{left:0;width:3px;height:100%;cursor:ew-resize;}.go2194780694{right:0;width:3px;height:100%;cursor:ew-resize;}.go1245051397{display:flex;justify-content:space-between;align-items:center;padding:calc(var(--tsqd-font-size) * 0.5) calc(var(--tsqd-font-size) * 0.625);gap:calc(var(--tsqd-font-size) * 0.625);border-bottom:#292e3d 1px solid;}.go1245051397 > button{padding:0;background:transparent;border:none;display:flex;gap:calc(var(--tsqd-font-size) * 0.125);flex-direction:column;}.go857308786{display:flex;gap:calc(var(--tsqd-font-size) * 0.75);align-items:center;}.go1936323176{cursor:pointer;display:flex;flex-direction:column;background-color:transparent;border:none;gap:calc(var(--tsqd-font-size) * 0.125);padding:0px;}.go1936323176:hover{opacity:0.7;}.go1936323176:focus-visible{outline-offset:4px;border-radius:calc(var(--tsqd-font-size) * 0.125);outline:2px solid #1849A9;}.go3203190455{font-size:var(--tsqd-font-size);font-weight:700;line-height:calc(var(--tsqd-font-size) * 1);white-space:nowrap;color:#d0d5dd;}.go2826607642{font-weight:600;font-size:calc(var(--tsqd-font-size) * 0.75);background:linear-gradient( to right, #dd524b, #e9a03b );background-clip:text;-webkit-background-clip:text;line-height:1;-webkit-text-fill-color:transparent;white-space:nowrap;}.go1745108753{display:flex;gap:calc(var(--tsqd-font-size) * 0.5);height:min-content;}.go2789881399{display:flex;gap:calc(var(--tsqd-font-size) * 0.375);box-sizing:border-box;height:calc(var(--tsqd-font-size) * 1.625);background:#292e3d;color:#d0d5dd;border-radius:calc(var(--tsqd-font-size) * 0.25);font-size:calc(var(--tsqd-font-size) * 0.875);padding:calc(var(--tsqd-font-size) * 0.25);padding-left:calc(var(--tsqd-font-size) * 0.375);align-items:center;font-weight:500;border:1px solid transparent;user-select:none;position:relative;}.go2789881399:focus-visible{outline-offset:2px;outline:2px solid #1849A9;}.go1270626771{font-size:calc(var(--tsqd-font-size) * 0.75);}.go2647401337{font-size:calc(var(--tsqd-font-size) * 0.75);padding:0 5px;display:flex;align-items:center;justify-content:center;color:#98a2b3;background-color:#394056;border-radius:2px;font-variant-numeric:tabular-nums;height:calc(var(--tsqd-font-size) * 1.125);}.go701307223{position:absolute;z-index:1;background-color:#292e3d;top:100%;left:50%;transform:translate(-50%, calc(calc(var(--tsqd-font-size) * 0.5)));padding:calc(var(--tsqd-font-size) * 0.125) calc(var(--tsqd-font-size) * 0.5);border-radius:calc(var(--tsqd-font-size) * 0.25);font-size:calc(var(--tsqd-font-size) * 0.75);border:1px solid #475467;color:#d0d5dd;}.go701307223::before{top:0px;content:' ';display:block;left:50%;transform:translate(-50%, -100%);position:absolute;border-color:transparent transparent #475467 transparent;border-style:solid;border-width:7px;}.go701307223::after{top:0px;content:' ';display:block;left:50%;transform:translate(-50%, calc(-100% + 2px));position:absolute;border-color:transparent transparent #292e3d transparent;border-style:solid;border-width:7px;}.go903258897{display:flex;gap:calc(var(--tsqd-font-size) * 0.5);}.go903258897 > button{cursor:pointer;padding:calc(var(--tsqd-font-size) * 0.125) calc(var(--tsqd-font-size) * 0.375) calc(var(--tsqd-font-size) * 0.125) calc(var(--tsqd-font-size) * 0.5);border-radius:calc(var(--tsqd-font-size) * 0.25);background-color:#313749;border:1px solid #414962;color:#d0d5dd;font-size:calc(var(--tsqd-font-size) * 0.75);display:flex;align-items:center;line-height:calc(var(--tsqd-font-size) * 1.25);gap:calc(var(--tsqd-font-size) * 0.375);max-width:160px;}.go903258897 > button:focus-visible{outline-offset:2px;border-radius:calc(var(--tsqd-font-size) * 0.125);outline:2px solid #1849A9;}.go903258897 > button svg{width:calc(var(--tsqd-font-size) * 0.75);height:calc(var(--tsqd-font-size) * 0.75);color:#98a2b3;}.go2738029867{padding:calc(var(--tsqd-font-size) * 0.125) calc(var(--tsqd-font-size) * 0.5);border-radius:calc(var(--tsqd-font-size) * 0.25);background-color:#313749;display:flex;box-sizing:content-box;align-items:center;gap:calc(var(--tsqd-font-size) * 0.375);max-width:160px;min-width:100px;border:1px solid #414962;height:min-content;color:#98a2b3;}.go2738029867 > svg{width:calc(var(--tsqd-font-size) * 0.75);height:calc(var(--tsqd-font-size) * 0.75);}.go2738029867 input{font-size:calc(var(--tsqd-font-size) * 0.75);width:100%;background-color:#313749;border:none;padding:0;line-height:calc(var(--tsqd-font-size) * 1.25);color:#d0d5dd;}.go2738029867 input::placeholder{color:#d0d5dd;}.go2738029867 input:focus{outline:none;}.go2738029867:focus-within{outline-offset:2px;border-radius:calc(var(--tsqd-font-size) * 0.125);outline:2px solid #1849A9;}.go4264856030{padding:calc(var(--tsqd-font-size) * 0.125) calc(var(--tsqd-font-size) * 0.5);border-radius:calc(var(--tsqd-font-size) * 0.25);background-color:#313749;display:flex;align-items:center;gap:calc(var(--tsqd-font-size) * 0.375);box-sizing:content-box;max-width:160px;border:1px solid #414962;height:min-content;}.go4264856030 > svg{color:#98a2b3;width:calc(var(--tsqd-font-size) * 0.5);height:calc(var(--tsqd-font-size) * 0.5);}.go4264856030 > select{appearance:none;color:#d0d5dd;min-width:100px;line-height:calc(var(--tsqd-font-size) * 1.25);font-size:calc(var(--tsqd-font-size) * 0.75);background-color:#313749;border:none;}.go4264856030 > select:focus{outline:none;}.go4264856030:focus-within{outline-offset:2px;border-radius:calc(var(--tsqd-font-size) * 0.125);outline:2px solid #1849A9;}.go441294505{display:flex;gap:calc(var(--tsqd-font-size) * 0.5);}.go3814664482{border-radius:calc(var(--tsqd-font-size) * 0.25);background-color:#313749;border:1px solid #414962;width:calc(var(--tsqd-font-size) * 1.625);height:calc(var(--tsqd-font-size) * 1.625);justify-content:center;display:flex;align-items:center;gap:calc(var(--tsqd-font-size) * 0.375);max-width:160px;cursor:pointer;padding:0;}.go3814664482:hover{background-color:#292e3d;}.go3814664482 svg{color:#d0d5dd;width:calc(var(--tsqd-font-size) * 0.75);height:calc(var(--tsqd-font-size) * 0.75);}.go3814664482:focus-visible{outline-offset:2px;border-radius:calc(var(--tsqd-font-size) * 0.125);outline:2px solid #1849A9;}.go848580748 svg{stroke:#F79009;fill:#F79009;}.go3932029643{flex:1;overflow-y:auto;}.go3932029643 > div{display:flex;flex-direction:column;}.go2242848476{display:flex;align-items:center;padding:0;border:none;cursor:pointer;color:#d0d5dd;background-color:#191c24;line-height:1;}.go2242848476:focus{outline:none;}.go2242848476:focus-visible{outline-offset:-2px;border-radius:calc(var(--tsqd-font-size) * 0.125);outline:2px solid #1849A9;}.go2242848476:hover .tsqd-query-hash{background-color:#212530;}.go2242848476 .tsqd-query-observer-count{padding:0 calc(var(--tsqd-font-size) * 0.25);user-select:none;min-width:calc(var(--tsqd-font-size) * 1.625);align-self:stretch;display:flex;align-items:center;justify-content:center;font-size:calc(var(--tsqd-font-size) * 0.75);font-weight:500;border-bottom-width:1px;border-bottom-style:solid;border-bottom:1px solid #191c24;}.go2242848476 .tsqd-query-hash{user-select:text;font-size:calc(var(--tsqd-font-size) * 0.75);display:flex;align-items:center;min-height:calc(var(--tsqd-font-size) * 1.5);flex:1;padding:calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.5);font-family:ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;border-bottom:1px solid #313749;text-align:left;text-overflow:clip;word-break:break-word;}.go2242848476 .tsqd-query-disabled-indicator{align-self:stretch;display:flex;align-items:center;padding:0 calc(var(--tsqd-font-size) * 0.5);color:#d0d5dd;background-color:#212530;border-bottom:1px solid #313749;font-size:calc(var(--tsqd-font-size) * 0.75);}.go3691623036{background-color:#292e3d;}.go2655854486{flex:1 1 700px;background-color:#191c24;color:#d0d5dd;font-family:ui-sans-serif, Inter, system-ui, sans-serif, sans-serif;display:flex;flex-direction:column;overflow-y:auto;text-align:left;}.go1223780339{font-family:ui-sans-serif, Inter, system-ui, sans-serif, sans-serif;position:sticky;top:0;z-index:2;background-color:#212530;padding:calc(var(--tsqd-font-size) * 0.375) calc(var(--tsqd-font-size) * 0.5);font-weight:500;font-size:calc(var(--tsqd-font-size) * 0.75);line-height:calc(var(--tsqd-font-size) * 1);text-align:left;}.go2709625642{margin:calc(var(--tsqd-font-size) * 0.375) 0px calc(var(--tsqd-font-size) * 0.5) 0px;}.go2709625642 > div{display:flex;align-items:stretch;padding:0 calc(var(--tsqd-font-size) * 0.5);line-height:calc(var(--tsqd-font-size) * 1.25);justify-content:space-between;}.go2709625642 > div > span{font-size:calc(var(--tsqd-font-size) * 0.75);}.go2709625642 > div > span:nth-child(2){font-variant-numeric:tabular-nums;}.go2709625642 > div:first-child{margin-bottom:calc(var(--tsqd-font-size) * 0.375);}.go2709625642 code{font-family:ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;margin:0;font-size:calc(var(--tsqd-font-size) * 0.75);line-height:calc(var(--tsqd-font-size) * 1);}.go2709625642 pre{margin:0;display:flex;align-items:center;}.go564315114{border:1px solid #414962;border-radius:calc(var(--tsqd-font-size) * 0.25);font-weight:500;padding:calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.625);}.go3392938368{flex-wrap:wrap;margin:calc(var(--tsqd-font-size) * 0.5) 0px calc(var(--tsqd-font-size) * 0.5) 0px;display:flex;gap:calc(var(--tsqd-font-size) * 0.5);padding:0px calc(var(--tsqd-font-size) * 0.5);}.go3392938368 > button{font-family:ui-sans-serif, Inter, system-ui, sans-serif, sans-serif;font-size:calc(var(--tsqd-font-size) * 0.75);padding:calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.5);display:flex;border-radius:calc(var(--tsqd-font-size) * 0.25);background-color:#212530;border:1px solid #313749;align-items:center;gap:calc(var(--tsqd-font-size) * 0.5);font-weight:500;line-height:calc(var(--tsqd-font-size) * 1);cursor:pointer;}.go3392938368 > button:focus-visible{outline-offset:2px;border-radius:calc(var(--tsqd-font-size) * 0.125);outline:2px solid #1849A9;}.go3392938368 > button:hover{background-color:#292e3d;}.go3392938368 > button:disabled{opacity:0.6;cursor:not-allowed;}.go3392938368 > button > span{width:calc(var(--tsqd-font-size) * 0.375);height:calc(var(--tsqd-font-size) * 0.375);border-radius:9999px;}.go601952022{font-size:calc(var(--tsqd-font-size) * 0.75);padding:calc(var(--tsqd-font-size) * 0.125) calc(var(--tsqd-font-size) * 0.5);display:flex;border-radius:calc(var(--tsqd-font-size) * 0.25);overflow:hidden;background-color:#212530;border:1px solid #313749;align-items:center;gap:calc(var(--tsqd-font-size) * 0.5);font-weight:500;line-height:calc(var(--tsqd-font-size) * 1.25);color:#f87171;cursor:pointer;position:relative;}.go601952022:hover{background-color:#292e3d;}.go601952022 > span{width:calc(var(--tsqd-font-size) * 0.375);height:calc(var(--tsqd-font-size) * 0.375);border-radius:9999px;}.go601952022:focus-within{outline-offset:2px;border-radius:calc(var(--tsqd-font-size) * 0.125);outline:2px solid #1849A9;}.go601952022 select{position:absolute;top:0;left:0;width:100%;height:100%;appearance:none;background-color:transparent;border:none;color:transparent;outline:none;}.go601952022 svg path{stroke:#f87171;}.go601952022 svg{width:calc(var(--tsqd-font-size) * 0.5);height:calc(var(--tsqd-font-size) * 0.5);}.go510199209{display:flex;flex-direction:column;gap:calc(var(--tsqd-font-size) * 0.125);border-radius:calc(var(--tsqd-font-size) * 0.25);border:1px solid #344054;background-color:#212530;font-size:calc(var(--tsqd-font-size) * 0.75);color:#d0d5dd;z-index:99999;min-width:120px;padding:calc(var(--tsqd-font-size) * 0.125);}.go510199209 *{font-family:ui-sans-serif, Inter, system-ui, sans-serif, sans-serif;}.go1686117193{display:flex;align-items:center;justify-content:space-between;border-radius:calc(var(--tsqd-font-size) * 0.125);padding:calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.25);cursor:pointer;background-color:transparent;border:none;color:#d0d5dd;}.go1686117193 svg{color:#98a2b3;transform:rotate(-90deg);width:calc(var(--tsqd-font-size) * 0.5);height:calc(var(--tsqd-font-size) * 0.5);}.go1686117193:hover{background-color:#292e3d;}.go1686117193:focus-visible{outline-offset:2px;outline:2px solid #1849A9;}.go1686117193.data-disabled{opacity:0.6;cursor:not-allowed;}.go3851724898{padding:calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.25);font-weight:500;border-bottom:1px solid #313749;color:#98a2b3;font-size:calc(var(--tsqd-font-size) * 0.75);}.go1847177295{display:flex;align-items:center;justify-content:space-between;color:#d0d5dd;font-size:calc(var(--tsqd-font-size) * 0.75);border-radius:calc(var(--tsqd-font-size) * 0.125);padding:calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.25);cursor:pointer;background-color:transparent;border:none;}.go1847177295 svg{color:#98a2b3;}.go1847177295:hover{background-color:#292e3d;}.go1847177295:focus-visible{outline-offset:2px;outline:2px solid #1849A9;}.go2085547053{background-color:#3E1C96;color:#BDB4FE;}.go2085547053 svg{color:#BDB4FE;}.go2085547053:hover{background-color:#3E1C96;}.go1417314958{border-radius:calc(var(--tsqd-font-size) * 0.25);background-color:#212530;border:1px solid #414962;display:flex;padding:0;font-size:calc(var(--tsqd-font-size) * 0.75);color:#d0d5dd;overflow:hidden;}.go1417314958:has(:focus-visible){outline:2px solid #1849A9;}.go1417314958 .tsqd-radio-toggle{opacity:0.5;display:flex;}.go1417314958 .tsqd-radio-toggle label{display:flex;align-items:center;cursor:pointer;line-height:calc(var(--tsqd-font-size) * 1.5);}.go1417314958 .tsqd-radio-toggle label:hover{background-color:#292e3d;}.go1417314958 > [data-checked]{opacity:1;background-color:#313749;}.go1417314958 > [data-checked] label:hover{background-color:#313749;}.go1417314958 .tsqd-radio-toggle:first-child{border-right:1px solid #414962;}.go1417314958 .tsqd-radio-toggle:first-child label{padding:0 calc(var(--tsqd-font-size) * 0.375) 0 calc(var(--tsqd-font-size) * 0.5);}.go1417314958 .tsqd-radio-toggle:nth-child(2) label{padding:0 calc(var(--tsqd-font-size) * 0.5) 0 calc(var(--tsqd-font-size) * 0.375);}.go643647742{padding:calc(var(--tsqd-font-size) * 0.5);}.go643647742 > [data-error='true']{outline:2px solid #991b1b;outline-offset:2px;border-radius:calc(var(--tsqd-font-size) * 0.125);}.go3827170696{width:100%;max-height:500px;font-family:'Fira Code', monospace;font-size:calc(var(--tsqd-font-size) * 0.75);border-radius:calc(var(--tsqd-font-size) * 0.25);field-sizing:content;padding:calc(var(--tsqd-font-size) * 0.5);background-color:#111318;color:#f2f4f7;border:1px solid #344054;resize:none;}.go3827170696:focus{outline-offset:2px;border-radius:calc(var(--tsqd-font-size) * 0.125);outline:2px solid #1849A9;}.go3542865594{display:flex;justify-content:space-between;gap:calc(var(--tsqd-font-size) * 0.5);align-items:center;padding-top:calc(var(--tsqd-font-size) * 0.25);font-size:calc(var(--tsqd-font-size) * 0.75);}.go818103473{color:#ef4444;}.go1125328280{font-family:ui-sans-serif, Inter, system-ui, sans-serif, sans-serif;font-size:calc(var(--tsqd-font-size) * 0.75);padding:calc(var(--tsqd-font-size) * 0.25) calc(var(--tsqd-font-size) * 0.5);display:flex;border-radius:calc(var(--tsqd-font-size) * 0.25);background-color:#212530;border:1px solid #313749;align-items:center;gap:calc(var(--tsqd-font-size) * 0.5);font-weight:500;line-height:calc(var(--tsqd-font-size) * 1);cursor:pointer;}.go1125328280:focus-visible{outline-offset:2px;border-radius:calc(var(--tsqd-font-size) * 0.125);outline:2px solid #1849A9;}.go1125328280:hover{background-color:#292e3d;}.go1125328280:disabled{opacity:0.6;cursor:not-allowed;}.go2420793703 .tsqd-panel-transition-exit-active, .go2420793703 .tsqd-panel-transition-enter-active{transition:opacity 0.3s, transform 0.3s;}.go2420793703 .tsqd-panel-transition-exit-to, .go2420793703 .tsqd-panel-transition-enter{transform:translateY(var(--tsqd-panel-height));}.go2420793703 .tsqd-button-transition-exit-active, .go2420793703 .tsqd-button-transition-enter-active{transition:opacity 0.3s, transform 0.3s;opacity:1;}.go2420793703 .tsqd-button-transition-exit-to, .go2420793703 .tsqd-button-transition-enter{transform:translateY(72px);opacity:0;}</style></head>
  <body class="group/body">
    <div id="root"><div class="group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar" style="--sidebar-width: 16rem; --sidebar-width-icon: 3rem;"><a class="fixed left-44 z-[999] -translate-y-52 whitespace-nowrap bg-primary px-4 py-2 text-sm font-medium text-primary-foreground opacity-95 shadow transition hover:bg-primary/90 focus:translate-y-3 focus:transform focus-visible:ring-1 focus-visible:ring-ring" href="http://localhost:5173/#content">Skip to Main</a><div class="group peer hidden md:block text-sidebar-foreground" data-state="expanded" data-collapsible="" data-variant="sidebar" data-side="left"><div class="duration-200 relative h-svh w-[--sidebar-width] bg-transparent transition-[width] ease-linear group-data-[collapsible=offcanvas]:w-0 group-data-[side=right]:rotate-180 group-data-[collapsible=icon]:w-[--sidebar-width-icon]"></div><div class="duration-200 fixed inset-y-0 z-10 hidden h-svh w-[--sidebar-width] transition-[left,right,width] ease-linear md:flex left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)] group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l"><div data-sidebar="sidebar" class="flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow"><div class="flex justify-center bg-logo"><img alt="Logo" class="w-40 pt-4 pb-4 mx-auto" src="data:image/svg+xml,%3csvg%20width=&#39;220&#39;%20height=&#39;54&#39;%20viewBox=&#39;0%200%20220%2054&#39;%20fill=&#39;none&#39;%20xmlns=&#39;http://www.w3.org/2000/svg&#39;%3e%3cpath%20d=&#39;M76.0772%206.41626H68.8112V48.0107H76.0772V6.41626Z&#39;%20fill=&#39;%23212120&#39;/%3e%3cpath%20d=&#39;M83.6837%2048.0107V6.41626H118.983V13.6823H90.8898V48.0075H83.6837V48.0107ZM96.2109%2024.4379H117.265V30.7897H96.2109V24.4379Z&#39;%20fill=&#39;%23212120&#39;/%3e%3cpath%20d=&#39;M126.82%2048.0107C125.71%2048.0107%20124.752%2047.6954%20123.932%2047.065C123.109%2046.4345%20122.7%2045.5077%20122.7%2044.2878C122.7%2043.2192%20123.194%2042.1915%20124.187%2041.2017L151.82%2013.3387H123.217V6.41626H159.603C160.672%206.41626%20161.621%206.73464%20162.466%207.36195C163.304%207.9924%20163.72%208.91917%20163.72%2010.1391C163.72%2011.2077%20163.225%2012.2354%20162.236%2013.2283L134.603%2041.0882H163.55V48.0107H126.82Z&#39;%20fill=&#39;%23212120&#39;/%3e%3cpath%20d=&#39;M211.219%2048.0107L205.069%2037.6838H185.654L189.043%2031.871H201.626L192.485%2016.5193L173.858%2048.0138H165.675L189.266%208.73319C189.676%208.01763%20190.168%207.45337%20190.732%207.03726C191.3%206.62431%20191.974%206.41942%20192.765%206.41942C193.56%206.41942%20194.225%206.62431%20194.773%207.03726C195.316%207.45021%20195.795%208.01447%20196.211%208.73319L219.856%2048.0107H211.219Z&#39;%20fill=&#39;%23212120&#39;/%3e%3cpath%20d=&#39;M217.205%2010.1391C217.205%209.31321%20216.524%208.58188%20215.528%208.58188H213.681V13.3481H214.604V11.7184H215.553L216.357%2013.3481H217.331L216.408%2011.5482C216.912%2011.3055%20217.205%2010.7948%20217.205%2010.1391ZM215.38%2010.965H214.601V9.36049H215.38C215.891%209.36049%20216.256%209.67572%20216.256%2010.1391C216.256%2010.6498%20215.891%2010.965%20215.38%2010.965Z&#39;%20fill=&#39;%23212120&#39;/%3e%3cpath%20d=&#39;M215.235%206.41626C212.656%206.41626%20210.639%208.48415%20210.639%2011.0123C210.639%2013.5404%20212.656%2015.6083%20215.235%2015.6083C217.813%2015.6083%20219.856%2013.5404%20219.856%2011.0123C219.856%208.48415%20217.813%206.41626%20215.235%206.41626ZM215.235%2014.7604C213.12%2014.7604%20211.562%2013.0833%20211.562%2011.0154C211.562%208.94754%20213.12%207.27053%20215.235%207.27053C217.35%207.27053%20218.958%208.94754%20218.958%2011.0154C218.958%2013.0802%20217.35%2014.7604%20215.235%2014.7604Z&#39;%20fill=&#39;%23212120&#39;/%3e%3cpath%20d=&#39;M18.8097%2026.2158C21.404%2029.5099%2023.1756%2031.5274%2026.7787%2031.5274C30.3786%2031.5274%2032.1502%2029.5099%2034.7445%2026.2158C37.3136%2022.95%2040.2295%2019.2524%2046.1148%2019.2524C49.0622%2019.2524%2051.2656%2020.4313%2053.053%2022.0201C50.6352%209.71356%2039.7913%200.426926%2026.7755%200.426926C13.7597%200.426926%202.91587%209.71356%200.498066%2022.0201C2.28541%2020.4313%204.48886%2019.2524%207.4394%2019.2524C13.3247%2019.2524%2016.2374%2022.9532%2018.8097%2026.2158Z&#39;%20fill=&#39;url(%23paint0_radial_359_5632)&#39;/%3e%3cpath%20d=&#39;M46.1148%2023.5836C42.5149%2023.5836%2040.7401%2025.6011%2038.149%2028.8952C35.5798%2032.161%2032.664%2035.8586%2026.7787%2035.8586C20.8933%2035.8586%2017.9775%2032.1578%2015.4084%2028.8952C12.814%2025.6011%2011.0425%2023.5836%207.43939%2023.5836C4.08851%2023.5836%202.32008%2024.8414%200%2027.6658C0.242726%2042.2483%2012.1331%2054%2026.7755%2054C41.4179%2054%2053.3083%2042.2514%2053.551%2027.6658C51.2309%2024.8382%2049.4625%2023.5836%2046.1148%2023.5836Z&#39;%20fill=&#39;url(%23paint1_radial_359_5632)&#39;/%3e%3cdefs%3e%3cradialGradient%20id=&#39;paint0_radial_359_5632&#39;%20cx=&#39;0&#39;%20cy=&#39;0&#39;%20r=&#39;1&#39;%20gradientUnits=&#39;userSpaceOnUse&#39;%20gradientTransform=&#39;translate(26.7746%2027.2134)%20scale(26.7863)&#39;%3e%3cstop%20stop-color=&#39;%23C39A2D&#39;/%3e%3cstop%20offset=&#39;0.3&#39;%20stop-color=&#39;%23C39A2D&#39;/%3e%3cstop%20offset=&#39;0.5264&#39;%20stop-color=&#39;%23C0972D&#39;/%3e%3cstop%20offset=&#39;0.6626&#39;%20stop-color=&#39;%23B78F2C&#39;/%3e%3cstop%20offset=&#39;0.7749&#39;%20stop-color=&#39;%23A9812B&#39;/%3e%3cstop%20offset=&#39;0.8741&#39;%20stop-color=&#39;%23957028&#39;/%3e%3cstop%20offset=&#39;0.964&#39;%20stop-color=&#39;%237E5B23&#39;/%3e%3cstop%20offset=&#39;0.9987&#39;%20stop-color=&#39;%23735220&#39;/%3e%3cstop%20offset=&#39;0.9995&#39;%20stop-color=&#39;%23735220&#39;/%3e%3cstop%20offset=&#39;1&#39;%20stop-color=&#39;%23735220&#39;/%3e%3c/radialGradient%3e%3cradialGradient%20id=&#39;paint1_radial_359_5632&#39;%20cx=&#39;0&#39;%20cy=&#39;0&#39;%20r=&#39;1&#39;%20gradientUnits=&#39;userSpaceOnUse&#39;%20gradientTransform=&#39;translate(26.7748%2027.2134)%20scale(26.7804%2026.7804)&#39;%3e%3cstop%20stop-color=&#39;%23C39A2D&#39;/%3e%3cstop%20offset=&#39;0.3&#39;%20stop-color=&#39;%23C39A2D&#39;/%3e%3cstop%20offset=&#39;0.5264&#39;%20stop-color=&#39;%23C0972D&#39;/%3e%3cstop%20offset=&#39;0.6626&#39;%20stop-color=&#39;%23B78F2C&#39;/%3e%3cstop%20offset=&#39;0.7749&#39;%20stop-color=&#39;%23A9812B&#39;/%3e%3cstop%20offset=&#39;0.8741&#39;%20stop-color=&#39;%23957028&#39;/%3e%3cstop%20offset=&#39;0.964&#39;%20stop-color=&#39;%237E5B23&#39;/%3e%3cstop%20offset=&#39;0.9987&#39;%20stop-color=&#39;%23735220&#39;/%3e%3cstop%20offset=&#39;0.9995&#39;%20stop-color=&#39;%23735220&#39;/%3e%3cstop%20offset=&#39;1&#39;%20stop-color=&#39;%23735220&#39;/%3e%3c/radialGradient%3e%3c/defs%3e%3c/svg%3e"></div><div data-sidebar="content" class="flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden"><div data-sidebar="group" class="relative flex w-full min-w-0 flex-col p-2"><div data-sidebar="group-label" class="duration-200 flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] ease-linear focus-visible:ring-2 [&amp;&gt;svg]:size-4 [&amp;&gt;svg]:shrink-0 group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0">General</div><ul data-sidebar="menu" class="flex w-full min-w-0 flex-col gap-1"><li data-sidebar="menu-item" class="group/menu-item relative"><a data-sidebar="menu-button" data-size="default" data-active="true" data-state="closed" class="peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left outline-none ring-sidebar-ring transition-[width,height,padding] focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&amp;&gt;span:last-child]:truncate [&amp;&gt;svg]:size-4 [&amp;&gt;svg]:shrink-0 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground h-8 text-sm active" href="http://localhost:5173/" data-status="active" aria-current="page"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-house"><path d="M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8"></path><path d="M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path></svg><span>Home</span></a></li><li data-sidebar="menu-item" class="group/menu-item relative"><a data-sidebar="menu-button" data-size="default" data-active="false" data-state="closed" class="peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left outline-none ring-sidebar-ring transition-[width,height,padding] focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&amp;&gt;span:last-child]:truncate [&amp;&gt;svg]:size-4 [&amp;&gt;svg]:shrink-0 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground h-8 text-sm active" href="http://localhost:5173/" data-status="active" aria-current="page"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-table2"><path d="M9 3H5a2 2 0 0 0-2 2v4m6-6h10a2 2 0 0 1 2 2v4M9 3v18m0 0h10a2 2 0 0 0 2-2V9M9 21H5a2 2 0 0 1-2-2V9m0 0h18"></path></svg><span>Dashboard</span></a></li></ul></div><div data-sidebar="group" class="relative flex w-full min-w-0 flex-col p-2"><div data-sidebar="group-label" class="duration-200 flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] ease-linear focus-visible:ring-2 [&amp;&gt;svg]:size-4 [&amp;&gt;svg]:shrink-0 group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0">Setup</div><ul data-sidebar="menu" class="flex w-full min-w-0 flex-col gap-1"><li data-sidebar="menu-item" class="group/menu-item relative group/collapsible" data-state="closed"><button data-sidebar="menu-button" data-size="default" data-active="false" class="peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left outline-none ring-sidebar-ring transition-[width,height,padding] focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&amp;&gt;span:last-child]:truncate [&amp;&gt;svg]:size-4 [&amp;&gt;svg]:shrink-0 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground h-8 text-sm" type="button" aria-controls="radix-:r2:" aria-expanded="false" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-badge"><path d="M12 22h6a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v3"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path><path d="M5 17a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"></path><path d="M7 16.5 8 22l-3-1-3 1 1-5.5"></path></svg><span>License</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90"><path d="m9 18 6-6-6-6"></path></svg></button><div data-state="closed" id="radix-:r2:" hidden="" class="CollapsibleContent" style=""></div></li><li data-sidebar="menu-item" class="group/menu-item relative"><a data-sidebar="menu-button" data-size="default" data-active="false" data-state="closed" class="peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left outline-none ring-sidebar-ring transition-[width,height,padding] focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&amp;&gt;span:last-child]:truncate [&amp;&gt;svg]:size-4 [&amp;&gt;svg]:shrink-0 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground h-8 text-sm active" href="http://localhost:5173/" data-status="active" aria-current="page"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-book-marked"><path d="M10 2v8l3-3 3 3V2"></path><path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20"></path></svg><span>Visa</span></a></li></ul></div><div data-sidebar="group" class="relative flex w-full min-w-0 flex-col p-2"><div data-sidebar="group-label" class="duration-200 flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] ease-linear focus-visible:ring-2 [&amp;&gt;svg]:size-4 [&amp;&gt;svg]:shrink-0 group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0">Applications</div><ul data-sidebar="menu" class="flex w-full min-w-0 flex-col gap-1"><li data-sidebar="menu-item" class="group/menu-item relative"><a data-sidebar="menu-button" data-size="default" data-active="false" data-state="closed" class="peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left outline-none ring-sidebar-ring transition-[width,height,padding] focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&amp;&gt;span:last-child]:truncate [&amp;&gt;svg]:size-4 [&amp;&gt;svg]:shrink-0 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground h-8 text-sm active" href="http://localhost:5173/" data-status="active" aria-current="page"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-symlink"><path d="m10 18 3-3-3-3"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path><path d="M4 11V4a2 2 0 0 1 2-2h9l5 5v13a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h7"></path></svg><span>Renewals</span></a></li><li data-sidebar="menu-item" class="group/menu-item relative"><a data-sidebar="menu-button" data-size="default" data-active="false" data-state="closed" class="peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left outline-none ring-sidebar-ring transition-[width,height,padding] focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&amp;&gt;span:last-child]:truncate [&amp;&gt;svg]:size-4 [&amp;&gt;svg]:shrink-0 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground h-8 text-sm active" href="http://localhost:5173/" data-status="active" aria-current="page"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-check2"><path d="M4 22h14a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v4"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path><path d="m3 15 2 2 4-4"></path></svg><span>Status Check</span></a></li></ul></div><div data-sidebar="group" class="relative flex w-full min-w-0 flex-col p-2"><div data-sidebar="group-label" class="duration-200 flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] ease-linear focus-visible:ring-2 [&amp;&gt;svg]:size-4 [&amp;&gt;svg]:shrink-0 group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0">Transactions</div><ul data-sidebar="menu" class="flex w-full min-w-0 flex-col gap-1"><li data-sidebar="menu-item" class="group/menu-item relative"><a data-sidebar="menu-button" data-size="default" data-active="false" data-state="closed" class="peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left outline-none ring-sidebar-ring transition-[width,height,padding] focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&amp;&gt;span:last-child]:truncate [&amp;&gt;svg]:size-4 [&amp;&gt;svg]:shrink-0 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground h-8 text-sm active" href="http://localhost:5173/" data-status="active" aria-current="page"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-hand-coins"><path d="M11 15h2a2 2 0 1 0 0-4h-3c-.6 0-1.1.2-1.4.6L3 17"></path><path d="m7 21 1.6-1.4c.3-.4.8-.6 1.4-.6h4c1.1 0 2.1-.4 2.8-1.2l4.6-4.4a2 2 0 0 0-2.75-2.91l-4.2 3.9"></path><path d="m2 16 6 6"></path><circle cx="16" cy="9" r="2.9"></circle><circle cx="6" cy="5" r="3"></circle></svg><span>Finances</span></a></li></ul></div></div></div></div></div><div id="content" class="max-w-full w-full ml-auto peer-data-[state=collapsed]:w-[calc(100%-var(--sidebar-width-icon)-1rem)] peer-data-[state=expanded]:w-[calc(100%-var(--sidebar-width))] transition-[width] ease-linear duration-200 h-svh flex flex-col group-data-[scroll-locked=1]/body:h-full group-data-[scroll-locked=1]/body:has-[main.fixed-main]:h-svh"><header class="flex items-center gap-3 sm:gap-4 bg-header p-4 h-18 shadow-none"><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-7 w-7 scale-125 sm:scale-100" data-sidebar="trigger"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-panel-left"><rect width="18" height="18" x="3" y="3" rx="2"></rect><path d="M9 3v18"></path></svg><span class="sr-only">Toggle Sidebar</span></button><div class="ml-auto flex items-center space-x-4"><button class="inline-flex items-center gap-2 whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border border-input hover:text-accent-foreground px-4 py-2 relative h-8 w-full flex-1 md:flex-none justify-start rounded-md bg-muted/25 hover:bg-muted/50 text-sm font-normal text-muted-foreground shadow-none sm:pr-12 md:w-40 lg:w-56 xl:w-64"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="tabler-icon tabler-icon-search absolute left-1.5 top-1/2 -translate-y-1/2" aria-hidden="true"><path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0"></path><path d="M21 21l-6 -6"></path></svg><span class="ml-3">Search</span><kbd class="pointer-events-none absolute right-[0.3rem] top-[0.3rem] hidden h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 sm:flex"><span class="text-xs">⌘</span>K</kbd></button><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-9 w-9 scale-95 rounded-full" type="button" id="radix-:r8:" aria-haspopup="menu" aria-expanded="false" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="tabler-icon tabler-icon-sun size-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"><path d="M12 12m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0"></path><path d="M3 12h1m8 -9v1m8 8h1m-9 8v1m-6.4 -15.4l.7 .7m12.1 -.7l-.7 .7m0 11.4l.7 .7m-12.1 -.7l-.7 .7"></path></svg><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="tabler-icon tabler-icon-moon absolute size-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"><path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z"></path></svg><span class="sr-only">Toggle theme</span></button><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground px-4 py-2 relative h-8 w-8 rounded-full" type="button" id="radix-:ra:" aria-haspopup="menu" aria-expanded="false" data-state="closed"><span class="relative flex shrink-0 overflow-hidden rounded-full h-8 w-8"><span class="flex h-full w-full items-center justify-center rounded-full bg-muted">L</span></span></button></div></header><main class="peer-[.header-fixed]/header:mt-16 px-4 py-6"></main></div></div><div role="region" aria-label="Notifications (F8)" tabindex="-1" style="pointer-events: none;"><ol tabindex="-1" class="fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]"></ol></div><div class="tsqd-parent-container" style="--tsqd-panel-height: 500px; --tsqd-panel-width: 500px; --tsqd-font-size: 16px;"><div class="go2420793703 tsqd-transitions-container"><div class="go3489369143 go1942079657 tsqd-open-btn-container"><div aria-hidden="true"><svg version="1.0" viewBox="0 0 633 633"><lineargradient x1="-666.45" x2="-666.45" y1="163.28" y2="163.99" gradientTransform="matrix(633 0 0 633 422177 -103358)" gradientUnits="userSpaceOnUse" id="a-cl-0"><stop stop-color="#6BDAFF" offset="0"></stop><stop stop-color="#F9FFB5" offset=".32"></stop><stop stop-color="#FFA770" offset=".71"></stop><stop stop-color="#FF7373" offset="1"></stop></lineargradient><circle cx="316.5" cy="316.5" r="316.5" fill="url(#a-cl-0)"></circle><defs><filter x="-137.5" y="412" width="454" height="396.9" filterUnits="userSpaceOnUse" id="am-cl-0"><fecolormatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></fecolormatrix></filter></defs><mask x="-137.5" y="412" width="454" height="396.9" maskUnits="userSpaceOnUse" id="b-cl-0"><g filter="url(#am-cl-0)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#b-cl-0)"><ellipse cx="89.5" cy="610.5" rx="214.5" ry="186" fill="#015064" stroke="#00CFE2" stroke-width="25"></ellipse></g><defs><filter x="316.5" y="412" width="454" height="396.9" filterUnits="userSpaceOnUse" id="ah-cl-0"><fecolormatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></fecolormatrix></filter></defs><mask x="316.5" y="412" width="454" height="396.9" maskUnits="userSpaceOnUse" id="k-cl-0"><g filter="url(#ah-cl-0)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#k-cl-0)"><ellipse cx="543.5" cy="610.5" rx="214.5" ry="186" fill="#015064" stroke="#00CFE2" stroke-width="25"></ellipse></g><defs><filter x="-137.5" y="450" width="454" height="396.9" filterUnits="userSpaceOnUse" id="ae-cl-0"><fecolormatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></fecolormatrix></filter></defs><mask x="-137.5" y="450" width="454" height="396.9" maskUnits="userSpaceOnUse" id="j-cl-0"><g filter="url(#ae-cl-0)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#j-cl-0)"><ellipse cx="89.5" cy="648.5" rx="214.5" ry="186" fill="#015064" stroke="#00A8B8" stroke-width="25"></ellipse></g><defs><filter x="316.5" y="450" width="454" height="396.9" filterUnits="userSpaceOnUse" id="ai-cl-0"><fecolormatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></fecolormatrix></filter></defs><mask x="316.5" y="450" width="454" height="396.9" maskUnits="userSpaceOnUse" id="i-cl-0"><g filter="url(#ai-cl-0)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#i-cl-0)"><ellipse cx="543.5" cy="648.5" rx="214.5" ry="186" fill="#015064" stroke="#00A8B8" stroke-width="25"></ellipse></g><defs><filter x="-137.5" y="486" width="454" height="396.9" filterUnits="userSpaceOnUse" id="aj-cl-0"><fecolormatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></fecolormatrix></filter></defs><mask x="-137.5" y="486" width="454" height="396.9" maskUnits="userSpaceOnUse" id="h-cl-0"><g filter="url(#aj-cl-0)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#h-cl-0)"><ellipse cx="89.5" cy="684.5" rx="214.5" ry="186" fill="#015064" stroke="#007782" stroke-width="25"></ellipse></g><defs><filter x="316.5" y="486" width="454" height="396.9" filterUnits="userSpaceOnUse" id="ag-cl-0"><fecolormatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></fecolormatrix></filter></defs><mask x="316.5" y="486" width="454" height="396.9" maskUnits="userSpaceOnUse" id="g-cl-0"><g filter="url(#ag-cl-0)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#g-cl-0)"><ellipse cx="543.5" cy="684.5" rx="214.5" ry="186" fill="#015064" stroke="#007782" stroke-width="25"></ellipse></g><defs><filter x="272.2" y="308" width="176.9" height="129.3" filterUnits="userSpaceOnUse" id="af-cl-0"><fecolormatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></fecolormatrix></filter></defs><mask x="272.2" y="308" width="176.9" height="129.3" maskUnits="userSpaceOnUse" id="f-cl-0"><g filter="url(#af-cl-0)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#f-cl-0)"><line x1="436" x2="431" y1="403.2" y2="431.8" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="11"></line><line x1="291" x2="280" y1="341.5" y2="403.5" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="11"></line><line x1="332.9" x2="328.6" y1="384.1" y2="411.2" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="11"></line><lineargradient x1="-670.75" x2="-671.59" y1="164.4" y2="164.49" gradientTransform="matrix(-184.16 -32.472 -11.461 64.997 -121359 -32126)" gradientUnits="userSpaceOnUse" id="m-cl-0"><stop stop-color="#EE2700" offset="0"></stop><stop stop-color="#FF008E" offset="1"></stop></lineargradient><path d="m344.1 363 97.7 17.2c5.8 2.1 8.2 6.1 7.1 12.1s-4.7 9.2-11 9.9l-106-18.7-57.5-59.2c-3.2-4.8-2.9-9.1 0.8-12.8s8.3-4.4 13.7-2.1l55.2 53.6z" clip-rule="evenodd" fill-rule="evenodd" fill="url(#m-cl-0)"></path><line x1="428.2" x2="429.1" y1="384.5" y2="378" fill="none" stroke="#fff" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7"></line><line x1="395.2" x2="396.1" y1="379.5" y2="373" fill="none" stroke="#fff" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7"></line><line x1="362.2" x2="363.1" y1="373.5" y2="367.4" fill="none" stroke="#fff" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7"></line><line x1="324.2" x2="328.4" y1="351.3" y2="347.4" fill="none" stroke="#fff" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7"></line><line x1="303.2" x2="307.4" y1="331.3" y2="327.4" fill="none" stroke="#fff" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7"></line></g><defs><filter x="73.2" y="113.8" width="280.6" height="317.4" filterUnits="userSpaceOnUse" id="ak-cl-0"><fecolormatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></fecolormatrix></filter></defs><mask x="73.2" y="113.8" width="280.6" height="317.4" maskUnits="userSpaceOnUse" id="e-cl-0"><g filter="url(#ak-cl-0)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#e-cl-0)"><lineargradient x1="-672.16" x2="-672.16" y1="165.03" y2="166.03" gradientTransform="matrix(-100.18 48.861 97.976 200.88 -83342 -93.059)" gradientUnits="userSpaceOnUse" id="n-cl-0"><stop stop-color="#A17500" offset="0"></stop><stop stop-color="#5D2100" offset="1"></stop></lineargradient><path d="m192.3 203c8.1 37.3 14 73.6 17.8 109.1 3.8 35.4 2.8 75.1-3 119.2l61.2-16.7c-15.6-59-25.2-97.9-28.6-116.6s-10.8-51.9-22.1-99.6l-25.3 4.6" clip-rule="evenodd" fill-rule="evenodd" fill="url(#n-cl-0)"></path><g stroke="#2F8A00"><lineargradient x1="-660.23" x2="-660.23" y1="166.72" y2="167.72" gradientTransform="matrix(92.683 4.8573 -2.0259 38.657 61680 -3088.6)" gradientUnits="userSpaceOnUse" id="r-cl-0"><stop stop-color="#2F8A00" offset="0"></stop><stop stop-color="#90FF57" offset="1"></stop></lineargradient><path d="m195 183.9s-12.6-22.1-36.5-29.9c-15.9-5.2-34.4-1.5-55.5 11.1 15.9 14.3 29.5 22.6 40.7 24.9 16.8 3.6 51.3-6.1 51.3-6.1z" clip-rule="evenodd" fill-rule="evenodd" stroke-width="13" fill="url(#r-cl-0)"></path><lineargradient x1="-661.36" x2="-661.36" y1="164.18" y2="165.18" gradientTransform="matrix(110 5.7648 -6.3599 121.35 73933 -15933)" gradientUnits="userSpaceOnUse" id="s-cl-0"><stop stop-color="#2F8A00" offset="0"></stop><stop stop-color="#90FF57" offset="1"></stop></lineargradient><path d="m194.9 184.5s-47.5-8.5-83.2 15.7c-23.8 16.2-34.3 49.3-31.6 99.4 30.3-27.8 52.1-48.5 65.2-61.9 19.8-20.2 49.6-53.2 49.6-53.2z" clip-rule="evenodd" fill-rule="evenodd" stroke-width="13" fill="url(#s-cl-0)"></path><lineargradient x1="-656.79" x2="-656.79" y1="165.15" y2="166.15" gradientTransform="matrix(62.954 3.2993 -3.5023 66.828 42156 -8754.1)" gradientUnits="userSpaceOnUse" id="q-cl-0"><stop stop-color="#2F8A00" offset="0"></stop><stop stop-color="#90FF57" offset="1"></stop></lineargradient><path d="m195 183.9c-0.8-21.9 6-38 20.6-48.2s29.8-15.4 45.5-15.3c-6.1 21.4-14.5 35.8-25.2 43.4s-24.4 14.2-40.9 20.1z" clip-rule="evenodd" fill-rule="evenodd" stroke-width="13" fill="url(#q-cl-0)"></path><lineargradient x1="-663.07" x2="-663.07" y1="165.44" y2="166.44" gradientTransform="matrix(152.47 7.9907 -3.0936 59.029 101884 -4318.7)" gradientUnits="userSpaceOnUse" id="p-cl-0"><stop stop-color="#2F8A00" offset="0"></stop><stop stop-color="#90FF57" offset="1"></stop></lineargradient><path d="m194.9 184.5c31.9-30 64.1-39.7 96.7-29s50.8 30.4 54.6 59.1c-35.2-5.5-60.4-9.6-75.8-12.1-15.3-2.6-40.5-8.6-75.5-18z" clip-rule="evenodd" fill-rule="evenodd" stroke-width="13" fill="url(#p-cl-0)"></path><lineargradient x1="-662.57" x2="-662.57" y1="164.44" y2="165.44" gradientTransform="matrix(136.46 7.1517 -5.2163 99.533 91536 -11442)" gradientUnits="userSpaceOnUse" id="o-cl-0"><stop stop-color="#2F8A00" offset="0"></stop><stop stop-color="#90FF57" offset="1"></stop></lineargradient><path d="m194.9 184.5c35.8-7.6 65.6-0.2 89.2 22s37.7 49 42.3 80.3c-39.8-9.7-68.3-23.8-85.5-42.4s-32.5-38.5-46-59.9z" clip-rule="evenodd" fill-rule="evenodd" stroke-width="13" fill="url(#o-cl-0)"></path><lineargradient x1="-656.43" x2="-656.43" y1="163.86" y2="164.86" gradientTransform="matrix(60.866 3.1899 -8.7773 167.48 41560 -25168)" gradientUnits="userSpaceOnUse" id="l-cl-0"><stop stop-color="#2F8A00" offset="0"></stop><stop stop-color="#90FF57" offset="1"></stop></lineargradient><path d="m194.9 184.5c-33.6 13.8-53.6 35.7-60.1 65.6s-3.6 63.1 8.7 99.6c27.4-40.3 43.2-69.6 47.4-88s5.6-44.1 4-77.2z" clip-rule="evenodd" fill-rule="evenodd" stroke-width="13" fill="url(#l-cl-0)"></path><path d="m196.5 182.3c-14.8 21.6-25.1 41.4-30.8 59.4s-9.5 33-11.1 45.1" fill="none" stroke-linecap="round" stroke-width="8"></path><path d="m194.9 185.7c-24.4 1.7-43.8 9-58.1 21.8s-24.7 25.4-31.3 37.8" fill="none" stroke-linecap="round" stroke-width="8"></path><path d="m204.5 176.4c29.7-6.7 52-8.4 67-5.1s26.9 8.6 35.8 15.9" fill="none" stroke-linecap="round" stroke-width="8"></path><path d="m196.5 181.4c20.3 9.9 38.2 20.5 53.9 31.9s27.4 22.1 35.1 32" fill="none" stroke-linecap="round" stroke-width="8"></path></g></g><defs><filter x="50.5" y="399" width="532" height="633" filterUnits="userSpaceOnUse" id="al-cl-0"><fecolormatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></fecolormatrix></filter></defs><mask x="50.5" y="399" width="532" height="633" maskUnits="userSpaceOnUse" id="d-cl-0"><g filter="url(#al-cl-0)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#d-cl-0)"><lineargradient x1="-666.06" x2="-666.23" y1="163.36" y2="163.75" gradientTransform="matrix(532 0 0 633 354760 -102959)" gradientUnits="userSpaceOnUse" id="u-cl-0"><stop stop-color="#FFF400" offset="0"></stop><stop stop-color="#3C8700" offset="1"></stop></lineargradient><ellipse cx="316.5" cy="715.5" rx="266" ry="316.5" fill="url(#u-cl-0)"></ellipse></g><defs><filter x="391" y="-24" width="288" height="283" filterUnits="userSpaceOnUse" id="ad-cl-0"><fecolormatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></fecolormatrix></filter></defs><mask x="391" y="-24" width="288" height="283" maskUnits="userSpaceOnUse" id="c-cl-0"><g filter="url(#ad-cl-0)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#c-cl-0)"><lineargradient x1="-664.56" x2="-664.56" y1="163.79" y2="164.79" gradientTransform="matrix(227 0 0 227 151421 -37204)" gradientUnits="userSpaceOnUse" id="t-cl-0"><stop stop-color="#FFDF00" offset="0"></stop><stop stop-color="#FF9D00" offset="1"></stop></lineargradient><circle cx="565.5" cy="89.5" r="113.5" fill="url(#t-cl-0)"></circle><lineargradient x1="-644.5" x2="-645.77" y1="342" y2="342" gradientTransform="matrix(30 0 0 1 19770 -253)" gradientUnits="userSpaceOnUse" id="v-cl-0"><stop stop-color="#FFA400" offset="0"></stop><stop stop-color="#FF5E00" offset="1"></stop></lineargradient><line x1="427" x2="397" y1="89" y2="89" fill="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" stroke="url(#v-cl-0)"></line><lineargradient x1="-641.56" x2="-642.83" y1="196.02" y2="196.07" gradientTransform="matrix(26.5 0 0 5.5 17439 -1025.5)" gradientUnits="userSpaceOnUse" id="aa-cl-0"><stop stop-color="#FFA400" offset="0"></stop><stop stop-color="#FF5E00" offset="1"></stop></lineargradient><line x1="430.5" x2="404" y1="55.5" y2="50" fill="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" stroke="url(#aa-cl-0)"></line><lineargradient x1="-643.73" x2="-645" y1="185.83" y2="185.9" gradientTransform="matrix(29 0 0 8 19107 -1361)" gradientUnits="userSpaceOnUse" id="w-cl-0"><stop stop-color="#FFA400" offset="0"></stop><stop stop-color="#FF5E00" offset="1"></stop></lineargradient><line x1="431" x2="402" y1="122" y2="130" fill="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" stroke="url(#w-cl-0)"></line><lineargradient x1="-638.94" x2="-640.22" y1="177.09" y2="177.39" gradientTransform="matrix(24 0 0 13 15783 -2145)" gradientUnits="userSpaceOnUse" id="ac-cl-0"><stop stop-color="#FFA400" offset="0"></stop><stop stop-color="#FF5E00" offset="1"></stop></lineargradient><line x1="442" x2="418" y1="153" y2="166" fill="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" stroke="url(#ac-cl-0)"></line><lineargradient x1="-633.42" x2="-634.7" y1="172.41" y2="173.31" gradientTransform="matrix(20 0 0 19 13137 -3096)" gradientUnits="userSpaceOnUse" id="ab-cl-0"><stop stop-color="#FFA400" offset="0"></stop><stop stop-color="#FF5E00" offset="1"></stop></lineargradient><line x1="464" x2="444" y1="180" y2="199" fill="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" stroke="url(#ab-cl-0)"></line><lineargradient x1="-619.05" x2="-619.52" y1="170.82" y2="171.82" gradientTransform="matrix(13.83 0 0 22.85 9050 -3703.4)" gradientUnits="userSpaceOnUse" id="y-cl-0"><stop stop-color="#FFA400" offset="0"></stop><stop stop-color="#FF5E00" offset="1"></stop></lineargradient><line x1="491.4" x2="477.5" y1="203" y2="225.9" fill="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" stroke="url(#y-cl-0)"></line><lineargradient x1="-578.5" x2="-578.63" y1="170.31" y2="171.31" gradientTransform="matrix(7.5 0 0 24.5 4860 -3953)" gradientUnits="userSpaceOnUse" id="x-cl-0"><stop stop-color="#FFA400" offset="0"></stop><stop stop-color="#FF5E00" offset="1"></stop></lineargradient><line x1="524.5" x2="517" y1="219.5" y2="244" fill="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" stroke="url(#x-cl-0)"></line><lineargradient x1="666.5" x2="666.5" y1="170.31" y2="171.31" gradientTransform="matrix(.5 0 0 24.5 231.5 -3944)" gradientUnits="userSpaceOnUse" id="z-cl-0"><stop stop-color="#FFA400" offset="0"></stop><stop stop-color="#FF5E00" offset="1"></stop></lineargradient><line x1="564.5" x2="565" y1="228.5" y2="253" fill="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" stroke="url(#z-cl-0)"></line></g></svg></div><button type="button" aria-label="Open Tanstack query devtools" class="tsqd-open-btn"><svg version="1.0" viewBox="0 0 633 633"><lineargradient x1="-666.45" x2="-666.45" y1="163.28" y2="163.99" gradientTransform="matrix(633 0 0 633 422177 -103358)" gradientUnits="userSpaceOnUse" id="a-cl-1"><stop stop-color="#6BDAFF" offset="0"></stop><stop stop-color="#F9FFB5" offset=".32"></stop><stop stop-color="#FFA770" offset=".71"></stop><stop stop-color="#FF7373" offset="1"></stop></lineargradient><circle cx="316.5" cy="316.5" r="316.5" fill="url(#a-cl-1)"></circle><defs><filter x="-137.5" y="412" width="454" height="396.9" filterUnits="userSpaceOnUse" id="am-cl-1"><fecolormatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></fecolormatrix></filter></defs><mask x="-137.5" y="412" width="454" height="396.9" maskUnits="userSpaceOnUse" id="b-cl-1"><g filter="url(#am-cl-1)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#b-cl-1)"><ellipse cx="89.5" cy="610.5" rx="214.5" ry="186" fill="#015064" stroke="#00CFE2" stroke-width="25"></ellipse></g><defs><filter x="316.5" y="412" width="454" height="396.9" filterUnits="userSpaceOnUse" id="ah-cl-1"><fecolormatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></fecolormatrix></filter></defs><mask x="316.5" y="412" width="454" height="396.9" maskUnits="userSpaceOnUse" id="k-cl-1"><g filter="url(#ah-cl-1)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#k-cl-1)"><ellipse cx="543.5" cy="610.5" rx="214.5" ry="186" fill="#015064" stroke="#00CFE2" stroke-width="25"></ellipse></g><defs><filter x="-137.5" y="450" width="454" height="396.9" filterUnits="userSpaceOnUse" id="ae-cl-1"><fecolormatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></fecolormatrix></filter></defs><mask x="-137.5" y="450" width="454" height="396.9" maskUnits="userSpaceOnUse" id="j-cl-1"><g filter="url(#ae-cl-1)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#j-cl-1)"><ellipse cx="89.5" cy="648.5" rx="214.5" ry="186" fill="#015064" stroke="#00A8B8" stroke-width="25"></ellipse></g><defs><filter x="316.5" y="450" width="454" height="396.9" filterUnits="userSpaceOnUse" id="ai-cl-1"><fecolormatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></fecolormatrix></filter></defs><mask x="316.5" y="450" width="454" height="396.9" maskUnits="userSpaceOnUse" id="i-cl-1"><g filter="url(#ai-cl-1)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#i-cl-1)"><ellipse cx="543.5" cy="648.5" rx="214.5" ry="186" fill="#015064" stroke="#00A8B8" stroke-width="25"></ellipse></g><defs><filter x="-137.5" y="486" width="454" height="396.9" filterUnits="userSpaceOnUse" id="aj-cl-1"><fecolormatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></fecolormatrix></filter></defs><mask x="-137.5" y="486" width="454" height="396.9" maskUnits="userSpaceOnUse" id="h-cl-1"><g filter="url(#aj-cl-1)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#h-cl-1)"><ellipse cx="89.5" cy="684.5" rx="214.5" ry="186" fill="#015064" stroke="#007782" stroke-width="25"></ellipse></g><defs><filter x="316.5" y="486" width="454" height="396.9" filterUnits="userSpaceOnUse" id="ag-cl-1"><fecolormatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></fecolormatrix></filter></defs><mask x="316.5" y="486" width="454" height="396.9" maskUnits="userSpaceOnUse" id="g-cl-1"><g filter="url(#ag-cl-1)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#g-cl-1)"><ellipse cx="543.5" cy="684.5" rx="214.5" ry="186" fill="#015064" stroke="#007782" stroke-width="25"></ellipse></g><defs><filter x="272.2" y="308" width="176.9" height="129.3" filterUnits="userSpaceOnUse" id="af-cl-1"><fecolormatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></fecolormatrix></filter></defs><mask x="272.2" y="308" width="176.9" height="129.3" maskUnits="userSpaceOnUse" id="f-cl-1"><g filter="url(#af-cl-1)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#f-cl-1)"><line x1="436" x2="431" y1="403.2" y2="431.8" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="11"></line><line x1="291" x2="280" y1="341.5" y2="403.5" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="11"></line><line x1="332.9" x2="328.6" y1="384.1" y2="411.2" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="11"></line><lineargradient x1="-670.75" x2="-671.59" y1="164.4" y2="164.49" gradientTransform="matrix(-184.16 -32.472 -11.461 64.997 -121359 -32126)" gradientUnits="userSpaceOnUse" id="m-cl-1"><stop stop-color="#EE2700" offset="0"></stop><stop stop-color="#FF008E" offset="1"></stop></lineargradient><path d="m344.1 363 97.7 17.2c5.8 2.1 8.2 6.1 7.1 12.1s-4.7 9.2-11 9.9l-106-18.7-57.5-59.2c-3.2-4.8-2.9-9.1 0.8-12.8s8.3-4.4 13.7-2.1l55.2 53.6z" clip-rule="evenodd" fill-rule="evenodd" fill="url(#m-cl-1)"></path><line x1="428.2" x2="429.1" y1="384.5" y2="378" fill="none" stroke="#fff" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7"></line><line x1="395.2" x2="396.1" y1="379.5" y2="373" fill="none" stroke="#fff" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7"></line><line x1="362.2" x2="363.1" y1="373.5" y2="367.4" fill="none" stroke="#fff" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7"></line><line x1="324.2" x2="328.4" y1="351.3" y2="347.4" fill="none" stroke="#fff" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7"></line><line x1="303.2" x2="307.4" y1="331.3" y2="327.4" fill="none" stroke="#fff" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7"></line></g><defs><filter x="73.2" y="113.8" width="280.6" height="317.4" filterUnits="userSpaceOnUse" id="ak-cl-1"><fecolormatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></fecolormatrix></filter></defs><mask x="73.2" y="113.8" width="280.6" height="317.4" maskUnits="userSpaceOnUse" id="e-cl-1"><g filter="url(#ak-cl-1)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#e-cl-1)"><lineargradient x1="-672.16" x2="-672.16" y1="165.03" y2="166.03" gradientTransform="matrix(-100.18 48.861 97.976 200.88 -83342 -93.059)" gradientUnits="userSpaceOnUse" id="n-cl-1"><stop stop-color="#A17500" offset="0"></stop><stop stop-color="#5D2100" offset="1"></stop></lineargradient><path d="m192.3 203c8.1 37.3 14 73.6 17.8 109.1 3.8 35.4 2.8 75.1-3 119.2l61.2-16.7c-15.6-59-25.2-97.9-28.6-116.6s-10.8-51.9-22.1-99.6l-25.3 4.6" clip-rule="evenodd" fill-rule="evenodd" fill="url(#n-cl-1)"></path><g stroke="#2F8A00"><lineargradient x1="-660.23" x2="-660.23" y1="166.72" y2="167.72" gradientTransform="matrix(92.683 4.8573 -2.0259 38.657 61680 -3088.6)" gradientUnits="userSpaceOnUse" id="r-cl-1"><stop stop-color="#2F8A00" offset="0"></stop><stop stop-color="#90FF57" offset="1"></stop></lineargradient><path d="m195 183.9s-12.6-22.1-36.5-29.9c-15.9-5.2-34.4-1.5-55.5 11.1 15.9 14.3 29.5 22.6 40.7 24.9 16.8 3.6 51.3-6.1 51.3-6.1z" clip-rule="evenodd" fill-rule="evenodd" stroke-width="13" fill="url(#r-cl-1)"></path><lineargradient x1="-661.36" x2="-661.36" y1="164.18" y2="165.18" gradientTransform="matrix(110 5.7648 -6.3599 121.35 73933 -15933)" gradientUnits="userSpaceOnUse" id="s-cl-1"><stop stop-color="#2F8A00" offset="0"></stop><stop stop-color="#90FF57" offset="1"></stop></lineargradient><path d="m194.9 184.5s-47.5-8.5-83.2 15.7c-23.8 16.2-34.3 49.3-31.6 99.4 30.3-27.8 52.1-48.5 65.2-61.9 19.8-20.2 49.6-53.2 49.6-53.2z" clip-rule="evenodd" fill-rule="evenodd" stroke-width="13" fill="url(#s-cl-1)"></path><lineargradient x1="-656.79" x2="-656.79" y1="165.15" y2="166.15" gradientTransform="matrix(62.954 3.2993 -3.5023 66.828 42156 -8754.1)" gradientUnits="userSpaceOnUse" id="q-cl-1"><stop stop-color="#2F8A00" offset="0"></stop><stop stop-color="#90FF57" offset="1"></stop></lineargradient><path d="m195 183.9c-0.8-21.9 6-38 20.6-48.2s29.8-15.4 45.5-15.3c-6.1 21.4-14.5 35.8-25.2 43.4s-24.4 14.2-40.9 20.1z" clip-rule="evenodd" fill-rule="evenodd" stroke-width="13" fill="url(#q-cl-1)"></path><lineargradient x1="-663.07" x2="-663.07" y1="165.44" y2="166.44" gradientTransform="matrix(152.47 7.9907 -3.0936 59.029 101884 -4318.7)" gradientUnits="userSpaceOnUse" id="p-cl-1"><stop stop-color="#2F8A00" offset="0"></stop><stop stop-color="#90FF57" offset="1"></stop></lineargradient><path d="m194.9 184.5c31.9-30 64.1-39.7 96.7-29s50.8 30.4 54.6 59.1c-35.2-5.5-60.4-9.6-75.8-12.1-15.3-2.6-40.5-8.6-75.5-18z" clip-rule="evenodd" fill-rule="evenodd" stroke-width="13" fill="url(#p-cl-1)"></path><lineargradient x1="-662.57" x2="-662.57" y1="164.44" y2="165.44" gradientTransform="matrix(136.46 7.1517 -5.2163 99.533 91536 -11442)" gradientUnits="userSpaceOnUse" id="o-cl-1"><stop stop-color="#2F8A00" offset="0"></stop><stop stop-color="#90FF57" offset="1"></stop></lineargradient><path d="m194.9 184.5c35.8-7.6 65.6-0.2 89.2 22s37.7 49 42.3 80.3c-39.8-9.7-68.3-23.8-85.5-42.4s-32.5-38.5-46-59.9z" clip-rule="evenodd" fill-rule="evenodd" stroke-width="13" fill="url(#o-cl-1)"></path><lineargradient x1="-656.43" x2="-656.43" y1="163.86" y2="164.86" gradientTransform="matrix(60.866 3.1899 -8.7773 167.48 41560 -25168)" gradientUnits="userSpaceOnUse" id="l-cl-1"><stop stop-color="#2F8A00" offset="0"></stop><stop stop-color="#90FF57" offset="1"></stop></lineargradient><path d="m194.9 184.5c-33.6 13.8-53.6 35.7-60.1 65.6s-3.6 63.1 8.7 99.6c27.4-40.3 43.2-69.6 47.4-88s5.6-44.1 4-77.2z" clip-rule="evenodd" fill-rule="evenodd" stroke-width="13" fill="url(#l-cl-1)"></path><path d="m196.5 182.3c-14.8 21.6-25.1 41.4-30.8 59.4s-9.5 33-11.1 45.1" fill="none" stroke-linecap="round" stroke-width="8"></path><path d="m194.9 185.7c-24.4 1.7-43.8 9-58.1 21.8s-24.7 25.4-31.3 37.8" fill="none" stroke-linecap="round" stroke-width="8"></path><path d="m204.5 176.4c29.7-6.7 52-8.4 67-5.1s26.9 8.6 35.8 15.9" fill="none" stroke-linecap="round" stroke-width="8"></path><path d="m196.5 181.4c20.3 9.9 38.2 20.5 53.9 31.9s27.4 22.1 35.1 32" fill="none" stroke-linecap="round" stroke-width="8"></path></g></g><defs><filter x="50.5" y="399" width="532" height="633" filterUnits="userSpaceOnUse" id="al-cl-1"><fecolormatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></fecolormatrix></filter></defs><mask x="50.5" y="399" width="532" height="633" maskUnits="userSpaceOnUse" id="d-cl-1"><g filter="url(#al-cl-1)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#d-cl-1)"><lineargradient x1="-666.06" x2="-666.23" y1="163.36" y2="163.75" gradientTransform="matrix(532 0 0 633 354760 -102959)" gradientUnits="userSpaceOnUse" id="u-cl-1"><stop stop-color="#FFF400" offset="0"></stop><stop stop-color="#3C8700" offset="1"></stop></lineargradient><ellipse cx="316.5" cy="715.5" rx="266" ry="316.5" fill="url(#u-cl-1)"></ellipse></g><defs><filter x="391" y="-24" width="288" height="283" filterUnits="userSpaceOnUse" id="ad-cl-1"><fecolormatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></fecolormatrix></filter></defs><mask x="391" y="-24" width="288" height="283" maskUnits="userSpaceOnUse" id="c-cl-1"><g filter="url(#ad-cl-1)"><circle cx="316.5" cy="316.5" r="316.5" fill="#fff"></circle></g></mask><g mask="url(#c-cl-1)"><lineargradient x1="-664.56" x2="-664.56" y1="163.79" y2="164.79" gradientTransform="matrix(227 0 0 227 151421 -37204)" gradientUnits="userSpaceOnUse" id="t-cl-1"><stop stop-color="#FFDF00" offset="0"></stop><stop stop-color="#FF9D00" offset="1"></stop></lineargradient><circle cx="565.5" cy="89.5" r="113.5" fill="url(#t-cl-1)"></circle><lineargradient x1="-644.5" x2="-645.77" y1="342" y2="342" gradientTransform="matrix(30 0 0 1 19770 -253)" gradientUnits="userSpaceOnUse" id="v-cl-1"><stop stop-color="#FFA400" offset="0"></stop><stop stop-color="#FF5E00" offset="1"></stop></lineargradient><line x1="427" x2="397" y1="89" y2="89" fill="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" stroke="url(#v-cl-1)"></line><lineargradient x1="-641.56" x2="-642.83" y1="196.02" y2="196.07" gradientTransform="matrix(26.5 0 0 5.5 17439 -1025.5)" gradientUnits="userSpaceOnUse" id="aa-cl-1"><stop stop-color="#FFA400" offset="0"></stop><stop stop-color="#FF5E00" offset="1"></stop></lineargradient><line x1="430.5" x2="404" y1="55.5" y2="50" fill="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" stroke="url(#aa-cl-1)"></line><lineargradient x1="-643.73" x2="-645" y1="185.83" y2="185.9" gradientTransform="matrix(29 0 0 8 19107 -1361)" gradientUnits="userSpaceOnUse" id="w-cl-1"><stop stop-color="#FFA400" offset="0"></stop><stop stop-color="#FF5E00" offset="1"></stop></lineargradient><line x1="431" x2="402" y1="122" y2="130" fill="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" stroke="url(#w-cl-1)"></line><lineargradient x1="-638.94" x2="-640.22" y1="177.09" y2="177.39" gradientTransform="matrix(24 0 0 13 15783 -2145)" gradientUnits="userSpaceOnUse" id="ac-cl-1"><stop stop-color="#FFA400" offset="0"></stop><stop stop-color="#FF5E00" offset="1"></stop></lineargradient><line x1="442" x2="418" y1="153" y2="166" fill="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" stroke="url(#ac-cl-1)"></line><lineargradient x1="-633.42" x2="-634.7" y1="172.41" y2="173.31" gradientTransform="matrix(20 0 0 19 13137 -3096)" gradientUnits="userSpaceOnUse" id="ab-cl-1"><stop stop-color="#FFA400" offset="0"></stop><stop stop-color="#FF5E00" offset="1"></stop></lineargradient><line x1="464" x2="444" y1="180" y2="199" fill="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" stroke="url(#ab-cl-1)"></line><lineargradient x1="-619.05" x2="-619.52" y1="170.82" y2="171.82" gradientTransform="matrix(13.83 0 0 22.85 9050 -3703.4)" gradientUnits="userSpaceOnUse" id="y-cl-1"><stop stop-color="#FFA400" offset="0"></stop><stop stop-color="#FF5E00" offset="1"></stop></lineargradient><line x1="491.4" x2="477.5" y1="203" y2="225.9" fill="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" stroke="url(#y-cl-1)"></line><lineargradient x1="-578.5" x2="-578.63" y1="170.31" y2="171.31" gradientTransform="matrix(7.5 0 0 24.5 4860 -3953)" gradientUnits="userSpaceOnUse" id="x-cl-1"><stop stop-color="#FFA400" offset="0"></stop><stop stop-color="#FF5E00" offset="1"></stop></lineargradient><line x1="524.5" x2="517" y1="219.5" y2="244" fill="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" stroke="url(#x-cl-1)"></line><lineargradient x1="666.5" x2="666.5" y1="170.31" y2="171.31" gradientTransform="matrix(.5 0 0 24.5 231.5 -3944)" gradientUnits="userSpaceOnUse" id="z-cl-1"><stop stop-color="#FFA400" offset="0"></stop><stop stop-color="#FF5E00" offset="1"></stop></lineargradient><line x1="564.5" x2="565" y1="228.5" y2="253" fill="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" stroke="url(#z-cl-1)"></line></g></svg></button></div></div></div><footer class="TanStackRouterDevtools" style="--tsrd-font-size: 16px;"><div class="go1688509379 TanStackRouterDevtoolsPanel go905199096 go1639696032 go3383159955 go844172998" style="height: 500px;"><div class="go1505018836"></div><button class="go2224423957"><svg xmlns="http://www.w3.org/2000/svg" width="10" height="6" fill="none" viewBox="0 0 10 6" class="go959131266"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.667" d="M1 1l4 4 4-4"></path></svg></button><div class="go1468579934"><div class="go1648261519"><button aria-hidden="true" class="go283257069"><div class="go151905477">TANSTACK</div><div class="go2259959382">React Router v1</div></button></div><div class="go3747950898"><div class="go2041761433"><div class="go898539426"><button class="go1058205856"><span class="go2272256769"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none" viewBox="0 0 24 24" class="go3879208559"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 18l6-6-6-6"></path></svg></span>Router<span class="go2916152260">10 items</span></button><div class="go1244333964"><div class="go898539426"><button class="go1058205856"><span class="go2272256769"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none" viewBox="0 0 24 24" class="go3879208559"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 18l6-6-6-6"></path></svg></span>state<span class="go2916152260">10 items</span></button><div class="go1244333964"><div class="go898539426"><span>loadedAt:</span> <span class="go1863549978">1737955052420</span></div><div class="go898539426"><span>isLoading:</span> <span class="go1863549978">false</span></div><div class="go898539426"><span>isTransitioning:</span> <span class="go1863549978">false</span></div><div class="go898539426"><span>status:</span> <span class="go1863549978">"idle"</span></div><div class="go898539426"><button class="go1058205856"><span class="go2272256769"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none" viewBox="0 0 24 24" class="go1560190766"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 18l6-6-6-6"></path></svg></span>resolvedLocation<span class="go2916152260">6 items</span></button></div><div class="go898539426"><button class="go1058205856"><span class="go2272256769"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none" viewBox="0 0 24 24" class="go1560190766"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 18l6-6-6-6"></path></svg></span>location<span class="go2916152260">6 items</span></button></div><div class="go898539426"><button class="go1058205856"><span class="go2272256769"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none" viewBox="0 0 24 24" class="go1560190766"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 18l6-6-6-6"></path></svg></span>matches<span class="go2916152260">3 items</span></button></div><div class="go898539426"><span>pendingMatches:</span> <span class="go1863549978"></span></div><div class="go898539426"><span>cachedMatches:</span> <span class="go1863549978">[]</span></div><div class="go898539426"><span>statusCode:</span> <span class="go1863549978">200</span></div></div></div><div class="go898539426"><button class="go1058205856"><span class="go2272256769"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none" viewBox="0 0 24 24" class="go1560190766"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 18l6-6-6-6"></path></svg></span>routesById<span class="go2916152260">25 items</span></button></div><div class="go898539426"><button class="go1058205856"><span class="go2272256769"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none" viewBox="0 0 24 24" class="go1560190766"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 18l6-6-6-6"></path></svg></span>routesByPath<span class="go2916152260">21 items</span></button></div><div class="go898539426"><button class="go1058205856"><span class="go2272256769"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none" viewBox="0 0 24 24" class="go1560190766"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 18l6-6-6-6"></path></svg></span>flatRoutes<span class="go2916152260">23 items</span></button></div><div class="go898539426"><button class="go1058205856"><span class="go2272256769"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none" viewBox="0 0 24 24" class="go3879208559"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 18l6-6-6-6"></path></svg></span>options<span class="go2916152260">10 items</span></button><div class="go1244333964"><div class="go898539426"><span>defaultPreloadDelay:</span> <span class="go1863549978">50</span></div><div class="go898539426"><span>defaultPendingMs:</span> <span class="go1863549978">1000</span></div><div class="go898539426"><span>defaultPendingMinMs:</span> <span class="go1863549978">500</span></div><div class="go898539426"><button class="go1058205856"><span class="go2272256769"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none" viewBox="0 0 24 24" class="go1560190766"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 18l6-6-6-6"></path></svg></span>context<span class="go2916152260">1 item</span></button></div><div class="go898539426"><button class="go1058205856"><span class="go2272256769"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="none" viewBox="0 0 24 24" class="go1560190766"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 18l6-6-6-6"></path></svg></span>routeTree<span class="go2916152260">13 items</span></button></div><div class="go898539426"><span>defaultPreload:</span> <span class="go1863549978">"intent"</span></div><div class="go898539426"><span>defaultPreloadStaleTime:</span> <span class="go1863549978">0</span></div><div class="go898539426"><span>caseSensitive:</span> <span class="go1863549978">false</span></div><div class="go898539426"><span>notFoundMode:</span> <span class="go1863549978">"fuzzy"</span></div><div class="go898539426"><span>transformer:</span> <span class="go1863549978">{}</span></div></div></div><div class="go898539426"><span>isViewTransitionTypesSupported:</span> <span class="go1863549978">true</span></div><div class="go898539426"><span>streamedKeys:</span> <span class="go1863549978">{}</span></div><div class="go898539426"><span>isServer:</span> <span class="go1863549978">false</span></div><div class="go898539426"><span>pathParamsDecodeCharMap:</span> <span class="go1863549978"></span></div><div class="go898539426"><span>commitLocationPromise:</span> <span class="go1863549978"></span></div></div></div></div></div></div><div class="go1468579934"><div class="go1560856704"><div class="go2273422943"><span>Pathname</span></div><div class="go3889393570"><code>/</code></div><div class="go2273422943"><div class="go3620606704"><button type="button" disabled="" class="go3643664686 go2107851696 go1232428028">Routes</button><button type="button" class="go3643664686 go862916629">Matches</button></div><div class="go3993070631"><div>age / staleTime / gcTime</div></div></div><div class="go427126709"><div><div role="button" aria-label="Open match details for __root__" class="go3515246205"><div class="go4274307370"></div><div class="go339260511"><div><code class="go1246236501">__root__ </code><code class="go263506863"></code></div></div></div><div class="go3874423951"><div><div role="button" aria-label="Open match details for /_authenticated" class="go3515246205"><div class="go4274307370"></div><div class="go339260511"><div><code class="go1246236501">_authenticated </code><code class="go263506863"></code></div></div></div><div class="go3954429975"><div><div role="button" aria-label="Open match details for /_authenticated/apps/" class="go1091339057"><div class="go1960959592 go3595285728"></div><div class="go339260511 go3*********"><div><code class="go1246236501">apps/ </code><code class="go263506863"></code></div></div></div></div><div><div role="button" aria-label="Open match details for /_authenticated/chats/" class="go1091339057 go2042539364"><div class="go1960959592 go3595285728"></div><div class="go339260511 go3*********"><div><code class="go1246236501">chats/ </code><code class="go263506863"></code></div></div></div></div><div><div role="button" aria-label="Open match details for /_authenticated/help-center/" class="go1091339057"><div class="go1960959592 go3595285728"></div><div class="go339260511 go3*********"><div><code class="go1246236501">help-center/ </code><code class="go263506863"></code></div></div></div></div><div><div role="button" aria-label="Open match details for /_authenticated/tasks/" class="go1091339057"><div class="go1960959592 go3595285728"></div><div class="go339260511 go3*********"><div><code class="go1246236501">tasks/ </code><code class="go263506863"></code></div></div></div></div><div><div role="button" aria-label="Open match details for /_authenticated/users/" class="go1091339057"><div class="go1960959592 go3595285728"></div><div class="go339260511 go3*********"><div><code class="go1246236501">users/ </code><code class="go263506863"></code></div></div></div></div><div><div role="button" aria-label="Open match details for /_authenticated/settings" class="go1091339057"><div class="go1960959592 go3595285728"></div><div class="go339260511 go3*********"><div><code class="go1246236501">settings </code><code class="go263506863"></code></div></div></div><div class="go3954429975"><div><div role="button" aria-label="Open match details for /_authenticated/settings/account" class="go1091339057"><div class="go1960959592 go3595285728"></div><div class="go339260511 go3*********"><div><code class="go1246236501">account </code><code class="go263506863"></code></div></div></div></div><div><div role="button" aria-label="Open match details for /_authenticated/settings/appearance" class="go1091339057"><div class="go1960959592 go3595285728"></div><div class="go339260511 go3*********"><div><code class="go1246236501">appearance </code><code class="go263506863"></code></div></div></div></div><div><div role="button" aria-label="Open match details for /_authenticated/settings/display" class="go1091339057"><div class="go1960959592 go3595285728"></div><div class="go339260511 go3*********"><div><code class="go1246236501">display </code><code class="go263506863"></code></div></div></div></div><div><div role="button" aria-label="Open match details for /_authenticated/settings/notifications" class="go1091339057"><div class="go1960959592 go3595285728"></div><div class="go339260511 go3*********"><div><code class="go1246236501">notifications </code><code class="go263506863"></code></div></div></div></div><div><div role="button" aria-label="Open match details for /_authenticated/settings/" class="go1091339057"><div class="go1960959592 go3595285728"></div><div class="go339260511 go3*********"><div><code class="go1246236501">/ </code><code class="go263506863"></code></div></div></div></div></div></div><div><div role="button" aria-label="Open match details for /_authenticated/" class="go3515246205"><div class="go4274307370"></div><div class="go339260511"><div><code class="go1246236501">/ </code><code class="go263506863"></code></div></div></div></div></div></div><div><div role="button" aria-label="Open match details for /(errors)/401" class="go1091339057"><div class="go1960959592 go3595285728"></div><div class="go339260511 go3*********"><div><code class="go1246236501">401 </code><code class="go263506863"></code></div></div></div></div><div><div role="button" aria-label="Open match details for /(errors)/403" class="go1091339057"><div class="go1960959592 go3595285728"></div><div class="go339260511 go3*********"><div><code class="go1246236501">403 </code><code class="go263506863"></code></div></div></div></div><div><div role="button" aria-label="Open match details for /(errors)/404" class="go1091339057"><div class="go1960959592 go3595285728"></div><div class="go339260511 go3*********"><div><code class="go1246236501">404 </code><code class="go263506863"></code></div></div></div></div><div><div role="button" aria-label="Open match details for /(auth)/500" class="go1091339057"><div class="go1960959592 go3595285728"></div><div class="go339260511 go3*********"><div><code class="go1246236501">500 </code><code class="go263506863"></code></div></div></div></div><div><div role="button" aria-label="Open match details for /(errors)/500" class="go1091339057"><div class="go1960959592 go3595285728"></div><div class="go339260511 go3*********"><div><code class="go1246236501">500 </code><code class="go263506863"></code></div></div></div></div><div><div role="button" aria-label="Open match details for /(errors)/503" class="go1091339057"><div class="go1960959592 go3595285728"></div><div class="go339260511 go3*********"><div><code class="go1246236501">503 </code><code class="go263506863"></code></div></div></div></div><div><div role="button" aria-label="Open match details for /(auth)/forgot-password" class="go1091339057"><div class="go1960959592 go3595285728"></div><div class="go339260511 go3*********"><div><code class="go1246236501">forgot-password </code><code class="go263506863"></code></div></div></div></div><div><div role="button" aria-label="Open match details for /(auth)/otp" class="go1091339057"><div class="go1960959592 go3595285728"></div><div class="go339260511 go3*********"><div><code class="go1246236501">otp </code><code class="go263506863"></code></div></div></div></div><div><div role="button" aria-label="Open match details for /(auth)/sign-in" class="go1091339057"><div class="go1960959592 go3595285728"></div><div class="go339260511 go3*********"><div><code class="go1246236501">sign-in </code><code class="go263506863"></code></div></div></div></div><div><div role="button" aria-label="Open match details for /(auth)/sign-in-2" class="go1091339057"><div class="go1960959592 go3595285728"></div><div class="go339260511 go3*********"><div><code class="go1246236501">sign-in-2 </code><code class="go263506863"></code></div></div></div></div><div><div role="button" aria-label="Open match details for /(auth)/sign-up" class="go1091339057"><div class="go1960959592 go3595285728"></div><div class="go339260511 go3*********"><div><code class="go1246236501">sign-up </code><code class="go263506863"></code></div></div></div></div></div></div></div></div></div></div><button type="button" aria-label="Open TanStack Router Devtools" class="go1767807125 go2968029118 go2279492678"><div class="go1460193506"><div class="go2552515089"><svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 634 633" viewBox="0 0 634 633"><g transform="translate(1)"><lineargradient id="a-:rf:" x1="-641.486" x2="-641.486" y1="856.648" y2="855.931" gradientTransform="matrix(633 0 0 -633 406377 542258)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#6bdaff"></stop><stop offset="0.319" stop-color="#f9ffb5"></stop><stop offset="0.706" stop-color="#ffa770"></stop><stop offset="1" stop-color="#ff7373"></stop></lineargradient><circle cx="316.5" cy="316.5" r="316.5" fill="url(#a-:rf:)" fill-rule="evenodd" clip-rule="evenodd"></circle><defs><filter id="b-:rf:" width="454" height="396.9" x="-137.5" y="412" filterUnits="userSpaceOnUse"><fecolormatrix values="1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"></fecolormatrix></filter></defs><mask id="c-:rf:" width="454" height="396.9" x="-137.5" y="412" maskUnits="userSpaceOnUse"><g filter="url(#b-:rf:)"><circle cx="316.5" cy="316.5" r="316.5" fill="#FFF" fill-rule="evenodd" clip-rule="evenodd"></circle></g></mask><ellipse cx="89.5" cy="610.5" fill="#015064" fill-rule="evenodd" stroke="#00CFE2" stroke-width="25" clip-rule="evenodd" mask="url(#c-:rf:)" rx="214.5" ry="186"></ellipse><defs><filter id="d-:rf:" width="454" height="396.9" x="316.5" y="412" filterUnits="userSpaceOnUse"><fecolormatrix values="1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"></fecolormatrix></filter></defs><mask id="e-:rf:" width="454" height="396.9" x="316.5" y="412" maskUnits="userSpaceOnUse"><g filter="url(#d-:rf:)"><circle cx="316.5" cy="316.5" r="316.5" fill="#FFF" fill-rule="evenodd" clip-rule="evenodd"></circle></g></mask><ellipse cx="543.5" cy="610.5" fill="#015064" fill-rule="evenodd" stroke="#00CFE2" stroke-width="25" clip-rule="evenodd" mask="url(#e-:rf:)" rx="214.5" ry="186"></ellipse><defs><filter id="f-:rf:" width="454" height="396.9" x="-137.5" y="450" filterUnits="userSpaceOnUse"><fecolormatrix values="1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"></fecolormatrix></filter></defs><mask id="g-:rf:" width="454" height="396.9" x="-137.5" y="450" maskUnits="userSpaceOnUse"><g filter="url(#f-:rf:)"><circle cx="316.5" cy="316.5" r="316.5" fill="#FFF" fill-rule="evenodd" clip-rule="evenodd"></circle></g></mask><ellipse cx="89.5" cy="648.5" fill="#015064" fill-rule="evenodd" stroke="#00A8B8" stroke-width="25" clip-rule="evenodd" mask="url(#g-:rf:)" rx="214.5" ry="186"></ellipse><defs><filter id="h-:rf:" width="454" height="396.9" x="316.5" y="450" filterUnits="userSpaceOnUse"><fecolormatrix values="1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"></fecolormatrix></filter></defs><mask id="i-:rf:" width="454" height="396.9" x="316.5" y="450" maskUnits="userSpaceOnUse"><g filter="url(#h-:rf:)"><circle cx="316.5" cy="316.5" r="316.5" fill="#FFF" fill-rule="evenodd" clip-rule="evenodd"></circle></g></mask><ellipse cx="543.5" cy="648.5" fill="#015064" fill-rule="evenodd" stroke="#00A8B8" stroke-width="25" clip-rule="evenodd" mask="url(#i-:rf:)" rx="214.5" ry="186"></ellipse><defs><filter id="j-:rf:" width="454" height="396.9" x="-137.5" y="486" filterUnits="userSpaceOnUse"><fecolormatrix values="1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"></fecolormatrix></filter></defs><mask id="k-:rf:" width="454" height="396.9" x="-137.5" y="486" maskUnits="userSpaceOnUse"><g filter="url(#j-:rf:)"><circle cx="316.5" cy="316.5" r="316.5" fill="#FFF" fill-rule="evenodd" clip-rule="evenodd"></circle></g></mask><ellipse cx="89.5" cy="684.5" fill="#015064" fill-rule="evenodd" stroke="#007782" stroke-width="25" clip-rule="evenodd" mask="url(#k-:rf:)" rx="214.5" ry="186"></ellipse><defs><filter id="l-:rf:" width="454" height="396.9" x="316.5" y="486" filterUnits="userSpaceOnUse"><fecolormatrix values="1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"></fecolormatrix></filter></defs><mask id="m-:rf:" width="454" height="396.9" x="316.5" y="486" maskUnits="userSpaceOnUse"><g filter="url(#l-:rf:)"><circle cx="316.5" cy="316.5" r="316.5" fill="#FFF" fill-rule="evenodd" clip-rule="evenodd"></circle></g></mask><ellipse cx="543.5" cy="684.5" fill="#015064" fill-rule="evenodd" stroke="#007782" stroke-width="25" clip-rule="evenodd" mask="url(#m-:rf:)" rx="214.5" ry="186"></ellipse><defs><filter id="n-:rf:" width="176.9" height="129.3" x="272.2" y="308" filterUnits="userSpaceOnUse"><fecolormatrix values="1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"></fecolormatrix></filter></defs><mask id="o-:rf:" width="176.9" height="129.3" x="272.2" y="308" maskUnits="userSpaceOnUse"><g filter="url(#n-:rf:)"><circle cx="316.5" cy="316.5" r="316.5" fill="#FFF" fill-rule="evenodd" clip-rule="evenodd"></circle></g></mask><g mask="url(#o-:rf:)"><path fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="11" d="M436 403.2l-5 28.6m-140-90.3l-10.9 62m52.8-19.4l-4.3 27.1"></path><lineargradient id="p-:rf:" x1="-645.656" x2="-646.499" y1="854.878" y2="854.788" gradientTransform="matrix(-184.159 -32.4722 11.4608 -64.9973 -128419.844 34938.836)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#ee2700"></stop><stop offset="1" stop-color="#ff008e"></stop></lineargradient><path fill="url(#p-:rf:)" fill-rule="evenodd" d="M344.1 363l97.7 17.2c5.8 2.1 8.2 6.2 7.1 12.1-1 5.9-4.7 9.2-11 9.9l-106-18.7-57.5-59.2c-3.2-4.8-2.9-9.1.8-12.8 3.7-3.7 8.3-4.4 13.7-2.1l55.2 53.6z" clip-rule="evenodd"></path><path fill="#D8D8D8" fill-rule="evenodd" stroke="#FFF" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7" d="M428.3 384.5l.9-6.5m-33.9 1.5l.9-6.5m-34 .5l.9-6.1m-38.9-16.1l4.2-3.9m-25.2-16.1l4.2-3.9" clip-rule="evenodd"></path></g><defs><filter id="q-:rf:" width="280.6" height="317.4" x="73.2" y="113.9" filterUnits="userSpaceOnUse"><fecolormatrix values="1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"></fecolormatrix></filter></defs><mask id="r-:rf:" width="280.6" height="317.4" x="73.2" y="113.9" maskUnits="userSpaceOnUse"><g filter="url(#q-:rf:)"><circle cx="316.5" cy="316.5" r="316.5" fill="#FFF" fill-rule="evenodd" clip-rule="evenodd"></circle></g></mask><g mask="url(#r-:rf:)"><lineargradient id="s-:rf:" x1="-646.8" x2="-646.8" y1="854.844" y2="853.844" gradientTransform="matrix(-100.1751 48.8587 -97.9753 -200.879 19124.773 203538.61)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#a17500"></stop><stop offset="1" stop-color="#5d2100"></stop></lineargradient><path fill="url(#s-:rf:)" fill-rule="evenodd" d="M192.3 203c8.1 37.3 14 73.6 17.8 109.1 3.8 35.4 2.8 75.2-2.9 119.2l61.2-16.7c-15.6-59-25.2-97.9-28.6-116.6-3.4-18.7-10.8-51.8-22.2-99.6l-25.3 4.6" clip-rule="evenodd"></path><lineargradient id="t-:rf:" x1="-635.467" x2="-635.467" y1="852.115" y2="851.115" gradientTransform="matrix(92.6873 4.8575 2.0257 -38.6535 57323.695 36176.047)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#2f8a00"></stop><stop offset="1" stop-color="#90ff57"></stop></lineargradient><path fill="url(#t-:rf:)" fill-rule="evenodd" stroke="#2F8A00" stroke-width="13" d="M195 183.9s-12.6-22.1-36.5-29.9c-15.9-5.2-34.4-1.5-55.5 11.1 15.9 14.3 29.5 22.6 40.7 24.9 16.8 3.6 51.3-6.1 51.3-6.1z" clip-rule="evenodd"></path><lineargradient id="u-:rf:" x1="-636.573" x2="-636.573" y1="855.444" y2="854.444" gradientTransform="matrix(109.9945 5.7646 6.3597 -121.3507 64719.133 107659.336)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#2f8a00"></stop><stop offset="1" stop-color="#90ff57"></stop></lineargradient><path fill="url(#u-:rf:)" fill-rule="evenodd" stroke="#2F8A00" stroke-width="13" d="M194.9 184.5s-47.5-8.5-83.2 15.7c-23.8 16.2-34.3 49.3-31.6 99.3 30.3-27.8 52.1-48.5 65.2-61.9 19.8-20 49.6-53.1 49.6-53.1z" clip-rule="evenodd"></path><lineargradient id="v-:rf:" x1="-632.145" x2="-632.145" y1="854.174" y2="853.174" gradientTransform="matrix(62.9558 3.2994 3.5021 -66.8246 37035.367 59284.227)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#2f8a00"></stop><stop offset="1" stop-color="#90ff57"></stop></lineargradient><path fill="url(#v-:rf:)" fill-rule="evenodd" stroke="#2F8A00" stroke-width="13" d="M195 183.9c-.8-21.9 6-38 20.6-48.2 14.6-10.2 29.8-15.3 45.5-15.3-6.1 21.4-14.5 35.8-25.2 43.4-10.7 7.5-24.4 14.2-40.9 20.1z" clip-rule="evenodd"></path><lineargradient id="w-:rf:" x1="-638.224" x2="-638.224" y1="853.801" y2="852.801" gradientTransform="matrix(152.4666 7.9904 3.0934 -59.0251 94939.86 55646.855)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#2f8a00"></stop><stop offset="1" stop-color="#90ff57"></stop></lineargradient><path fill="url(#w-:rf:)" fill-rule="evenodd" stroke="#2F8A00" stroke-width="13" d="M194.9 184.5c31.9-30 64.1-39.7 96.7-29 32.6 10.7 50.8 30.4 54.6 59.1-35.2-5.5-60.4-9.6-75.8-12.1-15.3-2.6-40.5-8.6-75.5-18z" clip-rule="evenodd"></path><lineargradient id="x-:rf:" x1="-637.723" x2="-637.723" y1="855.103" y2="854.103" gradientTransform="matrix(136.467 7.1519 5.2165 -99.5377 82830.875 89859.578)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#2f8a00"></stop><stop offset="1" stop-color="#90ff57"></stop></lineargradient><path fill="url(#x-:rf:)" fill-rule="evenodd" stroke="#2F8A00" stroke-width="13" d="M194.9 184.5c35.8-7.6 65.6-.2 89.2 22 23.6 22.2 37.7 49 42.3 80.3-39.8-9.7-68.3-23.8-85.5-42.4-17.2-18.5-32.5-38.5-46-59.9z" clip-rule="evenodd"></path><lineargradient id="y-:rf:" x1="-631.79" x2="-631.79" y1="855.872" y2="854.872" gradientTransform="matrix(60.8683 3.19 8.7771 -167.4773 31110.818 145537.61)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#2f8a00"></stop><stop offset="1" stop-color="#90ff57"></stop></lineargradient><path fill="url(#y-:rf:)" fill-rule="evenodd" stroke="#2F8A00" stroke-width="13" d="M194.9 184.5c-33.6 13.8-53.6 35.7-60.1 65.6-6.5 29.9-3.6 63.1 8.7 99.6 27.4-40.3 43.2-69.6 47.4-88 4.2-18.3 5.5-44.1 4-77.2z" clip-rule="evenodd"></path><path fill="none" stroke="#2F8A00" stroke-linecap="round" stroke-width="8" d="M196.5 182.3c-14.8 21.6-25.1 41.4-30.8 59.4-5.7 18-9.4 33-11.1 45.1"></path><path fill="none" stroke="#2F8A00" stroke-linecap="round" stroke-width="8" d="M194.8 185.7c-24.4 1.7-43.8 9-58.1 21.8-14.3 12.8-24.7 25.4-31.3 37.8m99.1-68.9c29.7-6.7 52-8.4 67-5 15 3.4 26.9 8.7 35.8 15.9m-110.8-5.9c20.3 9.9 38.2 20.5 53.9 31.9 15.7 11.4 27.4 22.1 35.1 32"></path></g><defs><filter id="z-:rf:" width="532" height="633" x="50.5" y="399" filterUnits="userSpaceOnUse"><fecolormatrix values="1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"></fecolormatrix></filter></defs><mask id="A-:rf:" width="532" height="633" x="50.5" y="399" maskUnits="userSpaceOnUse"><g filter="url(#z-:rf:)"><circle cx="316.5" cy="316.5" r="316.5" fill="#FFF" fill-rule="evenodd" clip-rule="evenodd"></circle></g></mask><lineargradient id="B-:rf:" x1="-641.104" x2="-641.278" y1="856.577" y2="856.183" gradientTransform="matrix(532 0 0 -633 341484.5 542657)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#fff400"></stop><stop offset="1" stop-color="#3c8700"></stop></lineargradient><ellipse cx="316.5" cy="715.5" fill="url(#B-:rf:)" fill-rule="evenodd" clip-rule="evenodd" mask="url(#A-:rf:)" rx="266" ry="316.5"></ellipse><defs><filter id="C-:rf:" width="288" height="283" x="391" y="-24" filterUnits="userSpaceOnUse"><fecolormatrix values="1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"></fecolormatrix></filter></defs><mask id="D-:rf:" width="288" height="283" x="391" y="-24" maskUnits="userSpaceOnUse"><g filter="url(#C-:rf:)"><circle cx="316.5" cy="316.5" r="316.5" fill="#FFF" fill-rule="evenodd" clip-rule="evenodd"></circle></g></mask><g mask="url(#D-:rf:)"><g transform="translate(397 -24)"><lineargradient id="E-:rf:" x1="-1036.672" x2="-1036.672" y1="880.018" y2="879.018" gradientTransform="matrix(227 0 0 -227 235493 199764)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#ffdf00"></stop><stop offset="1" stop-color="#ff9d00"></stop></lineargradient><circle cx="168.5" cy="113.5" r="113.5" fill="url(#E-:rf:)" fill-rule="evenodd" clip-rule="evenodd"></circle><lineargradient id="F-:rf:" x1="-1017.329" x2="-1018.602" y1="658.003" y2="657.998" gradientTransform="matrix(30 0 0 -1 30558 771)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#ffa400"></stop><stop offset="1" stop-color="#ff5e00"></stop></lineargradient><path fill="none" stroke="url(#F-:rf:)" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" d="M30 113H0"></path><lineargradient id="G-:rf:" x1="-1014.501" x2="-1015.774" y1="839.985" y2="839.935" gradientTransform="matrix(26.5 0 0 -5.5 26925 4696.5)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#ffa400"></stop><stop offset="1" stop-color="#ff5e00"></stop></lineargradient><path fill="none" stroke="url(#G-:rf:)" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" d="M33.5 79.5L7 74"></path><lineargradient id="H-:rf:" x1="-1016.59" x2="-1017.862" y1="852.671" y2="852.595" gradientTransform="matrix(29 0 0 -8 29523 6971)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#ffa400"></stop><stop offset="1" stop-color="#ff5e00"></stop></lineargradient><path fill="none" stroke="url(#H-:rf:)" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" d="M34 146l-29 8"></path><lineargradient id="I-:rf:" x1="-1011.984" x2="-1013.257" y1="863.523" y2="863.229" gradientTransform="matrix(24 0 0 -13 24339 11407)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#ffa400"></stop><stop offset="1" stop-color="#ff5e00"></stop></lineargradient><path fill="none" stroke="url(#I-:rf:)" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" d="M45 177l-24 13"></path><lineargradient id="J-:rf:" x1="-1006.673" x2="-1007.946" y1="869.279" y2="868.376" gradientTransform="matrix(20 0 0 -19 20205 16720)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#ffa400"></stop><stop offset="1" stop-color="#ff5e00"></stop></lineargradient><path fill="none" stroke="url(#J-:rf:)" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" d="M67 204l-20 19"></path><lineargradient id="K-:rf:" x1="-992.85" x2="-993.317" y1="871.258" y2="870.258" gradientTransform="matrix(13.8339 0 0 -22.8467 13825.796 20131.938)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#ffa400"></stop><stop offset="1" stop-color="#ff5e00"></stop></lineargradient><path fill="none" stroke="url(#K-:rf:)" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" d="M94.4 227l-13.8 22.8"></path><lineargradient id="L-:rf:" x1="-953.835" x2="-953.965" y1="871.9" y2="870.9" gradientTransform="matrix(7.5 0 0 -24.5 7278 21605)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#ffa400"></stop><stop offset="1" stop-color="#ff5e00"></stop></lineargradient><path fill="none" stroke="url(#L-:rf:)" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" d="M127.5 243.5L120 268"></path><lineargradient id="M-:rf:" x1="244.504" x2="244.496" y1="871.898" y2="870.898" gradientTransform="matrix(.5 0 0 -24.5 45.5 21614)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#ffa400"></stop><stop offset="1" stop-color="#ff5e00"></stop></lineargradient><path fill="none" stroke="url(#M-:rf:)" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" d="M167.5 252.5l.5 24.5"></path></g></g></g></svg></div><div class="go3455555371"><svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 634 633" viewBox="0 0 634 633"><g transform="translate(1)"><lineargradient id="a-:rg:" x1="-641.486" x2="-641.486" y1="856.648" y2="855.931" gradientTransform="matrix(633 0 0 -633 406377 542258)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#6bdaff"></stop><stop offset="0.319" stop-color="#f9ffb5"></stop><stop offset="0.706" stop-color="#ffa770"></stop><stop offset="1" stop-color="#ff7373"></stop></lineargradient><circle cx="316.5" cy="316.5" r="316.5" fill="url(#a-:rg:)" fill-rule="evenodd" clip-rule="evenodd"></circle><defs><filter id="b-:rg:" width="454" height="396.9" x="-137.5" y="412" filterUnits="userSpaceOnUse"><fecolormatrix values="1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"></fecolormatrix></filter></defs><mask id="c-:rg:" width="454" height="396.9" x="-137.5" y="412" maskUnits="userSpaceOnUse"><g filter="url(#b-:rg:)"><circle cx="316.5" cy="316.5" r="316.5" fill="#FFF" fill-rule="evenodd" clip-rule="evenodd"></circle></g></mask><ellipse cx="89.5" cy="610.5" fill="#015064" fill-rule="evenodd" stroke="#00CFE2" stroke-width="25" clip-rule="evenodd" mask="url(#c-:rg:)" rx="214.5" ry="186"></ellipse><defs><filter id="d-:rg:" width="454" height="396.9" x="316.5" y="412" filterUnits="userSpaceOnUse"><fecolormatrix values="1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"></fecolormatrix></filter></defs><mask id="e-:rg:" width="454" height="396.9" x="316.5" y="412" maskUnits="userSpaceOnUse"><g filter="url(#d-:rg:)"><circle cx="316.5" cy="316.5" r="316.5" fill="#FFF" fill-rule="evenodd" clip-rule="evenodd"></circle></g></mask><ellipse cx="543.5" cy="610.5" fill="#015064" fill-rule="evenodd" stroke="#00CFE2" stroke-width="25" clip-rule="evenodd" mask="url(#e-:rg:)" rx="214.5" ry="186"></ellipse><defs><filter id="f-:rg:" width="454" height="396.9" x="-137.5" y="450" filterUnits="userSpaceOnUse"><fecolormatrix values="1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"></fecolormatrix></filter></defs><mask id="g-:rg:" width="454" height="396.9" x="-137.5" y="450" maskUnits="userSpaceOnUse"><g filter="url(#f-:rg:)"><circle cx="316.5" cy="316.5" r="316.5" fill="#FFF" fill-rule="evenodd" clip-rule="evenodd"></circle></g></mask><ellipse cx="89.5" cy="648.5" fill="#015064" fill-rule="evenodd" stroke="#00A8B8" stroke-width="25" clip-rule="evenodd" mask="url(#g-:rg:)" rx="214.5" ry="186"></ellipse><defs><filter id="h-:rg:" width="454" height="396.9" x="316.5" y="450" filterUnits="userSpaceOnUse"><fecolormatrix values="1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"></fecolormatrix></filter></defs><mask id="i-:rg:" width="454" height="396.9" x="316.5" y="450" maskUnits="userSpaceOnUse"><g filter="url(#h-:rg:)"><circle cx="316.5" cy="316.5" r="316.5" fill="#FFF" fill-rule="evenodd" clip-rule="evenodd"></circle></g></mask><ellipse cx="543.5" cy="648.5" fill="#015064" fill-rule="evenodd" stroke="#00A8B8" stroke-width="25" clip-rule="evenodd" mask="url(#i-:rg:)" rx="214.5" ry="186"></ellipse><defs><filter id="j-:rg:" width="454" height="396.9" x="-137.5" y="486" filterUnits="userSpaceOnUse"><fecolormatrix values="1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"></fecolormatrix></filter></defs><mask id="k-:rg:" width="454" height="396.9" x="-137.5" y="486" maskUnits="userSpaceOnUse"><g filter="url(#j-:rg:)"><circle cx="316.5" cy="316.5" r="316.5" fill="#FFF" fill-rule="evenodd" clip-rule="evenodd"></circle></g></mask><ellipse cx="89.5" cy="684.5" fill="#015064" fill-rule="evenodd" stroke="#007782" stroke-width="25" clip-rule="evenodd" mask="url(#k-:rg:)" rx="214.5" ry="186"></ellipse><defs><filter id="l-:rg:" width="454" height="396.9" x="316.5" y="486" filterUnits="userSpaceOnUse"><fecolormatrix values="1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"></fecolormatrix></filter></defs><mask id="m-:rg:" width="454" height="396.9" x="316.5" y="486" maskUnits="userSpaceOnUse"><g filter="url(#l-:rg:)"><circle cx="316.5" cy="316.5" r="316.5" fill="#FFF" fill-rule="evenodd" clip-rule="evenodd"></circle></g></mask><ellipse cx="543.5" cy="684.5" fill="#015064" fill-rule="evenodd" stroke="#007782" stroke-width="25" clip-rule="evenodd" mask="url(#m-:rg:)" rx="214.5" ry="186"></ellipse><defs><filter id="n-:rg:" width="176.9" height="129.3" x="272.2" y="308" filterUnits="userSpaceOnUse"><fecolormatrix values="1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"></fecolormatrix></filter></defs><mask id="o-:rg:" width="176.9" height="129.3" x="272.2" y="308" maskUnits="userSpaceOnUse"><g filter="url(#n-:rg:)"><circle cx="316.5" cy="316.5" r="316.5" fill="#FFF" fill-rule="evenodd" clip-rule="evenodd"></circle></g></mask><g mask="url(#o-:rg:)"><path fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="11" d="M436 403.2l-5 28.6m-140-90.3l-10.9 62m52.8-19.4l-4.3 27.1"></path><lineargradient id="p-:rg:" x1="-645.656" x2="-646.499" y1="854.878" y2="854.788" gradientTransform="matrix(-184.159 -32.4722 11.4608 -64.9973 -128419.844 34938.836)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#ee2700"></stop><stop offset="1" stop-color="#ff008e"></stop></lineargradient><path fill="url(#p-:rg:)" fill-rule="evenodd" d="M344.1 363l97.7 17.2c5.8 2.1 8.2 6.2 7.1 12.1-1 5.9-4.7 9.2-11 9.9l-106-18.7-57.5-59.2c-3.2-4.8-2.9-9.1.8-12.8 3.7-3.7 8.3-4.4 13.7-2.1l55.2 53.6z" clip-rule="evenodd"></path><path fill="#D8D8D8" fill-rule="evenodd" stroke="#FFF" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="7" d="M428.3 384.5l.9-6.5m-33.9 1.5l.9-6.5m-34 .5l.9-6.1m-38.9-16.1l4.2-3.9m-25.2-16.1l4.2-3.9" clip-rule="evenodd"></path></g><defs><filter id="q-:rg:" width="280.6" height="317.4" x="73.2" y="113.9" filterUnits="userSpaceOnUse"><fecolormatrix values="1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"></fecolormatrix></filter></defs><mask id="r-:rg:" width="280.6" height="317.4" x="73.2" y="113.9" maskUnits="userSpaceOnUse"><g filter="url(#q-:rg:)"><circle cx="316.5" cy="316.5" r="316.5" fill="#FFF" fill-rule="evenodd" clip-rule="evenodd"></circle></g></mask><g mask="url(#r-:rg:)"><lineargradient id="s-:rg:" x1="-646.8" x2="-646.8" y1="854.844" y2="853.844" gradientTransform="matrix(-100.1751 48.8587 -97.9753 -200.879 19124.773 203538.61)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#a17500"></stop><stop offset="1" stop-color="#5d2100"></stop></lineargradient><path fill="url(#s-:rg:)" fill-rule="evenodd" d="M192.3 203c8.1 37.3 14 73.6 17.8 109.1 3.8 35.4 2.8 75.2-2.9 119.2l61.2-16.7c-15.6-59-25.2-97.9-28.6-116.6-3.4-18.7-10.8-51.8-22.2-99.6l-25.3 4.6" clip-rule="evenodd"></path><lineargradient id="t-:rg:" x1="-635.467" x2="-635.467" y1="852.115" y2="851.115" gradientTransform="matrix(92.6873 4.8575 2.0257 -38.6535 57323.695 36176.047)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#2f8a00"></stop><stop offset="1" stop-color="#90ff57"></stop></lineargradient><path fill="url(#t-:rg:)" fill-rule="evenodd" stroke="#2F8A00" stroke-width="13" d="M195 183.9s-12.6-22.1-36.5-29.9c-15.9-5.2-34.4-1.5-55.5 11.1 15.9 14.3 29.5 22.6 40.7 24.9 16.8 3.6 51.3-6.1 51.3-6.1z" clip-rule="evenodd"></path><lineargradient id="u-:rg:" x1="-636.573" x2="-636.573" y1="855.444" y2="854.444" gradientTransform="matrix(109.9945 5.7646 6.3597 -121.3507 64719.133 107659.336)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#2f8a00"></stop><stop offset="1" stop-color="#90ff57"></stop></lineargradient><path fill="url(#u-:rg:)" fill-rule="evenodd" stroke="#2F8A00" stroke-width="13" d="M194.9 184.5s-47.5-8.5-83.2 15.7c-23.8 16.2-34.3 49.3-31.6 99.3 30.3-27.8 52.1-48.5 65.2-61.9 19.8-20 49.6-53.1 49.6-53.1z" clip-rule="evenodd"></path><lineargradient id="v-:rg:" x1="-632.145" x2="-632.145" y1="854.174" y2="853.174" gradientTransform="matrix(62.9558 3.2994 3.5021 -66.8246 37035.367 59284.227)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#2f8a00"></stop><stop offset="1" stop-color="#90ff57"></stop></lineargradient><path fill="url(#v-:rg:)" fill-rule="evenodd" stroke="#2F8A00" stroke-width="13" d="M195 183.9c-.8-21.9 6-38 20.6-48.2 14.6-10.2 29.8-15.3 45.5-15.3-6.1 21.4-14.5 35.8-25.2 43.4-10.7 7.5-24.4 14.2-40.9 20.1z" clip-rule="evenodd"></path><lineargradient id="w-:rg:" x1="-638.224" x2="-638.224" y1="853.801" y2="852.801" gradientTransform="matrix(152.4666 7.9904 3.0934 -59.0251 94939.86 55646.855)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#2f8a00"></stop><stop offset="1" stop-color="#90ff57"></stop></lineargradient><path fill="url(#w-:rg:)" fill-rule="evenodd" stroke="#2F8A00" stroke-width="13" d="M194.9 184.5c31.9-30 64.1-39.7 96.7-29 32.6 10.7 50.8 30.4 54.6 59.1-35.2-5.5-60.4-9.6-75.8-12.1-15.3-2.6-40.5-8.6-75.5-18z" clip-rule="evenodd"></path><lineargradient id="x-:rg:" x1="-637.723" x2="-637.723" y1="855.103" y2="854.103" gradientTransform="matrix(136.467 7.1519 5.2165 -99.5377 82830.875 89859.578)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#2f8a00"></stop><stop offset="1" stop-color="#90ff57"></stop></lineargradient><path fill="url(#x-:rg:)" fill-rule="evenodd" stroke="#2F8A00" stroke-width="13" d="M194.9 184.5c35.8-7.6 65.6-.2 89.2 22 23.6 22.2 37.7 49 42.3 80.3-39.8-9.7-68.3-23.8-85.5-42.4-17.2-18.5-32.5-38.5-46-59.9z" clip-rule="evenodd"></path><lineargradient id="y-:rg:" x1="-631.79" x2="-631.79" y1="855.872" y2="854.872" gradientTransform="matrix(60.8683 3.19 8.7771 -167.4773 31110.818 145537.61)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#2f8a00"></stop><stop offset="1" stop-color="#90ff57"></stop></lineargradient><path fill="url(#y-:rg:)" fill-rule="evenodd" stroke="#2F8A00" stroke-width="13" d="M194.9 184.5c-33.6 13.8-53.6 35.7-60.1 65.6-6.5 29.9-3.6 63.1 8.7 99.6 27.4-40.3 43.2-69.6 47.4-88 4.2-18.3 5.5-44.1 4-77.2z" clip-rule="evenodd"></path><path fill="none" stroke="#2F8A00" stroke-linecap="round" stroke-width="8" d="M196.5 182.3c-14.8 21.6-25.1 41.4-30.8 59.4-5.7 18-9.4 33-11.1 45.1"></path><path fill="none" stroke="#2F8A00" stroke-linecap="round" stroke-width="8" d="M194.8 185.7c-24.4 1.7-43.8 9-58.1 21.8-14.3 12.8-24.7 25.4-31.3 37.8m99.1-68.9c29.7-6.7 52-8.4 67-5 15 3.4 26.9 8.7 35.8 15.9m-110.8-5.9c20.3 9.9 38.2 20.5 53.9 31.9 15.7 11.4 27.4 22.1 35.1 32"></path></g><defs><filter id="z-:rg:" width="532" height="633" x="50.5" y="399" filterUnits="userSpaceOnUse"><fecolormatrix values="1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"></fecolormatrix></filter></defs><mask id="A-:rg:" width="532" height="633" x="50.5" y="399" maskUnits="userSpaceOnUse"><g filter="url(#z-:rg:)"><circle cx="316.5" cy="316.5" r="316.5" fill="#FFF" fill-rule="evenodd" clip-rule="evenodd"></circle></g></mask><lineargradient id="B-:rg:" x1="-641.104" x2="-641.278" y1="856.577" y2="856.183" gradientTransform="matrix(532 0 0 -633 341484.5 542657)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#fff400"></stop><stop offset="1" stop-color="#3c8700"></stop></lineargradient><ellipse cx="316.5" cy="715.5" fill="url(#B-:rg:)" fill-rule="evenodd" clip-rule="evenodd" mask="url(#A-:rg:)" rx="266" ry="316.5"></ellipse><defs><filter id="C-:rg:" width="288" height="283" x="391" y="-24" filterUnits="userSpaceOnUse"><fecolormatrix values="1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"></fecolormatrix></filter></defs><mask id="D-:rg:" width="288" height="283" x="391" y="-24" maskUnits="userSpaceOnUse"><g filter="url(#C-:rg:)"><circle cx="316.5" cy="316.5" r="316.5" fill="#FFF" fill-rule="evenodd" clip-rule="evenodd"></circle></g></mask><g mask="url(#D-:rg:)"><g transform="translate(397 -24)"><lineargradient id="E-:rg:" x1="-1036.672" x2="-1036.672" y1="880.018" y2="879.018" gradientTransform="matrix(227 0 0 -227 235493 199764)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#ffdf00"></stop><stop offset="1" stop-color="#ff9d00"></stop></lineargradient><circle cx="168.5" cy="113.5" r="113.5" fill="url(#E-:rg:)" fill-rule="evenodd" clip-rule="evenodd"></circle><lineargradient id="F-:rg:" x1="-1017.329" x2="-1018.602" y1="658.003" y2="657.998" gradientTransform="matrix(30 0 0 -1 30558 771)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#ffa400"></stop><stop offset="1" stop-color="#ff5e00"></stop></lineargradient><path fill="none" stroke="url(#F-:rg:)" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" d="M30 113H0"></path><lineargradient id="G-:rg:" x1="-1014.501" x2="-1015.774" y1="839.985" y2="839.935" gradientTransform="matrix(26.5 0 0 -5.5 26925 4696.5)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#ffa400"></stop><stop offset="1" stop-color="#ff5e00"></stop></lineargradient><path fill="none" stroke="url(#G-:rg:)" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" d="M33.5 79.5L7 74"></path><lineargradient id="H-:rg:" x1="-1016.59" x2="-1017.862" y1="852.671" y2="852.595" gradientTransform="matrix(29 0 0 -8 29523 6971)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#ffa400"></stop><stop offset="1" stop-color="#ff5e00"></stop></lineargradient><path fill="none" stroke="url(#H-:rg:)" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" d="M34 146l-29 8"></path><lineargradient id="I-:rg:" x1="-1011.984" x2="-1013.257" y1="863.523" y2="863.229" gradientTransform="matrix(24 0 0 -13 24339 11407)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#ffa400"></stop><stop offset="1" stop-color="#ff5e00"></stop></lineargradient><path fill="none" stroke="url(#I-:rg:)" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" d="M45 177l-24 13"></path><lineargradient id="J-:rg:" x1="-1006.673" x2="-1007.946" y1="869.279" y2="868.376" gradientTransform="matrix(20 0 0 -19 20205 16720)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#ffa400"></stop><stop offset="1" stop-color="#ff5e00"></stop></lineargradient><path fill="none" stroke="url(#J-:rg:)" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" d="M67 204l-20 19"></path><lineargradient id="K-:rg:" x1="-992.85" x2="-993.317" y1="871.258" y2="870.258" gradientTransform="matrix(13.8339 0 0 -22.8467 13825.796 20131.938)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#ffa400"></stop><stop offset="1" stop-color="#ff5e00"></stop></lineargradient><path fill="none" stroke="url(#K-:rg:)" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" d="M94.4 227l-13.8 22.8"></path><lineargradient id="L-:rg:" x1="-953.835" x2="-953.965" y1="871.9" y2="870.9" gradientTransform="matrix(7.5 0 0 -24.5 7278 21605)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#ffa400"></stop><stop offset="1" stop-color="#ff5e00"></stop></lineargradient><path fill="none" stroke="url(#L-:rg:)" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" d="M127.5 243.5L120 268"></path><lineargradient id="M-:rg:" x1="244.504" x2="244.496" y1="871.898" y2="870.898" gradientTransform="matrix(.5 0 0 -24.5 45.5 21614)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#ffa400"></stop><stop offset="1" stop-color="#ff5e00"></stop></lineargradient><path fill="none" stroke="url(#M-:rg:)" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="12" d="M167.5 252.5l.5 24.5"></path></g></g></g></svg></div></div><div class="go2821754102">-</div><div class="go1561890071">TanStack Router</div></button></footer></div>
    <script type="module" src="./Portal Web_files/main.tsx"></script>
  

</body><div id="aitopia" class="aitopia dark" data-v-app=""><!----><!----><div id="aitopia-container" class="close-sidebar"><div id="aitopia-sidebar"><div id="ai-sidebar" class="ait-flex ait-w-full ait-h-full ait-overflow-hidden"><div class="ait-flex ait-flex-col ait-w-[calc(100%_-_60px)]" id="ait-sidebar-chat"><div class="ait-header ait-flex-shrink"><nav class="ait-w-full ait-bg-[transparent]"><div class="ait-flex ait-items-center ait-w-full ait-p-0"><div class="ait-flex ait-items-center ait-justify-between ait-w-full"><div class="ait-flex ait-items-center ait-justify-between"><span><svg class="ait-w-8 ait-h-8  ait-logo" xmlns="http://www.w3.org/2000/svg" baseProfile="tiny" viewBox="0 0 125 121.7" overflow="visible"><circle fill="#01a77d" cx="63.4" cy="61.2" r="57.8"></circle><g fill="#fff"><path d="M46.9 60.5h-.4c-1.9-.2-3.3-1.9-3.1-3.8.6-6.3 4.5-38 17.3-41.2 8.2-2 14.4 7.5 16.5 10.6 1.1 1.6.6 3.8-1 4.9s-3.8.6-4.9-1c-3.5-5.3-6.9-8.2-9-7.7-4.2 1-10 14.8-12 35.1-.2 1.7-1.7 3.1-3.4 3.1zm51.9-4.9c-.5 0-1-.1-1.4-.3-1.8-.8-2.6-2.9-1.8-4.6 2.6-5.8 3.2-10.2 1.7-11.7-3.1-3-17.8-.6-36.1 8.6-1.7.9-3.8.2-4.7-1.6-.9-1.7-.2-3.8 1.6-4.7.3-.2 8.3-4.1 17.4-7.1 13.4-4.5 22.2-4.5 26.7-.2 6.1 5.8 1.4 16.2-.1 19.6-.7 1.3-2 2-3.3 2zM88.2 89.5c-1.8 0-3.3-1.3-3.5-3.1-.2-1.9 1.1-3.7 3.1-3.9 6.3-.7 10.4-2.5 10.9-4.6 1-4.2-8.7-15.6-25.9-26.6-1.6-1-2.1-3.2-1.1-4.8s3.2-2.1 4.8-1.1c.3.2 7.8 5 15.1 11.3 10.7 9.3 15.3 16.7 13.9 22.8-1.9 8.2-13.2 9.5-16.9 10h-.4zm-25.9 18.1c-7.2 0-12.4-8.3-14.2-11.2-1-1.6-.5-3.8 1.1-4.8s3.8-.5 4.8 1.1c3.4 5.4 6.7 8.3 8.8 7.9 4.2-.9 10.3-14.5 12.9-34.8.2-1.9 2-3.3 3.9-3 1.9.2 3.3 2 3 3.9-.8 6.3-5.4 37.9-18.4 40.7-.5.1-1.2.2-1.9.2zm-25-18.1c-5.5 0-9.4-1.3-11.8-4-5.6-6.3-.1-16.3 1.6-19.5.9-1.7 3.1-2.3 4.7-1.4 1.7.9 2.3 3.1 1.4 4.7-3.1 5.6-4 9.9-2.6 11.5 2.9 3.2 17.7 1.9 36.7-5.7a3.57 3.57 0 0 1 4.6 1.9 3.57 3.57 0 0 1-1.9 4.6c-.3.1-8.6 3.5-17.9 5.8-5.9 1.4-10.8 2.1-14.8 2.1zm16.5-12.6c-.6 0-1.2-.2-1.8-.5-.3-.2-7.9-4.8-15.4-10.8-11-8.9-15.8-16.2-14.6-22.3 1.6-8.2 12.9-9.9 16.6-10.5 1.9-.3 3.7 1 4 2.9s-1 3.7-2.9 4c-6.3.9-10.3 2.8-10.7 4.9-1 4.3 9 15.3 26.6 25.8 1.7 1 2.2 3.1 1.2 4.8-.7 1.1-1.8 1.7-3 1.7z"></path><circle cx="63.8" cy="60.5" r="8.4"></circle></g></svg></span><div class="ait-self-center ait-text-base ait-text-black ait-ml-2 dark:ait-text-gray-200 ait-whitespace-nowrap">Chat</div></div><div class="ait-flex ait-items-center ait-max-h-[50px]"><button class="ait-flex ait-items-center ait-space-x-2 ait-bg-transparent hover:ait-bg-[var(--ait-link-color)] ait-transition-all ait-rounded-full ait-p-1 ait-px-2"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hide="true" class="ait-w-4 ait-h-4 dark:ait-text-gray-200"><path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15"></path></svg><span class="ait-text-sm dark:ait-text-gray-200">New Conversation</span></button><button type="button" class="ait-ml-2 svg-logo"><span><svg class="ait-h-6 ait-w-6 ait-history_icon" width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M20 12C20 16.4183 16.4183 20 12 20C7.58172 20 4 16.4183 4 12C4 7.58172 7.58172 4 12 4C16.4183 4 20 7.58172 20 12ZM22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12ZM13 8.99939C12.9997 8.4471 12.5517 7.99966 11.9994 8C11.4471 8.00034 10.9997 8.44833 11 9.00061L11.0024 13.0006C11.0026 13.3347 11.1697 13.6466 11.4477 13.832L14.4471 15.832C14.9066 16.1384 15.5275 16.0143 15.8339 15.5548C16.1403 15.0953 16.0161 14.4744 15.5566 14.168L13.0021 12.4647L13 8.99939Z" fill="currentColor"></path></svg></span></button></div></div></div></nav></div><div id="ait-upgrade-plan-modal" class="ait-hide"><div tabindex="-1" aria-hide="true" class="ait-fixed !ait-z-[999999] ait-modal ait-m-0 ait-bg-[rgba(0,0,0,.5)] ait-top-0 ait-left-0 ait-bottom-0 ait-right-0 ait-w-full ait-p-4 ait-overflow-x-hidden ait-overflow-y-auto lg:ait-inset-0 h-[calc(100%)] ait-max-h-full ait-justify-center ait-flex ait-w-full ait-items-center" data-modal-hide=""><div class="ait-relative ait-w-full lg:ait-max-w-screen-xl ait-max-h-full"><div class="ait-relative ait-bg-white ait-rounded-lg ait-shadow dark:ait-bg-neutral-800"><div class="ait-px-6 ait-py-4 ait-border-b ait-rounded-t dark:ait-border-neutral-600"><h3 class="ait-text-base ait-font-semibold ait-text-neutral-900 lg:ait-text-lg dark:ait-text-white ait-text-center">Search</h3><button type="button" class="ait-absolute ait-top-3 ait-right-2.5 ait-text-neutral-400 ait-bg-transparent hover:ait-bg-neutral-200 hover:ait-text-neutral-900 ait-rounded-lg ait-text-sm ait-w-8 ait-h-8 ait-ml-auto ait-inline-flex ait-justify-center ait-items-center dark:hover:ait-bg-neutral-600 dark:hover:ait-text-white" data-modal-hide=""><svg class="ait-w-3 ait-h-3" aria-hide="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"></path></svg><span class="ait-sr-only">Close modal</span></button></div><div class="ait-px-4 ait-py-12 ait-w-full ait-text-center ait-flex ait-flex-col ait-items-center ait-gap-8"><p class="ait-px-4 ait-text-sm ait-text-neutral-600 dark:ait-text-neutral-400">The data of ChatGPT isn't real-time. WebAccess Feature combines the intelligence of ChatGPT with realtime web information, allowing GPT to handle real-time information-related questions more effectively. Upgrade to gain this feature, and worry no more about outdated information!</p><button class="aitopia-pricing ait-cursor-pointer ait-w-full ait-font-semibold ait-text-white ait-text-sm ait-py-2 ait-rounded-md ait-flex ait-items-center ait-gap-2 ait-justify-center ait-bg-[var(--ait-link-color)] hover:ait-opacity-80 ait-transition-all">Upgrade Now</button></div><!----></div></div></div></div><button data-ait-dropdown-toggle="ait-upgrade-plan-modal" id="ait-toggle-upgrade-button" class="ait-hidden"></button><div id="ai-sidebar-content" class="false"><div class="ait-h-screen ait-flex ait-flex-col ait-h-full"><div id="ai-message-container" class="ait-flex ait-relative ait-flex-grow ait-w-full ait-overflow-x-hidden"><div class="ait-w-full"><div class="ait-flex ait-flex-col ait-h-full ait-space-y-3"><div id="aitopia-example"><div class="ait-p-2"><div class="ait-flex ait-items-center ait-justify-between ait-p-3 ait-space-x-3 ait-border ait-rounded-xl ait-bg-neutral-100 dark:ait-bg-neutral-900 dark:ait-text-neutral-200 ait-cursor-pointer"><div><p class="ait-font-bold">🤓 Explain a complex thing</p><p class="ait-opacity-60 ait-text-[11px] example_content">Explain Artificial Intelligence so
                        that I can explain it to my six-year-old child.</p></div><button><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" class="ait-w-5 ait-h-5 ait-cursor-pointer" fill="none"><path d="M20 12L14 6M20 12L14 18M20 12L9.5 12M4 12L6.5 12" stroke="#14746F" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></button></div></div><div class="ait-p-2"><div class="ait-flex ait-items-center ait-justify-between ait-p-3 ait-space-x-3 ait-border ait-rounded-xl ait-bg-neutral-100 dark:ait-bg-neutral-900 dark:ait-text-neutral-200 ait-cursor-pointer"><div><p class="ait-font-bold">🧠 Get suggestions and create new ideas</p><p class="ait-opacity-60 ait-text-[11px] example_content">Please give me the best 10 travel
                        ideas around the world</p></div><button><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" class="ait-w-5 ait-h-5 ait-cursor-pointer" fill="none"><path d="M20 12L14 6M20 12L14 18M20 12L9.5 12M4 12L6.5 12" stroke="#14746F" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></button></div></div><div class="ait-p-2"><div class="ait-flex ait-items-center ait-justify-between ait-p-3 ait-space-x-3 ait-border ait-rounded-xl ait-bg-neutral-100 dark:ait-bg-neutral-900 dark:ait-text-neutral-200 ait-cursor-pointer"><div><p class="ait-font-bold">💭 Translate, summarize, fix grammar and more…</p><p class="ait-opacity-60 ait-text-[11px] example_content">Translate "I love you" French</p></div><button><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" class="ait-w-5 ait-h-5 ait-cursor-pointer" fill="none"><path d="M20 12L14 6M20 12L14 18M20 12L9.5 12M4 12L6.5 12" stroke="#14746F" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></button></div></div></div><!----><div data-v-f5c89177="" class="ait-h-full" id="aitopia-message"><div data-v-f5c89177="" aria-details="chat-key-0" class="ait-bg-neutral-100 dark:ait-bg-neutral-900 dark:ait-text-gray-200 ait-mt-[2px] ait-mb-[2px]"><div data-v-f5c89177="" class="ait-flex ait-p-2 ait-text-[10px] dark:ait-text-gray-200 ait-flex-col ait-items-start ait-justify-start ait-gap-2"><div data-v-f5c89177="" class="ait-flex ait-items-center ait-space-x-2"><span data-v-f5c89177=""><svg class="ait-w-[16px] ait-h-[16px] ait-GPT-4o Mini" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none"> <rect width="16" height="16" rx="8" fill="#0BD6FF"></rect> <path d="M13.1348 6.91143C13.4071 6.0943 13.3133 5.19918 12.878 4.45593C12.2232 3.31593 10.907 2.72943 9.62146 3.00543C9.04958 2.36118 8.22796 1.99481 7.36658 2.00006C6.05258 1.99706 4.88671 2.84306 4.48246 4.09331C3.63833 4.26618 2.90971 4.79456 2.48333 5.54343C1.82371 6.68043 1.97408 8.11368 2.85533 9.08868C2.58308 9.9058 2.67683 10.8009 3.11221 11.5442C3.76696 12.6842 5.08321 13.2707 6.36871 12.9947C6.94021 13.6389 7.76221 14.0053 8.62358 13.9997C9.93833 14.0031 11.1046 13.1563 11.5088 11.9049C12.353 11.7321 13.0816 11.2037 13.508 10.4548C14.1668 9.3178 14.0157 7.88643 13.1348 6.91143ZM8.62433 13.2156C8.09821 13.2163 7.58858 13.0322 7.18471 12.6951C7.20308 12.6853 7.23496 12.6677 7.25558 12.6549L9.64508 11.2749C9.76733 11.2056 9.84233 11.0754 9.84158 10.9348V7.56618L10.8515 8.1493C10.8623 8.15455 10.8695 8.16505 10.871 8.17705V10.9667C10.8695 12.2072 9.86483 13.2129 8.62433 13.2156ZM3.79283 11.1519C3.52921 10.6967 3.43433 10.1631 3.52471 9.64518C3.54233 9.65568 3.57346 9.6748 3.59558 9.68755L5.98508 11.0676C6.10621 11.1384 6.25621 11.1384 6.37771 11.0676L9.29483 9.38305V10.5493C9.29558 10.5613 9.28996 10.5729 9.28058 10.5804L6.86521 11.9751C5.78933 12.5946 4.41496 12.2263 3.79283 11.1519ZM3.16396 5.93605C3.42646 5.48006 3.84083 5.1313 4.33433 4.95018C4.33433 4.97081 4.33321 5.00718 4.33321 5.03268V7.79305C4.33246 7.9333 4.40746 8.06343 4.52933 8.1328L7.44646 9.81693L6.43658 10.4001C6.42646 10.4068 6.41371 10.4079 6.40246 10.4031L3.98671 9.0073C2.91308 8.38555 2.54521 7.01155 3.16396 5.93605ZM11.4612 7.86693L8.54408 6.18243L9.55396 5.59968C9.56408 5.59293 9.57683 5.59181 9.58808 5.59668L12.0038 6.9913C13.0793 7.61268 13.448 8.98855 12.8266 10.0641C12.5637 10.5193 12.1497 10.8681 11.6566 11.0496V8.20668C11.6577 8.06643 11.5827 7.93668 11.4612 7.86693ZM12.4662 6.35418C12.4486 6.34331 12.4175 6.32455 12.3953 6.3118L10.0058 4.93181C9.88471 4.86093 9.73471 4.86093 9.61321 4.93181L6.69608 6.6163V5.45005C6.69533 5.43805 6.70096 5.42643 6.71033 5.41893L9.12571 4.02543C10.2016 3.40481 11.5771 3.77418 12.1973 4.85043C12.4595 5.30493 12.5551 5.83706 12.4662 6.35418ZM6.14708 8.4328L5.13683 7.84968C5.12596 7.84443 5.11883 7.83393 5.11733 7.82193V5.03231C5.11808 3.79031 6.12571 2.78381 7.36771 2.78456C7.89308 2.78456 8.40158 2.96906 8.80546 3.30506C8.78708 3.31481 8.75558 3.33243 8.73458 3.34518L6.34508 4.72518C6.22283 4.79456 6.14783 4.92431 6.14858 5.06493L6.14708 8.4328ZM6.69571 7.25005L7.99508 6.49968L9.29446 7.24968V8.75005L7.99508 9.50005L6.69571 8.75005V7.25005Z" fill="white"></path> </svg></span><span data-v-f5c89177="">GPT-4o Mini</span></div><!----></div><div data-v-f5c89177="" class="dark:ait-bg-neutral-900 ait-px-6 ait-py-4 ai_result_container"><div data-v-f5c89177="" class="ait-flex ait-flex-row ait-items-center"><div data-v-f5c89177="" class="ai_result">Hello, how can I help you today?<br></div><!----></div><!----><div data-v-f5c89177="" class="ait-mt-3 ait-flex ait-items-center ait-float-right ait-cursor-pointer ait-gap-2 ait-divide-x-[0.5px] ait-divide-neutral-300 dark:ait-divide-neutral-700 ait-opacity-0"><div class="ait-bg-neutral-200 dark:ait-bg-neutral-950 ait-w-3 ait-h-3 ait-p-1 ait-rounded-md ait-justify-center ait-flex ait-items-center ait-float-right ait-cursor-pointer"><li data-v-2db9de5a="" class="ait-relative ait-block ait-popper-container"><svg class="svg-inline--fa fa-clipboard ait-w-3 ait-h-3" aria-hidden="true" focusable="false" data-prefix="far" data-icon="clipboard" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512"><path class="" fill="currentColor" d="M280 64l40 0c35.3 0 64 28.7 64 64l0 320c0 35.3-28.7 64-64 64L64 512c-35.3 0-64-28.7-64-64L0 128C0 92.7 28.7 64 64 64l40 0 9.6 0C121 27.5 153.3 0 192 0s71 27.5 78.4 64l9.6 0zM64 112c-8.8 0-16 7.2-16 16l0 320c0 8.8 7.2 16 16 16l256 0c8.8 0 16-7.2 16-16l0-320c0-8.8-7.2-16-16-16l-16 0 0 24c0 13.3-10.7 24-24 24l-88 0-88 0c-13.3 0-24-10.7-24-24l0-24-16 0zm128-8a24 24 0 1 0 0-48 24 24 0 1 0 0 48z"></path></svg><div data-v-2db9de5a="" class="ait-absolute ait-z-[9999999] ait-top-0 ait-left-1/2 ait-transform ait--translate-x-1/2 ait-py-1 ait-px-2 ait-rounded-md ait-shadow-lg dark:ait-bg-neutral-100 ait-bg-neutral-800 ait-text-gray-900 dark:ait-text-gray-200 ait-translate-y-[-120%] ait:border-[1px] dark:ait-border-neutral-200 ait-border-neutral-300" style="display: none;"><div data-v-2db9de5a="" class="ait-text-xs ait-font-medium ait-text-gray-200 dark:ait-text-gray-800 ait-whitespace-nowrap ait-max-w-[200px] ait-w-full">Copy</div></div><div data-v-2db9de5a="" class="ait-hide ait-left-0 ait-top-0 ait-bottom-0 ait-left-0 ait-top-1/2 ait-left-1/2 ait-right-1/2 ait-bottom-1/2 ait-translate-x-[-100%] ait-translate-y-[-100%] ait-translate-x-[-120%] ait-translate-y-[-120%]"></div></li></div><!----><!----></div><div data-v-f5c89177="" class="ait-clear-both"></div></div><!----><!----></div><div data-v-f5c89177="" aria-details="chat-key-1" class=""><div data-v-f5c89177="" class="ait-px-3 ait-bg-white dark:ait-bg-neutral-800 dark:ait-text-gray-200  ait-py-1"><div data-v-f5c89177="" class="ait-relative ait-flex ait-flex-col ait-gap-1 ait-w-2/3 ait-ml-auto ait-py-4"><!----><div data-v-f5c89177=""><div data-v-f5c89177="" class="ait-text-right">write answer for this "What is your motivation to consider joining RemotePass? *" as me like "I see its a nice emerging product where I can contribute my tech stuffs and build together"</div><!----><div data-v-f5c89177="" class="ait-mt-3 ait-flex ait-items-center ait-float-right ait-cursor-pointer ait-gap-2 ait-divide-x-[0.5px] ait-divide-neutral-300 dark:ait-divide-neutral-700 ait-opacity-0"><div class="ait-bg-neutral-200 dark:ait-bg-neutral-950 ait-w-3 ait-h-3 ait-p-1 ait-rounded-md ait-justify-center ait-flex ait-items-center ait-float-right ait-cursor-pointer"><li data-v-2db9de5a="" class="ait-relative ait-block ait-popper-container"><svg class="svg-inline--fa fa-clipboard ait-w-3 ait-h-3" aria-hidden="true" focusable="false" data-prefix="far" data-icon="clipboard" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512"><path class="" fill="currentColor" d="M280 64l40 0c35.3 0 64 28.7 64 64l0 320c0 35.3-28.7 64-64 64L64 512c-35.3 0-64-28.7-64-64L0 128C0 92.7 28.7 64 64 64l40 0 9.6 0C121 27.5 153.3 0 192 0s71 27.5 78.4 64l9.6 0zM64 112c-8.8 0-16 7.2-16 16l0 320c0 8.8 7.2 16 16 16l256 0c8.8 0 16-7.2 16-16l0-320c0-8.8-7.2-16-16-16l-16 0 0 24c0 13.3-10.7 24-24 24l-88 0-88 0c-13.3 0-24-10.7-24-24l0-24-16 0zm128-8a24 24 0 1 0 0-48 24 24 0 1 0 0 48z"></path></svg><div data-v-2db9de5a="" class="ait-absolute ait-z-[9999999] ait-top-0 ait-left-1/2 ait-transform ait--translate-x-1/2 ait-py-1 ait-px-2 ait-rounded-md ait-shadow-lg dark:ait-bg-neutral-100 ait-bg-neutral-800 ait-text-gray-900 dark:ait-text-gray-200 ait-translate-y-[-120%] ait:border-[1px] dark:ait-border-neutral-200 ait-border-neutral-300" style="display: none;"><div data-v-2db9de5a="" class="ait-text-xs ait-font-medium ait-text-gray-200 dark:ait-text-gray-800 ait-whitespace-nowrap ait-max-w-[200px] ait-w-full">Copy</div></div><div data-v-2db9de5a="" class="ait-hide ait-left-0 ait-top-0 ait-bottom-0 ait-left-0 ait-top-1/2 ait-left-1/2 ait-right-1/2 ait-bottom-1/2 ait-translate-x-[-100%] ait-translate-y-[-100%] ait-translate-x-[-120%] ait-translate-y-[-120%]"></div></li></div><!----></div></div></div></div><!----><!----></div><div data-v-f5c89177="" aria-details="chat-key-2" class="ait-bg-neutral-100 dark:ait-bg-neutral-900 dark:ait-text-gray-200 ait-mt-[2px] ait-mb-[2px]"><div data-v-f5c89177="" class="ait-flex ait-p-2 ait-text-[10px] dark:ait-text-gray-200 ait-flex-col ait-items-start ait-justify-start ait-gap-2"><div data-v-f5c89177="" class="ait-flex ait-items-center ait-space-x-2"><span data-v-f5c89177=""><svg class="ait-w-[16px] ait-h-[16px] ait-GPT-4o Mini" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none"> <rect width="16" height="16" rx="8" fill="#0BD6FF"></rect> <path d="M13.1348 6.91143C13.4071 6.0943 13.3133 5.19918 12.878 4.45593C12.2232 3.31593 10.907 2.72943 9.62146 3.00543C9.04958 2.36118 8.22796 1.99481 7.36658 2.00006C6.05258 1.99706 4.88671 2.84306 4.48246 4.09331C3.63833 4.26618 2.90971 4.79456 2.48333 5.54343C1.82371 6.68043 1.97408 8.11368 2.85533 9.08868C2.58308 9.9058 2.67683 10.8009 3.11221 11.5442C3.76696 12.6842 5.08321 13.2707 6.36871 12.9947C6.94021 13.6389 7.76221 14.0053 8.62358 13.9997C9.93833 14.0031 11.1046 13.1563 11.5088 11.9049C12.353 11.7321 13.0816 11.2037 13.508 10.4548C14.1668 9.3178 14.0157 7.88643 13.1348 6.91143ZM8.62433 13.2156C8.09821 13.2163 7.58858 13.0322 7.18471 12.6951C7.20308 12.6853 7.23496 12.6677 7.25558 12.6549L9.64508 11.2749C9.76733 11.2056 9.84233 11.0754 9.84158 10.9348V7.56618L10.8515 8.1493C10.8623 8.15455 10.8695 8.16505 10.871 8.17705V10.9667C10.8695 12.2072 9.86483 13.2129 8.62433 13.2156ZM3.79283 11.1519C3.52921 10.6967 3.43433 10.1631 3.52471 9.64518C3.54233 9.65568 3.57346 9.6748 3.59558 9.68755L5.98508 11.0676C6.10621 11.1384 6.25621 11.1384 6.37771 11.0676L9.29483 9.38305V10.5493C9.29558 10.5613 9.28996 10.5729 9.28058 10.5804L6.86521 11.9751C5.78933 12.5946 4.41496 12.2263 3.79283 11.1519ZM3.16396 5.93605C3.42646 5.48006 3.84083 5.1313 4.33433 4.95018C4.33433 4.97081 4.33321 5.00718 4.33321 5.03268V7.79305C4.33246 7.9333 4.40746 8.06343 4.52933 8.1328L7.44646 9.81693L6.43658 10.4001C6.42646 10.4068 6.41371 10.4079 6.40246 10.4031L3.98671 9.0073C2.91308 8.38555 2.54521 7.01155 3.16396 5.93605ZM11.4612 7.86693L8.54408 6.18243L9.55396 5.59968C9.56408 5.59293 9.57683 5.59181 9.58808 5.59668L12.0038 6.9913C13.0793 7.61268 13.448 8.98855 12.8266 10.0641C12.5637 10.5193 12.1497 10.8681 11.6566 11.0496V8.20668C11.6577 8.06643 11.5827 7.93668 11.4612 7.86693ZM12.4662 6.35418C12.4486 6.34331 12.4175 6.32455 12.3953 6.3118L10.0058 4.93181C9.88471 4.86093 9.73471 4.86093 9.61321 4.93181L6.69608 6.6163V5.45005C6.69533 5.43805 6.70096 5.42643 6.71033 5.41893L9.12571 4.02543C10.2016 3.40481 11.5771 3.77418 12.1973 4.85043C12.4595 5.30493 12.5551 5.83706 12.4662 6.35418ZM6.14708 8.4328L5.13683 7.84968C5.12596 7.84443 5.11883 7.83393 5.11733 7.82193V5.03231C5.11808 3.79031 6.12571 2.78381 7.36771 2.78456C7.89308 2.78456 8.40158 2.96906 8.80546 3.30506C8.78708 3.31481 8.75558 3.33243 8.73458 3.34518L6.34508 4.72518C6.22283 4.79456 6.14783 4.92431 6.14858 5.06493L6.14708 8.4328ZM6.69571 7.25005L7.99508 6.49968L9.29446 7.24968V8.75005L7.99508 9.50005L6.69571 8.75005V7.25005Z" fill="white"></path> </svg></span><span data-v-f5c89177="">GPT-4o Mini</span></div><!----></div><div data-v-f5c89177="" class="dark:ait-bg-neutral-900 ait-px-6 ait-py-4 ai_result_container"><div data-v-f5c89177="" class="ait-flex ait-flex-row ait-items-center"><div data-v-f5c89177="" class="ai_result">I am motivated to consider joining RemotePass because I see it as an exciting and emerging product in the remote work landscape. I am eager to contribute my technical skills and expertise to help shape its development. The opportunity to collaborate with a talented team and build something impactful is particularly appealing to me. I believe that by leveraging innovative technologies, we can enhance the remote work experience for users and drive the product's growth together.<br></div><!----></div><!----><div data-v-f5c89177="" class="ait-mt-3 ait-flex ait-items-center ait-float-right ait-cursor-pointer ait-gap-2 ait-divide-x-[0.5px] ait-divide-neutral-300 dark:ait-divide-neutral-700 ait-opacity-0"><div class="ait-bg-neutral-200 dark:ait-bg-neutral-950 ait-w-3 ait-h-3 ait-p-1 ait-rounded-md ait-justify-center ait-flex ait-items-center ait-float-right ait-cursor-pointer"><li data-v-2db9de5a="" class="ait-relative ait-block ait-popper-container"><svg class="svg-inline--fa fa-clipboard ait-w-3 ait-h-3" aria-hidden="true" focusable="false" data-prefix="far" data-icon="clipboard" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512"><path class="" fill="currentColor" d="M280 64l40 0c35.3 0 64 28.7 64 64l0 320c0 35.3-28.7 64-64 64L64 512c-35.3 0-64-28.7-64-64L0 128C0 92.7 28.7 64 64 64l40 0 9.6 0C121 27.5 153.3 0 192 0s71 27.5 78.4 64l9.6 0zM64 112c-8.8 0-16 7.2-16 16l0 320c0 8.8 7.2 16 16 16l256 0c8.8 0 16-7.2 16-16l0-320c0-8.8-7.2-16-16-16l-16 0 0 24c0 13.3-10.7 24-24 24l-88 0-88 0c-13.3 0-24-10.7-24-24l0-24-16 0zm128-8a24 24 0 1 0 0-48 24 24 0 1 0 0 48z"></path></svg><div data-v-2db9de5a="" class="ait-absolute ait-z-[9999999] ait-top-0 ait-left-1/2 ait-transform ait--translate-x-1/2 ait-py-1 ait-px-2 ait-rounded-md ait-shadow-lg dark:ait-bg-neutral-100 ait-bg-neutral-800 ait-text-gray-900 dark:ait-text-gray-200 ait-translate-y-[-120%] ait:border-[1px] dark:ait-border-neutral-200 ait-border-neutral-300" style="display: none;"><div data-v-2db9de5a="" class="ait-text-xs ait-font-medium ait-text-gray-200 dark:ait-text-gray-800 ait-whitespace-nowrap ait-max-w-[200px] ait-w-full">Copy</div></div><div data-v-2db9de5a="" class="ait-hide ait-left-0 ait-top-0 ait-bottom-0 ait-left-0 ait-top-1/2 ait-left-1/2 ait-right-1/2 ait-bottom-1/2 ait-translate-x-[-100%] ait-translate-y-[-100%] ait-translate-x-[-120%] ait-translate-y-[-120%]"></div></li></div><!----><!----></div><div data-v-f5c89177="" class="ait-clear-both"></div></div><!----><!----></div><div data-v-f5c89177="" aria-details="chat-key-3" class=""><div data-v-f5c89177="" class="ait-px-3 ait-bg-white dark:ait-bg-neutral-800 dark:ait-text-gray-200  ait-py-1"><div data-v-f5c89177="" class="ait-relative ait-flex ait-flex-col ait-gap-1 ait-w-2/3 ait-ml-auto ait-py-4"><!----><div data-v-f5c89177=""><div data-v-f5c89177="" class="ait-text-right">20000 aed in usd</div><!----><div data-v-f5c89177="" class="ait-mt-3 ait-flex ait-items-center ait-float-right ait-cursor-pointer ait-gap-2 ait-divide-x-[0.5px] ait-divide-neutral-300 dark:ait-divide-neutral-700 ait-opacity-0"><div class="ait-bg-neutral-200 dark:ait-bg-neutral-950 ait-w-3 ait-h-3 ait-p-1 ait-rounded-md ait-justify-center ait-flex ait-items-center ait-float-right ait-cursor-pointer"><li data-v-2db9de5a="" class="ait-relative ait-block ait-popper-container"><svg class="svg-inline--fa fa-clipboard ait-w-3 ait-h-3" aria-hidden="true" focusable="false" data-prefix="far" data-icon="clipboard" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512"><path class="" fill="currentColor" d="M280 64l40 0c35.3 0 64 28.7 64 64l0 320c0 35.3-28.7 64-64 64L64 512c-35.3 0-64-28.7-64-64L0 128C0 92.7 28.7 64 64 64l40 0 9.6 0C121 27.5 153.3 0 192 0s71 27.5 78.4 64l9.6 0zM64 112c-8.8 0-16 7.2-16 16l0 320c0 8.8 7.2 16 16 16l256 0c8.8 0 16-7.2 16-16l0-320c0-8.8-7.2-16-16-16l-16 0 0 24c0 13.3-10.7 24-24 24l-88 0-88 0c-13.3 0-24-10.7-24-24l0-24-16 0zm128-8a24 24 0 1 0 0-48 24 24 0 1 0 0 48z"></path></svg><div data-v-2db9de5a="" class="ait-absolute ait-z-[9999999] ait-top-0 ait-left-1/2 ait-transform ait--translate-x-1/2 ait-py-1 ait-px-2 ait-rounded-md ait-shadow-lg dark:ait-bg-neutral-100 ait-bg-neutral-800 ait-text-gray-900 dark:ait-text-gray-200 ait-translate-y-[-120%] ait:border-[1px] dark:ait-border-neutral-200 ait-border-neutral-300" style="display: none;"><div data-v-2db9de5a="" class="ait-text-xs ait-font-medium ait-text-gray-200 dark:ait-text-gray-800 ait-whitespace-nowrap ait-max-w-[200px] ait-w-full">Copy</div></div><div data-v-2db9de5a="" class="ait-hide ait-left-0 ait-top-0 ait-bottom-0 ait-left-0 ait-top-1/2 ait-left-1/2 ait-right-1/2 ait-bottom-1/2 ait-translate-x-[-100%] ait-translate-y-[-100%] ait-translate-x-[-120%] ait-translate-y-[-120%]"></div></li></div><!----></div></div></div></div><!----><!----></div><div data-v-f5c89177="" aria-details="chat-key-4" class="ait-bg-neutral-100 dark:ait-bg-neutral-900 dark:ait-text-gray-200 ait-mt-[2px] ait-mb-[2px]"><div data-v-f5c89177="" class="ait-flex ait-p-2 ait-text-[10px] dark:ait-text-gray-200 ait-flex-col ait-items-start ait-justify-start ait-gap-2"><div data-v-f5c89177="" class="ait-flex ait-items-center ait-space-x-2"><span data-v-f5c89177=""><svg class="ait-w-[16px] ait-h-[16px] ait-GPT-4o Mini" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none"> <rect width="16" height="16" rx="8" fill="#0BD6FF"></rect> <path d="M13.1348 6.91143C13.4071 6.0943 13.3133 5.19918 12.878 4.45593C12.2232 3.31593 10.907 2.72943 9.62146 3.00543C9.04958 2.36118 8.22796 1.99481 7.36658 2.00006C6.05258 1.99706 4.88671 2.84306 4.48246 4.09331C3.63833 4.26618 2.90971 4.79456 2.48333 5.54343C1.82371 6.68043 1.97408 8.11368 2.85533 9.08868C2.58308 9.9058 2.67683 10.8009 3.11221 11.5442C3.76696 12.6842 5.08321 13.2707 6.36871 12.9947C6.94021 13.6389 7.76221 14.0053 8.62358 13.9997C9.93833 14.0031 11.1046 13.1563 11.5088 11.9049C12.353 11.7321 13.0816 11.2037 13.508 10.4548C14.1668 9.3178 14.0157 7.88643 13.1348 6.91143ZM8.62433 13.2156C8.09821 13.2163 7.58858 13.0322 7.18471 12.6951C7.20308 12.6853 7.23496 12.6677 7.25558 12.6549L9.64508 11.2749C9.76733 11.2056 9.84233 11.0754 9.84158 10.9348V7.56618L10.8515 8.1493C10.8623 8.15455 10.8695 8.16505 10.871 8.17705V10.9667C10.8695 12.2072 9.86483 13.2129 8.62433 13.2156ZM3.79283 11.1519C3.52921 10.6967 3.43433 10.1631 3.52471 9.64518C3.54233 9.65568 3.57346 9.6748 3.59558 9.68755L5.98508 11.0676C6.10621 11.1384 6.25621 11.1384 6.37771 11.0676L9.29483 9.38305V10.5493C9.29558 10.5613 9.28996 10.5729 9.28058 10.5804L6.86521 11.9751C5.78933 12.5946 4.41496 12.2263 3.79283 11.1519ZM3.16396 5.93605C3.42646 5.48006 3.84083 5.1313 4.33433 4.95018C4.33433 4.97081 4.33321 5.00718 4.33321 5.03268V7.79305C4.33246 7.9333 4.40746 8.06343 4.52933 8.1328L7.44646 9.81693L6.43658 10.4001C6.42646 10.4068 6.41371 10.4079 6.40246 10.4031L3.98671 9.0073C2.91308 8.38555 2.54521 7.01155 3.16396 5.93605ZM11.4612 7.86693L8.54408 6.18243L9.55396 5.59968C9.56408 5.59293 9.57683 5.59181 9.58808 5.59668L12.0038 6.9913C13.0793 7.61268 13.448 8.98855 12.8266 10.0641C12.5637 10.5193 12.1497 10.8681 11.6566 11.0496V8.20668C11.6577 8.06643 11.5827 7.93668 11.4612 7.86693ZM12.4662 6.35418C12.4486 6.34331 12.4175 6.32455 12.3953 6.3118L10.0058 4.93181C9.88471 4.86093 9.73471 4.86093 9.61321 4.93181L6.69608 6.6163V5.45005C6.69533 5.43805 6.70096 5.42643 6.71033 5.41893L9.12571 4.02543C10.2016 3.40481 11.5771 3.77418 12.1973 4.85043C12.4595 5.30493 12.5551 5.83706 12.4662 6.35418ZM6.14708 8.4328L5.13683 7.84968C5.12596 7.84443 5.11883 7.83393 5.11733 7.82193V5.03231C5.11808 3.79031 6.12571 2.78381 7.36771 2.78456C7.89308 2.78456 8.40158 2.96906 8.80546 3.30506C8.78708 3.31481 8.75558 3.33243 8.73458 3.34518L6.34508 4.72518C6.22283 4.79456 6.14783 4.92431 6.14858 5.06493L6.14708 8.4328ZM6.69571 7.25005L7.99508 6.49968L9.29446 7.24968V8.75005L7.99508 9.50005L6.69571 8.75005V7.25005Z" fill="white"></path> </svg></span><span data-v-f5c89177="">GPT-4o Mini</span></div><!----></div><div data-v-f5c89177="" class="dark:ait-bg-neutral-900 ait-px-6 ait-py-4 ai_result_container"><div data-v-f5c89177="" class="ait-flex ait-flex-row ait-items-center"><div data-v-f5c89177="" class="ai_result">As of my last knowledge update, the exchange rate fluctuates, but 20,000 AED is approximately around 5,400 USD. For the most accurate and current conversion, please check a reliable financial news source or currency converter.<br></div><!----></div><!----><div data-v-f5c89177="" class="ait-mt-3 ait-flex ait-items-center ait-float-right ait-cursor-pointer ait-gap-2 ait-divide-x-[0.5px] ait-divide-neutral-300 dark:ait-divide-neutral-700 ait-opacity-0"><div class="ait-bg-neutral-200 dark:ait-bg-neutral-950 ait-w-3 ait-h-3 ait-p-1 ait-rounded-md ait-justify-center ait-flex ait-items-center ait-float-right ait-cursor-pointer"><li data-v-2db9de5a="" class="ait-relative ait-block ait-popper-container"><svg class="svg-inline--fa fa-clipboard ait-w-3 ait-h-3" aria-hidden="true" focusable="false" data-prefix="far" data-icon="clipboard" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512"><path class="" fill="currentColor" d="M280 64l40 0c35.3 0 64 28.7 64 64l0 320c0 35.3-28.7 64-64 64L64 512c-35.3 0-64-28.7-64-64L0 128C0 92.7 28.7 64 64 64l40 0 9.6 0C121 27.5 153.3 0 192 0s71 27.5 78.4 64l9.6 0zM64 112c-8.8 0-16 7.2-16 16l0 320c0 8.8 7.2 16 16 16l256 0c8.8 0 16-7.2 16-16l0-320c0-8.8-7.2-16-16-16l-16 0 0 24c0 13.3-10.7 24-24 24l-88 0-88 0c-13.3 0-24-10.7-24-24l0-24-16 0zm128-8a24 24 0 1 0 0-48 24 24 0 1 0 0 48z"></path></svg><div data-v-2db9de5a="" class="ait-absolute ait-z-[9999999] ait-top-0 ait-left-1/2 ait-transform ait--translate-x-1/2 ait-py-1 ait-px-2 ait-rounded-md ait-shadow-lg dark:ait-bg-neutral-100 ait-bg-neutral-800 ait-text-gray-900 dark:ait-text-gray-200 ait-translate-y-[-120%] ait:border-[1px] dark:ait-border-neutral-200 ait-border-neutral-300" style="display: none;"><div data-v-2db9de5a="" class="ait-text-xs ait-font-medium ait-text-gray-200 dark:ait-text-gray-800 ait-whitespace-nowrap ait-max-w-[200px] ait-w-full">Copy</div></div><div data-v-2db9de5a="" class="ait-hide ait-left-0 ait-top-0 ait-bottom-0 ait-left-0 ait-top-1/2 ait-left-1/2 ait-right-1/2 ait-bottom-1/2 ait-translate-x-[-100%] ait-translate-y-[-100%] ait-translate-x-[-120%] ait-translate-y-[-120%]"></div></li></div><!----><!----></div><div data-v-f5c89177="" class="ait-clear-both"></div></div><!----><!----></div><div data-v-f5c89177="" aria-details="chat-key-5" class=""><div data-v-f5c89177="" class="ait-px-3 ait-bg-white dark:ait-bg-neutral-800 dark:ait-text-gray-200  ait-py-1"><div data-v-f5c89177="" class="ait-relative ait-flex ait-flex-col ait-gap-1 ait-w-2/3 ait-ml-auto ait-py-4"><!----><div data-v-f5c89177=""><div data-v-f5c89177="" class="ait-text-right">Explain Artificial Intelligence so that I can explain it to my six-year-old child.</div><!----><div data-v-f5c89177="" class="ait-mt-3 ait-flex ait-items-center ait-float-right ait-cursor-pointer ait-gap-2 ait-divide-x-[0.5px] ait-divide-neutral-300 dark:ait-divide-neutral-700 ait-opacity-0"><div class="ait-bg-neutral-200 dark:ait-bg-neutral-950 ait-w-3 ait-h-3 ait-p-1 ait-rounded-md ait-justify-center ait-flex ait-items-center ait-float-right ait-cursor-pointer"><li data-v-2db9de5a="" class="ait-relative ait-block ait-popper-container"><svg class="svg-inline--fa fa-clipboard ait-w-3 ait-h-3" aria-hidden="true" focusable="false" data-prefix="far" data-icon="clipboard" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512"><path class="" fill="currentColor" d="M280 64l40 0c35.3 0 64 28.7 64 64l0 320c0 35.3-28.7 64-64 64L64 512c-35.3 0-64-28.7-64-64L0 128C0 92.7 28.7 64 64 64l40 0 9.6 0C121 27.5 153.3 0 192 0s71 27.5 78.4 64l9.6 0zM64 112c-8.8 0-16 7.2-16 16l0 320c0 8.8 7.2 16 16 16l256 0c8.8 0 16-7.2 16-16l0-320c0-8.8-7.2-16-16-16l-16 0 0 24c0 13.3-10.7 24-24 24l-88 0-88 0c-13.3 0-24-10.7-24-24l0-24-16 0zm128-8a24 24 0 1 0 0-48 24 24 0 1 0 0 48z"></path></svg><div data-v-2db9de5a="" class="ait-absolute ait-z-[9999999] ait-top-0 ait-left-1/2 ait-transform ait--translate-x-1/2 ait-py-1 ait-px-2 ait-rounded-md ait-shadow-lg dark:ait-bg-neutral-100 ait-bg-neutral-800 ait-text-gray-900 dark:ait-text-gray-200 ait-translate-y-[-120%] ait:border-[1px] dark:ait-border-neutral-200 ait-border-neutral-300" style="display: none;"><div data-v-2db9de5a="" class="ait-text-xs ait-font-medium ait-text-gray-200 dark:ait-text-gray-800 ait-whitespace-nowrap ait-max-w-[200px] ait-w-full">Copy</div></div><div data-v-2db9de5a="" class="ait-hide ait-left-0 ait-top-0 ait-bottom-0 ait-left-0 ait-top-1/2 ait-left-1/2 ait-right-1/2 ait-bottom-1/2 ait-translate-x-[-100%] ait-translate-y-[-100%] ait-translate-x-[-120%] ait-translate-y-[-120%]"></div></li></div><!----></div></div></div></div><!----><!----></div><div data-v-f5c89177="" aria-details="chat-key-6" class="ait-bg-neutral-100 dark:ait-bg-neutral-900 dark:ait-text-gray-200 ait-mt-[2px] ait-mb-[2px]"><div data-v-f5c89177="" class="ait-flex ait-p-2 ait-text-[10px] dark:ait-text-gray-200 ait-flex-col ait-items-start ait-justify-start ait-gap-2"><div data-v-f5c89177="" class="ait-flex ait-items-center ait-space-x-2"><span data-v-f5c89177=""><svg class="ait-w-[16px] ait-h-[16px] ait-GPT-4o Mini" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none"> <rect width="16" height="16" rx="8" fill="#0BD6FF"></rect> <path d="M13.1348 6.91143C13.4071 6.0943 13.3133 5.19918 12.878 4.45593C12.2232 3.31593 10.907 2.72943 9.62146 3.00543C9.04958 2.36118 8.22796 1.99481 7.36658 2.00006C6.05258 1.99706 4.88671 2.84306 4.48246 4.09331C3.63833 4.26618 2.90971 4.79456 2.48333 5.54343C1.82371 6.68043 1.97408 8.11368 2.85533 9.08868C2.58308 9.9058 2.67683 10.8009 3.11221 11.5442C3.76696 12.6842 5.08321 13.2707 6.36871 12.9947C6.94021 13.6389 7.76221 14.0053 8.62358 13.9997C9.93833 14.0031 11.1046 13.1563 11.5088 11.9049C12.353 11.7321 13.0816 11.2037 13.508 10.4548C14.1668 9.3178 14.0157 7.88643 13.1348 6.91143ZM8.62433 13.2156C8.09821 13.2163 7.58858 13.0322 7.18471 12.6951C7.20308 12.6853 7.23496 12.6677 7.25558 12.6549L9.64508 11.2749C9.76733 11.2056 9.84233 11.0754 9.84158 10.9348V7.56618L10.8515 8.1493C10.8623 8.15455 10.8695 8.16505 10.871 8.17705V10.9667C10.8695 12.2072 9.86483 13.2129 8.62433 13.2156ZM3.79283 11.1519C3.52921 10.6967 3.43433 10.1631 3.52471 9.64518C3.54233 9.65568 3.57346 9.6748 3.59558 9.68755L5.98508 11.0676C6.10621 11.1384 6.25621 11.1384 6.37771 11.0676L9.29483 9.38305V10.5493C9.29558 10.5613 9.28996 10.5729 9.28058 10.5804L6.86521 11.9751C5.78933 12.5946 4.41496 12.2263 3.79283 11.1519ZM3.16396 5.93605C3.42646 5.48006 3.84083 5.1313 4.33433 4.95018C4.33433 4.97081 4.33321 5.00718 4.33321 5.03268V7.79305C4.33246 7.9333 4.40746 8.06343 4.52933 8.1328L7.44646 9.81693L6.43658 10.4001C6.42646 10.4068 6.41371 10.4079 6.40246 10.4031L3.98671 9.0073C2.91308 8.38555 2.54521 7.01155 3.16396 5.93605ZM11.4612 7.86693L8.54408 6.18243L9.55396 5.59968C9.56408 5.59293 9.57683 5.59181 9.58808 5.59668L12.0038 6.9913C13.0793 7.61268 13.448 8.98855 12.8266 10.0641C12.5637 10.5193 12.1497 10.8681 11.6566 11.0496V8.20668C11.6577 8.06643 11.5827 7.93668 11.4612 7.86693ZM12.4662 6.35418C12.4486 6.34331 12.4175 6.32455 12.3953 6.3118L10.0058 4.93181C9.88471 4.86093 9.73471 4.86093 9.61321 4.93181L6.69608 6.6163V5.45005C6.69533 5.43805 6.70096 5.42643 6.71033 5.41893L9.12571 4.02543C10.2016 3.40481 11.5771 3.77418 12.1973 4.85043C12.4595 5.30493 12.5551 5.83706 12.4662 6.35418ZM6.14708 8.4328L5.13683 7.84968C5.12596 7.84443 5.11883 7.83393 5.11733 7.82193V5.03231C5.11808 3.79031 6.12571 2.78381 7.36771 2.78456C7.89308 2.78456 8.40158 2.96906 8.80546 3.30506C8.78708 3.31481 8.75558 3.33243 8.73458 3.34518L6.34508 4.72518C6.22283 4.79456 6.14783 4.92431 6.14858 5.06493L6.14708 8.4328ZM6.69571 7.25005L7.99508 6.49968L9.29446 7.24968V8.75005L7.99508 9.50005L6.69571 8.75005V7.25005Z" fill="white"></path> </svg></span><span data-v-f5c89177="">GPT-4o Mini</span></div><!----></div><div data-v-f5c89177="" class="dark:ait-bg-neutral-900 ait-px-6 ait-py-4 ai_result_container"><div data-v-f5c89177="" class="ait-flex ait-flex-row ait-items-center"><div data-v-f5c89177="" class="ai_result">Sure!<br></div><!----></div><!----><div data-v-f5c89177="" class="ait-mt-3 ait-flex ait-items-center ait-float-right ait-cursor-pointer ait-gap-2 ait-divide-x-[0.5px] ait-divide-neutral-300 dark:ait-divide-neutral-700 ait-opacity-0"><div class="ait-bg-neutral-200 dark:ait-bg-neutral-950 ait-w-3 ait-h-3 ait-p-1 ait-rounded-md ait-justify-center ait-flex ait-items-center ait-float-right ait-cursor-pointer"><li data-v-2db9de5a="" class="ait-relative ait-block ait-popper-container"><svg class="svg-inline--fa fa-clipboard ait-w-3 ait-h-3" aria-hidden="true" focusable="false" data-prefix="far" data-icon="clipboard" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512"><path class="" fill="currentColor" d="M280 64l40 0c35.3 0 64 28.7 64 64l0 320c0 35.3-28.7 64-64 64L64 512c-35.3 0-64-28.7-64-64L0 128C0 92.7 28.7 64 64 64l40 0 9.6 0C121 27.5 153.3 0 192 0s71 27.5 78.4 64l9.6 0zM64 112c-8.8 0-16 7.2-16 16l0 320c0 8.8 7.2 16 16 16l256 0c8.8 0 16-7.2 16-16l0-320c0-8.8-7.2-16-16-16l-16 0 0 24c0 13.3-10.7 24-24 24l-88 0-88 0c-13.3 0-24-10.7-24-24l0-24-16 0zm128-8a24 24 0 1 0 0-48 24 24 0 1 0 0 48z"></path></svg><div data-v-2db9de5a="" class="ait-absolute ait-z-[9999999] ait-top-0 ait-left-1/2 ait-transform ait--translate-x-1/2 ait-py-1 ait-px-2 ait-rounded-md ait-shadow-lg dark:ait-bg-neutral-100 ait-bg-neutral-800 ait-text-gray-900 dark:ait-text-gray-200 ait-translate-y-[-120%] ait:border-[1px] dark:ait-border-neutral-200 ait-border-neutral-300" style="display: none;"><div data-v-2db9de5a="" class="ait-text-xs ait-font-medium ait-text-gray-200 dark:ait-text-gray-800 ait-whitespace-nowrap ait-max-w-[200px] ait-w-full">Copy</div></div><div data-v-2db9de5a="" class="ait-hide ait-left-0 ait-top-0 ait-bottom-0 ait-left-0 ait-top-1/2 ait-left-1/2 ait-right-1/2 ait-bottom-1/2 ait-translate-x-[-100%] ait-translate-y-[-100%] ait-translate-x-[-120%] ait-translate-y-[-120%]"></div></li></div><!----><!----></div><div data-v-f5c89177="" class="ait-clear-both"></div></div><!----><!----></div><div data-v-f5c89177="" aria-details="chat-key-7" class=""><div data-v-f5c89177="" class="ait-px-3 ait-bg-white dark:ait-bg-neutral-800 dark:ait-text-gray-200  ait-py-1"><div data-v-f5c89177="" class="ait-relative ait-flex ait-flex-col ait-gap-1 ait-w-2/3 ait-ml-auto ait-py-4"><!----><div data-v-f5c89177=""><div data-v-f5c89177="" class="ait-text-right">Describe your involvement in a production project which you are particularly proud of or contribution to a web framework?&nbsp;*</div><!----><div data-v-f5c89177="" class="ait-mt-3 ait-flex ait-items-center ait-float-right ait-cursor-pointer ait-gap-2 ait-divide-x-[0.5px] ait-divide-neutral-300 dark:ait-divide-neutral-700 ait-opacity-0"><div class="ait-bg-neutral-200 dark:ait-bg-neutral-950 ait-w-3 ait-h-3 ait-p-1 ait-rounded-md ait-justify-center ait-flex ait-items-center ait-float-right ait-cursor-pointer"><li data-v-2db9de5a="" class="ait-relative ait-block ait-popper-container"><svg class="svg-inline--fa fa-clipboard ait-w-3 ait-h-3" aria-hidden="true" focusable="false" data-prefix="far" data-icon="clipboard" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512"><path class="" fill="currentColor" d="M280 64l40 0c35.3 0 64 28.7 64 64l0 320c0 35.3-28.7 64-64 64L64 512c-35.3 0-64-28.7-64-64L0 128C0 92.7 28.7 64 64 64l40 0 9.6 0C121 27.5 153.3 0 192 0s71 27.5 78.4 64l9.6 0zM64 112c-8.8 0-16 7.2-16 16l0 320c0 8.8 7.2 16 16 16l256 0c8.8 0 16-7.2 16-16l0-320c0-8.8-7.2-16-16-16l-16 0 0 24c0 13.3-10.7 24-24 24l-88 0-88 0c-13.3 0-24-10.7-24-24l0-24-16 0zm128-8a24 24 0 1 0 0-48 24 24 0 1 0 0 48z"></path></svg><div data-v-2db9de5a="" class="ait-absolute ait-z-[9999999] ait-top-0 ait-left-1/2 ait-transform ait--translate-x-1/2 ait-py-1 ait-px-2 ait-rounded-md ait-shadow-lg dark:ait-bg-neutral-100 ait-bg-neutral-800 ait-text-gray-900 dark:ait-text-gray-200 ait-translate-y-[-120%] ait:border-[1px] dark:ait-border-neutral-200 ait-border-neutral-300" style="display: none;"><div data-v-2db9de5a="" class="ait-text-xs ait-font-medium ait-text-gray-200 dark:ait-text-gray-800 ait-whitespace-nowrap ait-max-w-[200px] ait-w-full">Copy</div></div><div data-v-2db9de5a="" class="ait-hide ait-left-0 ait-top-0 ait-bottom-0 ait-left-0 ait-top-1/2 ait-left-1/2 ait-right-1/2 ait-bottom-1/2 ait-translate-x-[-100%] ait-translate-y-[-100%] ait-translate-x-[-120%] ait-translate-y-[-120%]"></div></li></div><!----></div></div></div></div><!----><!----></div><div data-v-f5c89177="" aria-details="chat-key-8" class="ait-bg-neutral-100 dark:ait-bg-neutral-900 dark:ait-text-gray-200 ait-mt-[2px] ait-mb-[2px]"><div data-v-f5c89177="" class="ait-flex ait-p-2 ait-text-[10px] dark:ait-text-gray-200 ait-flex-col ait-items-start ait-justify-start ait-gap-2"><div data-v-f5c89177="" class="ait-flex ait-items-center ait-space-x-2"><span data-v-f5c89177=""><svg class="ait-w-[16px] ait-h-[16px] ait-GPT-4o Mini" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none"> <rect width="16" height="16" rx="8" fill="#0BD6FF"></rect> <path d="M13.1348 6.91143C13.4071 6.0943 13.3133 5.19918 12.878 4.45593C12.2232 3.31593 10.907 2.72943 9.62146 3.00543C9.04958 2.36118 8.22796 1.99481 7.36658 2.00006C6.05258 1.99706 4.88671 2.84306 4.48246 4.09331C3.63833 4.26618 2.90971 4.79456 2.48333 5.54343C1.82371 6.68043 1.97408 8.11368 2.85533 9.08868C2.58308 9.9058 2.67683 10.8009 3.11221 11.5442C3.76696 12.6842 5.08321 13.2707 6.36871 12.9947C6.94021 13.6389 7.76221 14.0053 8.62358 13.9997C9.93833 14.0031 11.1046 13.1563 11.5088 11.9049C12.353 11.7321 13.0816 11.2037 13.508 10.4548C14.1668 9.3178 14.0157 7.88643 13.1348 6.91143ZM8.62433 13.2156C8.09821 13.2163 7.58858 13.0322 7.18471 12.6951C7.20308 12.6853 7.23496 12.6677 7.25558 12.6549L9.64508 11.2749C9.76733 11.2056 9.84233 11.0754 9.84158 10.9348V7.56618L10.8515 8.1493C10.8623 8.15455 10.8695 8.16505 10.871 8.17705V10.9667C10.8695 12.2072 9.86483 13.2129 8.62433 13.2156ZM3.79283 11.1519C3.52921 10.6967 3.43433 10.1631 3.52471 9.64518C3.54233 9.65568 3.57346 9.6748 3.59558 9.68755L5.98508 11.0676C6.10621 11.1384 6.25621 11.1384 6.37771 11.0676L9.29483 9.38305V10.5493C9.29558 10.5613 9.28996 10.5729 9.28058 10.5804L6.86521 11.9751C5.78933 12.5946 4.41496 12.2263 3.79283 11.1519ZM3.16396 5.93605C3.42646 5.48006 3.84083 5.1313 4.33433 4.95018C4.33433 4.97081 4.33321 5.00718 4.33321 5.03268V7.79305C4.33246 7.9333 4.40746 8.06343 4.52933 8.1328L7.44646 9.81693L6.43658 10.4001C6.42646 10.4068 6.41371 10.4079 6.40246 10.4031L3.98671 9.0073C2.91308 8.38555 2.54521 7.01155 3.16396 5.93605ZM11.4612 7.86693L8.54408 6.18243L9.55396 5.59968C9.56408 5.59293 9.57683 5.59181 9.58808 5.59668L12.0038 6.9913C13.0793 7.61268 13.448 8.98855 12.8266 10.0641C12.5637 10.5193 12.1497 10.8681 11.6566 11.0496V8.20668C11.6577 8.06643 11.5827 7.93668 11.4612 7.86693ZM12.4662 6.35418C12.4486 6.34331 12.4175 6.32455 12.3953 6.3118L10.0058 4.93181C9.88471 4.86093 9.73471 4.86093 9.61321 4.93181L6.69608 6.6163V5.45005C6.69533 5.43805 6.70096 5.42643 6.71033 5.41893L9.12571 4.02543C10.2016 3.40481 11.5771 3.77418 12.1973 4.85043C12.4595 5.30493 12.5551 5.83706 12.4662 6.35418ZM6.14708 8.4328L5.13683 7.84968C5.12596 7.84443 5.11883 7.83393 5.11733 7.82193V5.03231C5.11808 3.79031 6.12571 2.78381 7.36771 2.78456C7.89308 2.78456 8.40158 2.96906 8.80546 3.30506C8.78708 3.31481 8.75558 3.33243 8.73458 3.34518L6.34508 4.72518C6.22283 4.79456 6.14783 4.92431 6.14858 5.06493L6.14708 8.4328ZM6.69571 7.25005L7.99508 6.49968L9.29446 7.24968V8.75005L7.99508 9.50005L6.69571 8.75005V7.25005Z" fill="white"></path> </svg></span><span data-v-f5c89177="">GPT-4o Mini</span></div><!----></div><div data-v-f5c89177="" class="dark:ait-bg-neutral-900 ait-px-6 ait-py-4 ai_result_container"><div data-v-f5c89177="" class="ait-flex ait-flex-row ait-items-center"><div data-v-f5c89177="" class="ai_result">One production project I’m particularly proud of involved developing a web application for a local nonprofit organization. The goal was to create a platform where volunteers could sign up for events, and the organization could easily manage their activities.<br>My contribution included:
<ol>
<li><strong>Planning and Design:</strong> I worked closely with the nonprofit team to understand their needs. We created simple sketches of how the website should look and what features it would need.
</li>
<li><strong>Choosing a Web Framework:</strong> After considering different options, we decided to use Django because it’s great for building websites quickly and has built-in tools that help with managing data.
</li>
<li><strong>Developing Features:</strong> I wrote code to allow users to register, log in, and view upcoming events. I also implemented an admin panel that made it easy for the staff to add new events and track volunteer sign-ups.
</li>
<li><strong>Testing:</strong> Before launching, I tested everything thoroughly to make sure there were no bugs or glitches. This step is crucial because we wanted volunteers to have a smooth experience when using the site.
</li>
<li><strong>Launch &amp; Feedback:</strong> Once we launched the website, I gathered feedback from both volunteers and staff members on how it was working for them. Based on their suggestions, we made updates that improved functionality.
</li>
<li><strong>Training &amp; Support:</strong> Finally, I provided training sessions for the staff so they would feel comfortable using the new system without any issues.
</li>
</ol>
Seeing how this website helped streamline their operations and encouraged more people to volunteer made me really proud of my work on this project!
</div><!----></div><!----><div data-v-f5c89177="" class="ait-mt-3 ait-flex ait-items-center ait-float-right ait-cursor-pointer ait-gap-2 ait-divide-x-[0.5px] ait-divide-neutral-300 dark:ait-divide-neutral-700"><div class="ait-bg-neutral-200 dark:ait-bg-neutral-950 ait-w-3 ait-h-3 ait-p-1 ait-rounded-md ait-justify-center ait-flex ait-items-center ait-float-right ait-cursor-pointer"><li data-v-2db9de5a="" class="ait-relative ait-block ait-popper-container"><svg class="svg-inline--fa fa-clipboard ait-w-3 ait-h-3" aria-hidden="true" focusable="false" data-prefix="far" data-icon="clipboard" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512"><path class="" fill="currentColor" d="M280 64l40 0c35.3 0 64 28.7 64 64l0 320c0 35.3-28.7 64-64 64L64 512c-35.3 0-64-28.7-64-64L0 128C0 92.7 28.7 64 64 64l40 0 9.6 0C121 27.5 153.3 0 192 0s71 27.5 78.4 64l9.6 0zM64 112c-8.8 0-16 7.2-16 16l0 320c0 8.8 7.2 16 16 16l256 0c8.8 0 16-7.2 16-16l0-320c0-8.8-7.2-16-16-16l-16 0 0 24c0 13.3-10.7 24-24 24l-88 0-88 0c-13.3 0-24-10.7-24-24l0-24-16 0zm128-8a24 24 0 1 0 0-48 24 24 0 1 0 0 48z"></path></svg><div data-v-2db9de5a="" class="ait-absolute ait-z-[9999999] ait-top-0 ait-left-1/2 ait-transform ait--translate-x-1/2 ait-py-1 ait-px-2 ait-rounded-md ait-shadow-lg dark:ait-bg-neutral-100 ait-bg-neutral-800 ait-text-gray-900 dark:ait-text-gray-200 ait-translate-y-[-120%] ait:border-[1px] dark:ait-border-neutral-200 ait-border-neutral-300" style="display: none;"><div data-v-2db9de5a="" class="ait-text-xs ait-font-medium ait-text-gray-200 dark:ait-text-gray-800 ait-whitespace-nowrap ait-max-w-[200px] ait-w-full">Copy</div></div><div data-v-2db9de5a="" class="ait-hide ait-left-0 ait-top-0 ait-bottom-0 ait-left-0 ait-top-1/2 ait-left-1/2 ait-right-1/2 ait-bottom-1/2 ait-translate-x-[-100%] ait-translate-y-[-100%] ait-translate-x-[-120%] ait-translate-y-[-120%]"></div></li></div><!----><!----></div><div data-v-f5c89177="" class="ait-clear-both"></div></div><!----><!----></div><div data-v-f5c89177="" id="ait-stop-chat-ai_chat" class="ait-stop-chat ait-hide"><button data-v-f5c89177="" class="ait-text-white hover:ait-bg-green-800 ait-font-medium ait-text-md ait-px-5 ait-py-2 ait-rounded-md dark:hover:ait-bg-[var(--ait-tab-menu-active-bg-color)]"><div data-v-f5c89177="" class="ait-flex ait-w-full ait-gap-2 ait-items-center ait-justify-center"><svg data-v-f5c89177="" stroke="currentColor" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke-linecap="round" stroke-linejoin="round" class="icon-xs" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><rect data-v-f5c89177="" x="3" y="3" width="18" height="18" rx="2" ry="2"></rect></svg> Stop generating</div></button></div></div></div></div></div><div class="ait-px-3 ait-pt-3"><div class="ait-flex ait-relative ait-w-full ait-items-center ait-justify-start ait-my-3 ait-grid ait-grid-cols-5 lg:ait-grid-none lg:grid-cols-2"><div class="ait-flex ait-w-full ait-gap-3 ait-items-center ait-col-span-5 lg:ait-col-span-none"><div class="ait-flex ait-flex-wrap ait-gap-[6px] ait-max-w-full ait-place-self-end ait-self-center ait-min-w-[55px]"><div class="ait-text-xs ait-relative ait-min-w-[55px]" data-ait-dropdown-toggle="ait-model-dropdown" data-placement="top-start"><div class="ait-flex ait-justify-center ait-min-h-full ait-max-h-full ait-items-center ait-leading-4 ait-gap-[2px] lg:ait-flex ait-space-x-1 ait-text-sm ait-relative dark:ait-text-neutral-200 ait-border ait-rounded-xl ait-cursor-pointer ait-pl-[6px] ait-pr-[5px] ait-py-[7px]"><div class="ait-ai-icon ait-flex ait-justify-center ait-items-center ait-rounded-full ait-h-4 ait-w-4 ait-ml-[1px] ait-mr-[2px]"><div class="ait-flex ait-items-center ait-justify-center ait-rounded-full"><span><svg class="ait-w-[16px] ait-h-[16px] ait-GPT-4o Mini" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none"> <rect width="16" height="16" rx="8" fill="#0BD6FF"></rect> <path d="M13.1348 6.91143C13.4071 6.0943 13.3133 5.19918 12.878 4.45593C12.2232 3.31593 10.907 2.72943 9.62146 3.00543C9.04958 2.36118 8.22796 1.99481 7.36658 2.00006C6.05258 1.99706 4.88671 2.84306 4.48246 4.09331C3.63833 4.26618 2.90971 4.79456 2.48333 5.54343C1.82371 6.68043 1.97408 8.11368 2.85533 9.08868C2.58308 9.9058 2.67683 10.8009 3.11221 11.5442C3.76696 12.6842 5.08321 13.2707 6.36871 12.9947C6.94021 13.6389 7.76221 14.0053 8.62358 13.9997C9.93833 14.0031 11.1046 13.1563 11.5088 11.9049C12.353 11.7321 13.0816 11.2037 13.508 10.4548C14.1668 9.3178 14.0157 7.88643 13.1348 6.91143ZM8.62433 13.2156C8.09821 13.2163 7.58858 13.0322 7.18471 12.6951C7.20308 12.6853 7.23496 12.6677 7.25558 12.6549L9.64508 11.2749C9.76733 11.2056 9.84233 11.0754 9.84158 10.9348V7.56618L10.8515 8.1493C10.8623 8.15455 10.8695 8.16505 10.871 8.17705V10.9667C10.8695 12.2072 9.86483 13.2129 8.62433 13.2156ZM3.79283 11.1519C3.52921 10.6967 3.43433 10.1631 3.52471 9.64518C3.54233 9.65568 3.57346 9.6748 3.59558 9.68755L5.98508 11.0676C6.10621 11.1384 6.25621 11.1384 6.37771 11.0676L9.29483 9.38305V10.5493C9.29558 10.5613 9.28996 10.5729 9.28058 10.5804L6.86521 11.9751C5.78933 12.5946 4.41496 12.2263 3.79283 11.1519ZM3.16396 5.93605C3.42646 5.48006 3.84083 5.1313 4.33433 4.95018C4.33433 4.97081 4.33321 5.00718 4.33321 5.03268V7.79305C4.33246 7.9333 4.40746 8.06343 4.52933 8.1328L7.44646 9.81693L6.43658 10.4001C6.42646 10.4068 6.41371 10.4079 6.40246 10.4031L3.98671 9.0073C2.91308 8.38555 2.54521 7.01155 3.16396 5.93605ZM11.4612 7.86693L8.54408 6.18243L9.55396 5.59968C9.56408 5.59293 9.57683 5.59181 9.58808 5.59668L12.0038 6.9913C13.0793 7.61268 13.448 8.98855 12.8266 10.0641C12.5637 10.5193 12.1497 10.8681 11.6566 11.0496V8.20668C11.6577 8.06643 11.5827 7.93668 11.4612 7.86693ZM12.4662 6.35418C12.4486 6.34331 12.4175 6.32455 12.3953 6.3118L10.0058 4.93181C9.88471 4.86093 9.73471 4.86093 9.61321 4.93181L6.69608 6.6163V5.45005C6.69533 5.43805 6.70096 5.42643 6.71033 5.41893L9.12571 4.02543C10.2016 3.40481 11.5771 3.77418 12.1973 4.85043C12.4595 5.30493 12.5551 5.83706 12.4662 6.35418ZM6.14708 8.4328L5.13683 7.84968C5.12596 7.84443 5.11883 7.83393 5.11733 7.82193V5.03231C5.11808 3.79031 6.12571 2.78381 7.36771 2.78456C7.89308 2.78456 8.40158 2.96906 8.80546 3.30506C8.78708 3.31481 8.75558 3.33243 8.73458 3.34518L6.34508 4.72518C6.22283 4.79456 6.14783 4.92431 6.14858 5.06493L6.14708 8.4328ZM6.69571 7.25005L7.99508 6.49968L9.29446 7.24968V8.75005L7.99508 9.50005L6.69571 8.75005V7.25005Z" fill="white"></path> </svg></span></div></div><span class="ait-inline-block ait-text-[13px] ait-text-ellipsis ait-whitespace-nowrap ait-overflow-hidden ait-max-w-full">GPT-4o Mini</span><div class="ait-flex ait-items-end pl-[2px] pr-[2px]"><svg aria-hide="true" fill="currentColor" focusable="false" height="12" role="img" viewBox="0 0 12 12" width="12" style="display: inline-block; user-select: none; vertical-align: text-bottom; overflow: visible;"><path d="M6 8.825c-.2 0-.4-.1-.5-.2l-3.3-3.3c-.3-.3-.3-.8 0-1.1.3-.3.8-.3 1.1 0l2.7 2.7 2.7-2.7c.3-.3.8-.3 1.1 0 .******* 0 1.1l-3.2 3.2c-.2.2-.4.3-.6.3Z"></path></svg></div></div><ul class="ait-model-dropdown ait-z-20 ait-width-full ait-hide ait-w-max ait-min-w-[180px] ait-max-w-[250px] ait-bg-white ait-divide-y ait-divide-neutral-100 ait-rounded-lg ait-shadow dark:ait-bg-neutral-900 dark:ait-divide-neutral-700"><div class="ait-divide-y ait-divide-neutral-100 dark:ait-divide-neutral-700"><li data-v-2db9de5a="" class="ait-relative ait-block ait-popper-container"><div class="ait-flex ait-px-4 ait-py-2.5 hover:ait-bg-neutral-100 dark:hover:ait-bg-neutral-700 hover:ait-rounded-lg ait-cursor-pointer" style="background: rgba(1, 167, 125, 0.3);"><div class="ait-w-full"><div class="ait-gap-4 ait-text-neutral-900 dark:ait-text-white ait-text-sm dark:ait-text-neutral-400"><div class="ait-ai-icon ait-flex ait-gap-2 ait-text-[12px]"><span><svg class="ait-w-[16px] ait-h-[16px] ait-GPT-4o Mini" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none"> <rect width="16" height="16" rx="8" fill="#0BD6FF"></rect> <path d="M13.1348 6.91143C13.4071 6.0943 13.3133 5.19918 12.878 4.45593C12.2232 3.31593 10.907 2.72943 9.62146 3.00543C9.04958 2.36118 8.22796 1.99481 7.36658 2.00006C6.05258 1.99706 4.88671 2.84306 4.48246 4.09331C3.63833 4.26618 2.90971 4.79456 2.48333 5.54343C1.82371 6.68043 1.97408 8.11368 2.85533 9.08868C2.58308 9.9058 2.67683 10.8009 3.11221 11.5442C3.76696 12.6842 5.08321 13.2707 6.36871 12.9947C6.94021 13.6389 7.76221 14.0053 8.62358 13.9997C9.93833 14.0031 11.1046 13.1563 11.5088 11.9049C12.353 11.7321 13.0816 11.2037 13.508 10.4548C14.1668 9.3178 14.0157 7.88643 13.1348 6.91143ZM8.62433 13.2156C8.09821 13.2163 7.58858 13.0322 7.18471 12.6951C7.20308 12.6853 7.23496 12.6677 7.25558 12.6549L9.64508 11.2749C9.76733 11.2056 9.84233 11.0754 9.84158 10.9348V7.56618L10.8515 8.1493C10.8623 8.15455 10.8695 8.16505 10.871 8.17705V10.9667C10.8695 12.2072 9.86483 13.2129 8.62433 13.2156ZM3.79283 11.1519C3.52921 10.6967 3.43433 10.1631 3.52471 9.64518C3.54233 9.65568 3.57346 9.6748 3.59558 9.68755L5.98508 11.0676C6.10621 11.1384 6.25621 11.1384 6.37771 11.0676L9.29483 9.38305V10.5493C9.29558 10.5613 9.28996 10.5729 9.28058 10.5804L6.86521 11.9751C5.78933 12.5946 4.41496 12.2263 3.79283 11.1519ZM3.16396 5.93605C3.42646 5.48006 3.84083 5.1313 4.33433 4.95018C4.33433 4.97081 4.33321 5.00718 4.33321 5.03268V7.79305C4.33246 7.9333 4.40746 8.06343 4.52933 8.1328L7.44646 9.81693L6.43658 10.4001C6.42646 10.4068 6.41371 10.4079 6.40246 10.4031L3.98671 9.0073C2.91308 8.38555 2.54521 7.01155 3.16396 5.93605ZM11.4612 7.86693L8.54408 6.18243L9.55396 5.59968C9.56408 5.59293 9.57683 5.59181 9.58808 5.59668L12.0038 6.9913C13.0793 7.61268 13.448 8.98855 12.8266 10.0641C12.5637 10.5193 12.1497 10.8681 11.6566 11.0496V8.20668C11.6577 8.06643 11.5827 7.93668 11.4612 7.86693ZM12.4662 6.35418C12.4486 6.34331 12.4175 6.32455 12.3953 6.3118L10.0058 4.93181C9.88471 4.86093 9.73471 4.86093 9.61321 4.93181L6.69608 6.6163V5.45005C6.69533 5.43805 6.70096 5.42643 6.71033 5.41893L9.12571 4.02543C10.2016 3.40481 11.5771 3.77418 12.1973 4.85043C12.4595 5.30493 12.5551 5.83706 12.4662 6.35418ZM6.14708 8.4328L5.13683 7.84968C5.12596 7.84443 5.11883 7.83393 5.11733 7.82193V5.03231C5.11808 3.79031 6.12571 2.78381 7.36771 2.78456C7.89308 2.78456 8.40158 2.96906 8.80546 3.30506C8.78708 3.31481 8.75558 3.33243 8.73458 3.34518L6.34508 4.72518C6.22283 4.79456 6.14783 4.92431 6.14858 5.06493L6.14708 8.4328ZM6.69571 7.25005L7.99508 6.49968L9.29446 7.24968V8.75005L7.99508 9.50005L6.69571 8.75005V7.25005Z" fill="white"></path> </svg></span> GPT-4o Mini</div></div></div></div><div data-v-2db9de5a="" class="ait-absolute ait-z-[9999999]  ait-popper ait-popper-y ait-py-1 ait-px-2 ait-rounded-md ait-shadow-lg dark:ait-bg-neutral-100 ait-bg-neutral-800 ait-text-gray-200 dark:ait-text-gray-900 ait:border-[1px] dark:ait-border-neutral-200 ait-border-neutral-300" style="display: none;"><div data-v-2db9de5a="" class="ait-text-xs ait-font-medium ait-text-gray-200 dark:ait-text-gray-800 ait-whitespace-nowrap ait-max-w-[170px] ait-w-full"><div class="ait-text-xs"><div class="ait-text-xs ait-break-words ait-whitespace-pre-wrap ait-text-gray-200 dark:ait-text-gray-800">Very fast, great for most everyday tasks.</div><div class="ait-text-[10px] ait-opacity-60 ait-mt-1 ait-text-gray-200 dark:ait-text-gray-800">Cost 1 Fast query.</div></div></div></div><div data-v-2db9de5a="" class="ait-hide ait-left-0 ait-top-0 ait-bottom-0 ait-left-0 ait-top-1/2 ait-left-1/2 ait-right-1/2 ait-bottom-1/2 ait-translate-x-[-100%] ait-translate-y-[-100%] ait-translate-x-[-120%] ait-translate-y-[-120%]"></div></li><li data-v-2db9de5a="" class="ait-relative ait-block ait-popper-container"><div class="ait-flex ait-px-4 ait-py-2.5 hover:ait-bg-neutral-100 dark:hover:ait-bg-neutral-700 hover:ait-rounded-lg ait-cursor-pointer" style=""><div class="ait-w-full"><div class="ait-gap-4 ait-text-neutral-900 dark:ait-text-white ait-text-sm dark:ait-text-neutral-400"><div class="ait-ai-icon ait-flex ait-gap-2 ait-text-[12px]"><span><svg class="ait-w-[16px] ait-h-[16px] ait-GPT-4o" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none"> <rect width="16" height="16" rx="8" fill="#AB68E1"></rect> <path d="M13.1348 6.91143C13.4071 6.0943 13.3133 5.19918 12.878 4.45593C12.2232 3.31593 10.907 2.72943 9.62146 3.00543C9.04958 2.36118 8.22796 1.99481 7.36658 2.00006C6.05258 1.99706 4.88671 2.84306 4.48246 4.09331C3.63833 4.26618 2.90971 4.79456 2.48333 5.54343C1.82371 6.68043 1.97408 8.11368 2.85533 9.08868C2.58308 9.9058 2.67683 10.8009 3.11221 11.5442C3.76696 12.6842 5.08321 13.2707 6.36871 12.9947C6.94021 13.6389 7.76221 14.0053 8.62358 13.9997C9.93833 14.0031 11.1046 13.1563 11.5088 11.9049C12.353 11.7321 13.0816 11.2037 13.508 10.4548C14.1668 9.3178 14.0157 7.88643 13.1348 6.91143ZM8.62433 13.2156C8.09821 13.2163 7.58858 13.0322 7.18471 12.6951C7.20308 12.6853 7.23496 12.6677 7.25558 12.6549L9.64508 11.2749C9.76733 11.2056 9.84233 11.0754 9.84158 10.9348V7.56618L10.8515 8.1493C10.8623 8.15455 10.8695 8.16505 10.871 8.17705V10.9667C10.8695 12.2072 9.86483 13.2129 8.62433 13.2156ZM3.79283 11.1519C3.52921 10.6967 3.43433 10.1631 3.52471 9.64518C3.54233 9.65568 3.57346 9.6748 3.59558 9.68755L5.98508 11.0676C6.10621 11.1384 6.25621 11.1384 6.37771 11.0676L9.29483 9.38305V10.5493C9.29558 10.5613 9.28996 10.5729 9.28058 10.5804L6.86521 11.9751C5.78933 12.5946 4.41496 12.2263 3.79283 11.1519ZM3.16396 5.93605C3.42646 5.48006 3.84083 5.1313 4.33433 4.95018C4.33433 4.97081 4.33321 5.00718 4.33321 5.03268V7.79305C4.33246 7.9333 4.40746 8.06343 4.52933 8.1328L7.44646 9.81693L6.43658 10.4001C6.42646 10.4068 6.41371 10.4079 6.40246 10.4031L3.98671 9.0073C2.91308 8.38555 2.54521 7.01155 3.16396 5.93605ZM11.4612 7.86693L8.54408 6.18243L9.55396 5.59968C9.56408 5.59293 9.57683 5.59181 9.58808 5.59668L12.0038 6.9913C13.0793 7.61268 13.448 8.98855 12.8266 10.0641C12.5637 10.5193 12.1497 10.8681 11.6566 11.0496V8.20668C11.6577 8.06643 11.5827 7.93668 11.4612 7.86693ZM12.4662 6.35418C12.4486 6.34331 12.4175 6.32455 12.3953 6.3118L10.0058 4.93181C9.88471 4.86093 9.73471 4.86093 9.61321 4.93181L6.69608 6.6163V5.45005C6.69533 5.43805 6.70096 5.42643 6.71033 5.41893L9.12571 4.02543C10.2016 3.40481 11.5771 3.77418 12.1973 4.85043C12.4595 5.30493 12.5551 5.83706 12.4662 6.35418ZM6.14708 8.4328L5.13683 7.84968C5.12596 7.84443 5.11883 7.83393 5.11733 7.82193V5.03231C5.11808 3.79031 6.12571 2.78381 7.36771 2.78456C7.89308 2.78456 8.40158 2.96906 8.80546 3.30506C8.78708 3.31481 8.75558 3.33243 8.73458 3.34518L6.34508 4.72518C6.22283 4.79456 6.14783 4.92431 6.14858 5.06493L6.14708 8.4328ZM6.69571 7.25005L7.99508 6.49968L9.29446 7.24968V8.75005L7.99508 9.50005L6.69571 8.75005V7.25005Z" fill="white"></path> </svg></span> GPT-4o</div></div></div></div><div data-v-2db9de5a="" class="ait-absolute ait-z-[9999999]  ait-popper ait-popper-y ait-py-1 ait-px-2 ait-rounded-md ait-shadow-lg dark:ait-bg-neutral-100 ait-bg-neutral-800 ait-text-gray-200 dark:ait-text-gray-900 ait:border-[1px] dark:ait-border-neutral-200 ait-border-neutral-300" style="display: none;"><div data-v-2db9de5a="" class="ait-text-xs ait-font-medium ait-text-gray-200 dark:ait-text-gray-800 ait-whitespace-nowrap ait-max-w-[170px] ait-w-full"><div class="ait-text-xs"><div class="ait-text-xs ait-break-words ait-whitespace-pre-wrap ait-text-gray-200 dark:ait-text-gray-800">OpenAI’s newest and most advanced flagship model which can reason across audio, vision, and text in real time.</div><div class="ait-text-[10px] ait-opacity-60 ait-mt-1 ait-text-gray-200 dark:ait-text-gray-800">Cost 1 Advanced query.</div></div></div></div><div data-v-2db9de5a="" class="ait-hide ait-left-0 ait-top-0 ait-bottom-0 ait-left-0 ait-top-1/2 ait-left-1/2 ait-right-1/2 ait-bottom-1/2 ait-translate-x-[-100%] ait-translate-y-[-100%] ait-translate-x-[-120%] ait-translate-y-[-120%]"></div></li><li data-v-2db9de5a="" class="ait-relative ait-block ait-popper-container"><div class="ait-flex ait-px-4 ait-py-2.5 hover:ait-bg-neutral-100 dark:hover:ait-bg-neutral-700 hover:ait-rounded-lg ait-cursor-pointer" style=""><div class="ait-w-full"><div class="ait-gap-4 ait-text-neutral-900 dark:ait-text-white ait-text-sm dark:ait-text-neutral-400"><div class="ait-ai-icon ait-flex ait-gap-2 ait-text-[12px]"><span><svg class="ait-w-[16px] ait-h-[16px] ait-o1 Mini" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none"> <path d="M0 8C0 3.58172 3.58172 0 8 0C12.4183 0 16 3.58172 16 8C16 12.4183 12.4183 16 8 16C3.58172 16 0 12.4183 0 8Z" fill="#AFDE75"></path> <g filter="url(#o1mini_filter)"> <path d="M13.1348 6.91143C13.4071 6.0943 13.3133 5.19918 12.878 4.45593C12.2232 3.31593 10.907 2.72943 9.62146 3.00543C9.04958 2.36118 8.22796 1.99481 7.36658 2.00006C6.05258 1.99706 4.88671 2.84306 4.48246 4.09331C3.63833 4.26618 2.90971 4.79456 2.48333 5.54343C1.82371 6.68043 1.97408 8.11368 2.85533 9.08868C2.58308 9.9058 2.67683 10.8009 3.11221 11.5442C3.76696 12.6842 5.08321 13.2707 6.36871 12.9947C6.94021 13.6389 7.76221 14.0053 8.62358 13.9997C9.93833 14.0031 11.1046 13.1563 11.5088 11.9049C12.353 11.7321 13.0816 11.2037 13.508 10.4548C14.1668 9.3178 14.0157 7.88643 13.1348 6.91143ZM8.62433 13.2156C8.09821 13.2163 7.58858 13.0322 7.18471 12.6951C7.20308 12.6853 7.23496 12.6677 7.25558 12.6549L9.64508 11.2749C9.76733 11.2056 9.84233 11.0754 9.84158 10.9348V7.56618L10.8515 8.1493C10.8623 8.15455 10.8695 8.16505 10.871 8.17705V10.9667C10.8695 12.2072 9.86483 13.2129 8.62433 13.2156ZM3.79283 11.1519C3.52921 10.6967 3.43433 10.1631 3.52471 9.64518C3.54233 9.65568 3.57346 9.6748 3.59558 9.68755L5.98508 11.0676C6.10621 11.1384 6.25621 11.1384 6.37771 11.0676L9.29483 9.38305V10.5493C9.29558 10.5613 9.28996 10.5729 9.28058 10.5804L6.86521 11.9751C5.78933 12.5946 4.41496 12.2263 3.79283 11.1519ZM3.16396 5.93605C3.42646 5.48006 3.84083 5.1313 4.33433 4.95018C4.33433 4.97081 4.33321 5.00718 4.33321 5.03268V7.79305C4.33246 7.9333 4.40746 8.06343 4.52933 8.1328L7.44646 9.81693L6.43658 10.4001C6.42646 10.4068 6.41371 10.4079 6.40246 10.4031L3.98671 9.0073C2.91308 8.38555 2.54521 7.01155 3.16396 5.93605ZM11.4612 7.86693L8.54408 6.18243L9.55396 5.59968C9.56408 5.59293 9.57683 5.59181 9.58808 5.59668L12.0038 6.9913C13.0793 7.61268 13.448 8.98855 12.8266 10.0641C12.5637 10.5193 12.1497 10.8681 11.6566 11.0496V8.20668C11.6577 8.06643 11.5827 7.93668 11.4612 7.86693ZM12.4662 6.35418C12.4486 6.34331 12.4175 6.32455 12.3953 6.3118L10.0058 4.93181C9.88471 4.86093 9.73471 4.86093 9.61321 4.93181L6.69608 6.6163V5.45005C6.69533 5.43805 6.70096 5.42643 6.71033 5.41893L9.12571 4.02543C10.2016 3.40481 11.5771 3.77418 12.1973 4.85043C12.4595 5.30493 12.5551 5.83706 12.4662 6.35418ZM6.14708 8.4328L5.13683 7.84968C5.12596 7.84443 5.11883 7.83393 5.11733 7.82193V5.03231C5.11808 3.79031 6.12571 2.78381 7.36771 2.78456C7.89308 2.78456 8.40158 2.96906 8.80546 3.30506C8.78708 3.31481 8.75558 3.33243 8.73458 3.34518L6.34508 4.72518C6.22283 4.79456 6.14783 4.92431 6.14858 5.06493L6.14708 8.4328ZM6.69571 7.25005L7.99508 6.49968L9.29446 7.24968V8.75005L7.99508 9.50005L6.69571 8.75005V7.25005Z" fill="white"></path> </g> <defs> <filter id="o1mini_filter" x="0.0749512" y="0" width="15.8406" height="15.9998" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"> <feflood flood-opacity="0" result="BackgroundImageFix"></feflood> <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></fecolormatrix> <feoffset></feoffset> <fegaussianblur stdDeviation="1"></fegaussianblur> <fecomposite in2="hardAlpha" operator="out"></fecomposite> <fecolormatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"></fecolormatrix> <feblend mode="normal" in2="BackgroundImageFix" result="o1mini_dropshadow"></feblend> <feblend mode="normal" in="SourceGraphic" in2="o1mini_dropshadow" result="shape"></feblend> </filter> </defs> </svg></span> o1 Mini</div></div></div></div><div data-v-2db9de5a="" class="ait-absolute ait-z-[9999999]  ait-popper ait-popper-y ait-py-1 ait-px-2 ait-rounded-md ait-shadow-lg dark:ait-bg-neutral-100 ait-bg-neutral-800 ait-text-gray-200 dark:ait-text-gray-900 ait:border-[1px] dark:ait-border-neutral-200 ait-border-neutral-300" style="display: none;"><div data-v-2db9de5a="" class="ait-text-xs ait-font-medium ait-text-gray-200 dark:ait-text-gray-800 ait-whitespace-nowrap ait-max-w-[170px] ait-w-full"><div class="ait-text-xs"><div class="ait-text-xs ait-break-words ait-whitespace-pre-wrap ait-text-gray-200 dark:ait-text-gray-800">OpenAI's basic reasoning model for STEM reasoning, especially math and coding. Best for tasks needing strong logic without broad knowledge.</div><div class="ait-text-[10px] ait-opacity-60 ait-mt-1 ait-text-gray-200 dark:ait-text-gray-800">Cost 5 Advanced query</div></div></div></div><div data-v-2db9de5a="" class="ait-hide ait-left-0 ait-top-0 ait-bottom-0 ait-left-0 ait-top-1/2 ait-left-1/2 ait-right-1/2 ait-bottom-1/2 ait-translate-x-[-100%] ait-translate-y-[-100%] ait-translate-x-[-120%] ait-translate-y-[-120%]"></div></li><li data-v-2db9de5a="" class="ait-relative ait-block ait-popper-container"><div class="ait-flex ait-px-4 ait-py-2.5 hover:ait-bg-neutral-100 dark:hover:ait-bg-neutral-700 hover:ait-rounded-lg ait-cursor-pointer" style=""><div class="ait-w-full"><div class="ait-gap-4 ait-text-neutral-900 dark:ait-text-white ait-text-sm dark:ait-text-neutral-400"><div class="ait-ai-icon ait-flex ait-gap-2 ait-text-[12px]"><span><svg class="ait-w-[16px] ait-h-[16px] ait-o1 Preview" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none"> <path d="M0 8C0 3.58172 3.58172 0 8 0C12.4183 0 16 3.58172 16 8C16 12.4183 12.4183 16 8 16C3.58172 16 0 12.4183 0 8Z" fill="url(#o1_linear)"></path> <g filter="url(#o1_filter)"> <path d="M13.1348 6.91143C13.4071 6.0943 13.3133 5.19918 12.878 4.45593C12.2232 3.31593 10.907 2.72943 9.62146 3.00543C9.04958 2.36118 8.22796 1.99481 7.36658 2.00006C6.05258 1.99706 4.88671 2.84306 4.48246 4.09331C3.63833 4.26618 2.90971 4.79456 2.48333 5.54343C1.82371 6.68043 1.97408 8.11368 2.85533 9.08868C2.58308 9.9058 2.67683 10.8009 3.11221 11.5442C3.76696 12.6842 5.08321 13.2707 6.36871 12.9947C6.94021 13.6389 7.76221 14.0053 8.62358 13.9997C9.93833 14.0031 11.1046 13.1563 11.5088 11.9049C12.353 11.7321 13.0816 11.2037 13.508 10.4548C14.1668 9.3178 14.0157 7.88643 13.1348 6.91143ZM8.62433 13.2156C8.09821 13.2163 7.58858 13.0322 7.18471 12.6951C7.20308 12.6853 7.23496 12.6677 7.25558 12.6549L9.64508 11.2749C9.76733 11.2056 9.84233 11.0754 9.84158 10.9348V7.56618L10.8515 8.1493C10.8623 8.15455 10.8695 8.16505 10.871 8.17705V10.9667C10.8695 12.2072 9.86483 13.2129 8.62433 13.2156ZM3.79283 11.1519C3.52921 10.6967 3.43433 10.1631 3.52471 9.64518C3.54233 9.65568 3.57346 9.6748 3.59558 9.68755L5.98508 11.0676C6.10621 11.1384 6.25621 11.1384 6.37771 11.0676L9.29483 9.38305V10.5493C9.29558 10.5613 9.28996 10.5729 9.28058 10.5804L6.86521 11.9751C5.78933 12.5946 4.41496 12.2263 3.79283 11.1519ZM3.16396 5.93605C3.42646 5.48006 3.84083 5.1313 4.33433 4.95018C4.33433 4.97081 4.33321 5.00718 4.33321 5.03268V7.79305C4.33246 7.9333 4.40746 8.06343 4.52933 8.1328L7.44646 9.81693L6.43658 10.4001C6.42646 10.4068 6.41371 10.4079 6.40246 10.4031L3.98671 9.0073C2.91308 8.38555 2.54521 7.01155 3.16396 5.93605ZM11.4612 7.86693L8.54408 6.18243L9.55396 5.59968C9.56408 5.59293 9.57683 5.59181 9.58808 5.59668L12.0038 6.9913C13.0793 7.61268 13.448 8.98855 12.8266 10.0641C12.5637 10.5193 12.1497 10.8681 11.6566 11.0496V8.20668C11.6577 8.06643 11.5827 7.93668 11.4612 7.86693ZM12.4662 6.35418C12.4486 6.34331 12.4175 6.32455 12.3953 6.3118L10.0058 4.93181C9.88471 4.86093 9.73471 4.86093 9.61321 4.93181L6.69608 6.6163V5.45005C6.69533 5.43805 6.70096 5.42643 6.71033 5.41893L9.12571 4.02543C10.2016 3.40481 11.5771 3.77418 12.1973 4.85043C12.4595 5.30493 12.5551 5.83706 12.4662 6.35418ZM6.14708 8.4328L5.13683 7.84968C5.12596 7.84443 5.11883 7.83393 5.11733 7.82193V5.03231C5.11808 3.79031 6.12571 2.78381 7.36771 2.78456C7.89308 2.78456 8.40158 2.96906 8.80546 3.30506C8.78708 3.31481 8.75558 3.33243 8.73458 3.34518L6.34508 4.72518C6.22283 4.79456 6.14783 4.92431 6.14858 5.06493L6.14708 8.4328ZM6.69571 7.25005L7.99508 6.49968L9.29446 7.24968V8.75005L7.99508 9.50005L6.69571 8.75005V7.25005Z" fill="white"></path> </g> <defs> <filter id="o1_filter" x="0.0749512" y="0" width="15.8406" height="15.9998" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"> <feflood flood-opacity="0" result="BackgroundImageFix"></feflood> <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></fecolormatrix> <feoffset></feoffset> <fegaussianblur stdDeviation="1"></fegaussianblur> <fecomposite in2="hardAlpha" operator="out"></fecomposite> <fecolormatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0"></fecolormatrix> <feblend mode="normal" in2="BackgroundImageFix" result="o1_dropshadow"></feblend> <feblend mode="normal" in="SourceGraphic" in2="o1_dropshadow" result="shape"></feblend> </filter> <lineargradient id="o1_linear" x1="2" y1="2" x2="14" y2="14" gradientUnits="userSpaceOnUse"> <stop offset="0.2" stop-color="#F9D914"></stop> <stop offset="0.6" stop-color="#D8E0A2"></stop> <stop offset="1" stop-color="#56A9CB"></stop> </lineargradient> </defs> </svg></span> o1 Preview</div></div></div></div><div data-v-2db9de5a="" class="ait-absolute ait-z-[9999999]  ait-popper ait-popper-y ait-py-1 ait-px-2 ait-rounded-md ait-shadow-lg dark:ait-bg-neutral-100 ait-bg-neutral-800 ait-text-gray-200 dark:ait-text-gray-900 ait:border-[1px] dark:ait-border-neutral-200 ait-border-neutral-300" style="display: none;"><div data-v-2db9de5a="" class="ait-text-xs ait-font-medium ait-text-gray-200 dark:ait-text-gray-800 ait-whitespace-nowrap ait-max-w-[170px] ait-w-full"><div class="ait-text-xs"><div class="ait-text-xs ait-break-words ait-whitespace-pre-wrap ait-text-gray-200 dark:ait-text-gray-800">OpenAI's advanced reasoning model for complex tasks in science, math, and coding. Takes more time to think, delivering well-considered responses to challenging problems.</div><div class="ait-text-[10px] ait-opacity-60 ait-mt-1 ait-text-gray-200 dark:ait-text-gray-800">Cost 15 Advanced query</div></div></div></div><div data-v-2db9de5a="" class="ait-hide ait-left-0 ait-top-0 ait-bottom-0 ait-left-0 ait-top-1/2 ait-left-1/2 ait-right-1/2 ait-bottom-1/2 ait-translate-x-[-100%] ait-translate-y-[-100%] ait-translate-x-[-120%] ait-translate-y-[-120%]"></div></li><li data-v-2db9de5a="" class="ait-relative ait-block ait-popper-container"><div class="ait-flex ait-px-4 ait-py-2.5 hover:ait-bg-neutral-100 dark:hover:ait-bg-neutral-700 hover:ait-rounded-lg ait-cursor-pointer" style=""><div class="ait-w-full"><div class="ait-gap-4 ait-text-neutral-900 dark:ait-text-white ait-text-sm dark:ait-text-neutral-400"><div class="ait-ai-icon ait-flex ait-gap-2 ait-text-[12px]"><span><svg class="ait-w-[16px] ait-h-[16px] ait-Claude 3 Haiku" width="16" height="16" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none"><rect width="100%" height="100%" rx="8" fill="#F0E8D6"></rect><path d="M4.67668 9.70835L6.71408 8.5651L6.74817 8.46546L6.71408 8.41039H6.61444L6.27356 8.38941L5.10934 8.35795L4.09981 8.31599L3.12176 8.26355L2.87528 8.21111L2.64453 7.90694L2.66813 7.75486L2.87528 7.61588L3.17158 7.64211L3.82711 7.68668L4.81041 7.75486L5.52363 7.79681L6.58035 7.90694H6.74817L6.77177 7.83877L6.71408 7.79681L6.6695 7.75486L5.65212 7.06524L4.55082 6.33628L3.97395 5.91674L3.66192 5.70435L3.50459 5.50507L3.43642 5.06979L3.71961 4.75776L4.09981 4.78398L4.19683 4.8102L4.58229 5.1065L5.40564 5.74368L6.48071 6.53557L6.63804 6.66667L6.70097 6.6221L6.70884 6.59063L6.63804 6.47264L6.0533 5.41592L5.42924 4.34084L5.15129 3.89508L5.07787 3.62762C5.05165 3.51749 5.03329 3.42572 5.03329 3.31297L5.35582 2.87507L5.53412 2.81738L5.96415 2.87507L6.14508 3.0324L6.41253 3.64335L6.84519 4.60568L7.51645 5.91412L7.71311 6.3022L7.818 6.66143L7.85733 6.77156H7.92551V6.70863L7.98057 5.97181L8.08283 5.06717L8.18247 3.90295L8.21656 3.57518L8.37913 3.18186L8.70166 2.96947L8.95338 3.09008L9.16053 3.38639L9.13169 3.5778L9.00845 4.37755L8.76721 5.63093L8.60988 6.47001H8.70166L8.80654 6.36513L9.23133 5.80137L9.94455 4.90984L10.2592 4.55586L10.6263 4.16516L10.8623 3.97899H11.3081L11.6358 4.4667L11.489 4.97015L11.0301 5.55227L10.6499 6.04523L10.1045 6.77942L9.76362 7.36678L9.79509 7.41398L9.87637 7.40611L11.1088 7.1439L11.7748 7.02328L12.5693 6.88693L12.9285 7.05475L12.9679 7.22519L12.8263 7.57393L11.9767 7.7837L10.9803 7.98298L9.49616 8.33435L9.47781 8.34746L9.49878 8.37368L10.1674 8.43661L10.4532 8.45234H11.1533L12.4565 8.54936L12.7974 8.77487L13.002 9.05019L12.9679 9.25996L12.4434 9.52742L11.7355 9.3596L10.0835 8.96628L9.51714 8.82469H9.43848V8.87189L9.91046 9.33338L10.7758 10.1148L11.8587 11.1217L11.9138 11.3708L11.7748 11.5674L11.628 11.5465L10.6761 10.8306L10.309 10.5081L9.47781 9.80799H9.42274V9.88141L9.61416 10.162L10.6263 11.6828L10.6787 12.1496L10.6053 12.3016L10.3431 12.3934L10.0547 12.341L9.46207 11.5097L8.85112 10.5736L8.35816 9.73457L8.29785 9.76865L8.00679 12.9021L7.87044 13.0621L7.55578 13.1827L7.29357 12.9834L7.1546 12.6609L7.29357 12.0237L7.46139 11.1925L7.59774 10.5317L7.72098 9.71097L7.7944 9.43827L7.78915 9.41991L7.72885 9.42778L7.11002 10.2773L6.16868 11.5491L5.42399 12.3462L5.24569 12.417L4.93627 12.2571L4.96512 11.9712L5.13818 11.7169L6.16868 10.4058L6.79012 9.59297L7.19131 9.12361L7.18869 9.05544H7.16509L4.42758 10.8332L3.93986 10.8962L3.73009 10.6995L3.75632 10.377L3.85596 10.2721L4.67931 9.70572L4.67668 9.70835Z" fill="#D97757"></path></svg></span> Claude 3 Haiku</div></div></div></div><div data-v-2db9de5a="" class="ait-absolute ait-z-[9999999]  ait-popper ait-popper-y ait-py-1 ait-px-2 ait-rounded-md ait-shadow-lg dark:ait-bg-neutral-100 ait-bg-neutral-800 ait-text-gray-200 dark:ait-text-gray-900 ait:border-[1px] dark:ait-border-neutral-200 ait-border-neutral-300" style="display: none;"><div data-v-2db9de5a="" class="ait-text-xs ait-font-medium ait-text-gray-200 dark:ait-text-gray-800 ait-whitespace-nowrap ait-max-w-[170px] ait-w-full"><div class="ait-text-xs"><div class="ait-text-xs ait-break-words ait-whitespace-pre-wrap ait-text-gray-200 dark:ait-text-gray-800">Anthropic's most compact model, designed for near-instant responsiveness and seamless AI experiences that mimic human interactions</div><div class="ait-text-[10px] ait-opacity-60 ait-mt-1 ait-text-gray-200 dark:ait-text-gray-800">Cost 1 Fast query.</div></div></div></div><div data-v-2db9de5a="" class="ait-hide ait-left-0 ait-top-0 ait-bottom-0 ait-left-0 ait-top-1/2 ait-left-1/2 ait-right-1/2 ait-bottom-1/2 ait-translate-x-[-100%] ait-translate-y-[-100%] ait-translate-x-[-120%] ait-translate-y-[-120%]"></div></li><li data-v-2db9de5a="" class="ait-relative ait-block ait-popper-container"><div class="ait-flex ait-px-4 ait-py-2.5 hover:ait-bg-neutral-100 dark:hover:ait-bg-neutral-700 hover:ait-rounded-lg ait-cursor-pointer" style=""><div class="ait-w-full"><div class="ait-gap-4 ait-text-neutral-900 dark:ait-text-white ait-text-sm dark:ait-text-neutral-400"><div class="ait-ai-icon ait-flex ait-gap-2 ait-text-[12px]"><span><svg class="ait-w-[16px] ait-h-[16px] ait-Claude 3 Sonnet" width="16" height="16" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none"><rect width="100%" height="100%" rx="8" fill="#F0E8D6"></rect><path d="M4.67668 9.70835L6.71408 8.5651L6.74817 8.46546L6.71408 8.41039H6.61444L6.27356 8.38941L5.10934 8.35795L4.09981 8.31599L3.12176 8.26355L2.87528 8.21111L2.64453 7.90694L2.66813 7.75486L2.87528 7.61588L3.17158 7.64211L3.82711 7.68668L4.81041 7.75486L5.52363 7.79681L6.58035 7.90694H6.74817L6.77177 7.83877L6.71408 7.79681L6.6695 7.75486L5.65212 7.06524L4.55082 6.33628L3.97395 5.91674L3.66192 5.70435L3.50459 5.50507L3.43642 5.06979L3.71961 4.75776L4.09981 4.78398L4.19683 4.8102L4.58229 5.1065L5.40564 5.74368L6.48071 6.53557L6.63804 6.66667L6.70097 6.6221L6.70884 6.59063L6.63804 6.47264L6.0533 5.41592L5.42924 4.34084L5.15129 3.89508L5.07787 3.62762C5.05165 3.51749 5.03329 3.42572 5.03329 3.31297L5.35582 2.87507L5.53412 2.81738L5.96415 2.87507L6.14508 3.0324L6.41253 3.64335L6.84519 4.60568L7.51645 5.91412L7.71311 6.3022L7.818 6.66143L7.85733 6.77156H7.92551V6.70863L7.98057 5.97181L8.08283 5.06717L8.18247 3.90295L8.21656 3.57518L8.37913 3.18186L8.70166 2.96947L8.95338 3.09008L9.16053 3.38639L9.13169 3.5778L9.00845 4.37755L8.76721 5.63093L8.60988 6.47001H8.70166L8.80654 6.36513L9.23133 5.80137L9.94455 4.90984L10.2592 4.55586L10.6263 4.16516L10.8623 3.97899H11.3081L11.6358 4.4667L11.489 4.97015L11.0301 5.55227L10.6499 6.04523L10.1045 6.77942L9.76362 7.36678L9.79509 7.41398L9.87637 7.40611L11.1088 7.1439L11.7748 7.02328L12.5693 6.88693L12.9285 7.05475L12.9679 7.22519L12.8263 7.57393L11.9767 7.7837L10.9803 7.98298L9.49616 8.33435L9.47781 8.34746L9.49878 8.37368L10.1674 8.43661L10.4532 8.45234H11.1533L12.4565 8.54936L12.7974 8.77487L13.002 9.05019L12.9679 9.25996L12.4434 9.52742L11.7355 9.3596L10.0835 8.96628L9.51714 8.82469H9.43848V8.87189L9.91046 9.33338L10.7758 10.1148L11.8587 11.1217L11.9138 11.3708L11.7748 11.5674L11.628 11.5465L10.6761 10.8306L10.309 10.5081L9.47781 9.80799H9.42274V9.88141L9.61416 10.162L10.6263 11.6828L10.6787 12.1496L10.6053 12.3016L10.3431 12.3934L10.0547 12.341L9.46207 11.5097L8.85112 10.5736L8.35816 9.73457L8.29785 9.76865L8.00679 12.9021L7.87044 13.0621L7.55578 13.1827L7.29357 12.9834L7.1546 12.6609L7.29357 12.0237L7.46139 11.1925L7.59774 10.5317L7.72098 9.71097L7.7944 9.43827L7.78915 9.41991L7.72885 9.42778L7.11002 10.2773L6.16868 11.5491L5.42399 12.3462L5.24569 12.417L4.93627 12.2571L4.96512 11.9712L5.13818 11.7169L6.16868 10.4058L6.79012 9.59297L7.19131 9.12361L7.18869 9.05544H7.16509L4.42758 10.8332L3.93986 10.8962L3.73009 10.6995L3.75632 10.377L3.85596 10.2721L4.67931 9.70572L4.67668 9.70835Z" fill="black" fill-opacity="0.92"></path></svg></span> Claude 3 Sonnet</div></div></div></div><div data-v-2db9de5a="" class="ait-absolute ait-z-[9999999]  ait-popper ait-popper-y ait-py-1 ait-px-2 ait-rounded-md ait-shadow-lg dark:ait-bg-neutral-100 ait-bg-neutral-800 ait-text-gray-200 dark:ait-text-gray-900 ait:border-[1px] dark:ait-border-neutral-200 ait-border-neutral-300" style="display: none;"><div data-v-2db9de5a="" class="ait-text-xs ait-font-medium ait-text-gray-200 dark:ait-text-gray-800 ait-whitespace-nowrap ait-max-w-[170px] ait-w-full"><div class="ait-text-xs"><div class="ait-text-xs ait-break-words ait-whitespace-pre-wrap ait-text-gray-200 dark:ait-text-gray-800">Very fast, great for most everyday tasks.</div><div class="ait-text-[10px] ait-opacity-60 ait-mt-1 ait-text-gray-200 dark:ait-text-gray-800">Cost 1 Advanced query.</div></div></div></div><div data-v-2db9de5a="" class="ait-hide ait-left-0 ait-top-0 ait-bottom-0 ait-left-0 ait-top-1/2 ait-left-1/2 ait-right-1/2 ait-bottom-1/2 ait-translate-x-[-100%] ait-translate-y-[-100%] ait-translate-x-[-120%] ait-translate-y-[-120%]"></div></li><li data-v-2db9de5a="" class="ait-relative ait-block ait-popper-container"><div class="ait-flex ait-px-4 ait-py-2.5 hover:ait-bg-neutral-100 dark:hover:ait-bg-neutral-700 hover:ait-rounded-lg ait-cursor-pointer" style=""><div class="ait-w-full"><div class="ait-gap-4 ait-text-neutral-900 dark:ait-text-white ait-text-sm dark:ait-text-neutral-400"><div class="ait-ai-icon ait-flex ait-gap-2 ait-text-[12px]"><span><svg class="ait-w-[16px] ait-h-[16px] ait-Claude 3 Opus" width="16" height="16" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none"><rect width="100%" height="100%" rx="8" fill="#C36E51"></rect><path d="M4.67668 9.70835L6.71408 8.5651L6.74817 8.46546L6.71408 8.41039H6.61444L6.27356 8.38941L5.10934 8.35795L4.09981 8.31599L3.12176 8.26355L2.87528 8.21111L2.64453 7.90694L2.66813 7.75486L2.87528 7.61588L3.17158 7.64211L3.82711 7.68668L4.81041 7.75486L5.52363 7.79681L6.58035 7.90694H6.74817L6.77177 7.83877L6.71408 7.79681L6.6695 7.75486L5.65212 7.06524L4.55082 6.33628L3.97395 5.91674L3.66192 5.70435L3.50459 5.50507L3.43642 5.06979L3.71961 4.75776L4.09981 4.78398L4.19683 4.8102L4.58229 5.1065L5.40564 5.74368L6.48071 6.53557L6.63804 6.66667L6.70097 6.6221L6.70884 6.59063L6.63804 6.47264L6.0533 5.41592L5.42924 4.34084L5.15129 3.89508L5.07787 3.62762C5.05165 3.51749 5.03329 3.42572 5.03329 3.31297L5.35582 2.87507L5.53412 2.81738L5.96415 2.87507L6.14508 3.0324L6.41253 3.64335L6.84519 4.60568L7.51645 5.91412L7.71311 6.3022L7.818 6.66143L7.85733 6.77156H7.92551V6.70863L7.98057 5.97181L8.08283 5.06717L8.18247 3.90295L8.21656 3.57518L8.37913 3.18186L8.70166 2.96947L8.95338 3.09008L9.16053 3.38639L9.13169 3.5778L9.00845 4.37755L8.76721 5.63093L8.60988 6.47001H8.70166L8.80654 6.36513L9.23133 5.80137L9.94455 4.90984L10.2592 4.55586L10.6263 4.16516L10.8623 3.97899H11.3081L11.6358 4.4667L11.489 4.97015L11.0301 5.55227L10.6499 6.04523L10.1045 6.77942L9.76362 7.36678L9.79509 7.41398L9.87637 7.40611L11.1088 7.1439L11.7748 7.02328L12.5693 6.88693L12.9285 7.05475L12.9679 7.22519L12.8263 7.57393L11.9767 7.7837L10.9803 7.98298L9.49616 8.33435L9.47781 8.34746L9.49878 8.37368L10.1674 8.43661L10.4532 8.45234H11.1533L12.4565 8.54936L12.7974 8.77487L13.002 9.05019L12.9679 9.25996L12.4434 9.52742L11.7355 9.3596L10.0835 8.96628L9.51714 8.82469H9.43848V8.87189L9.91046 9.33338L10.7758 10.1148L11.8587 11.1217L11.9138 11.3708L11.7748 11.5674L11.628 11.5465L10.6761 10.8306L10.309 10.5081L9.47781 9.80799H9.42274V9.88141L9.61416 10.162L10.6263 11.6828L10.6787 12.1496L10.6053 12.3016L10.3431 12.3934L10.0547 12.341L9.46207 11.5097L8.85112 10.5736L8.35816 9.73457L8.29785 9.76865L8.00679 12.9021L7.87044 13.0621L7.55578 13.1827L7.29357 12.9834L7.1546 12.6609L7.29357 12.0237L7.46139 11.1925L7.59774 10.5317L7.72098 9.71097L7.7944 9.43827L7.78915 9.41991L7.72885 9.42778L7.11002 10.2773L6.16868 11.5491L5.42399 12.3462L5.24569 12.417L4.93627 12.2571L4.96512 11.9712L5.13818 11.7169L6.16868 10.4058L6.79012 9.59297L7.19131 9.12361L7.18869 9.05544H7.16509L4.42758 10.8332L3.93986 10.8962L3.73009 10.6995L3.75632 10.377L3.85596 10.2721L4.67931 9.70572L4.67668 9.70835Z" fill="black" fill-opacity="0.92"></path></svg></span> Claude 3 Opus</div></div></div></div><div data-v-2db9de5a="" class="ait-absolute ait-z-[9999999]  ait-popper ait-popper-y ait-py-1 ait-px-2 ait-rounded-md ait-shadow-lg dark:ait-bg-neutral-100 ait-bg-neutral-800 ait-text-gray-200 dark:ait-text-gray-900 ait:border-[1px] dark:ait-border-neutral-200 ait-border-neutral-300" style="display: none;"><div data-v-2db9de5a="" class="ait-text-xs ait-font-medium ait-text-gray-200 dark:ait-text-gray-800 ait-whitespace-nowrap ait-max-w-[170px] ait-w-full"><div class="ait-text-xs"><div class="ait-text-xs ait-break-words ait-whitespace-pre-wrap ait-text-gray-200 dark:ait-text-gray-800">Anthropic's most powerful model, delivering state-of-the-art performance on highly complex tasks and demonstrating fluency and human-like understanding.</div><div class="ait-text-[10px] ait-opacity-60 ait-mt-1 ait-text-gray-200 dark:ait-text-gray-800">Cost 2 Advanced query.</div></div></div></div><div data-v-2db9de5a="" class="ait-hide ait-left-0 ait-top-0 ait-bottom-0 ait-left-0 ait-top-1/2 ait-left-1/2 ait-right-1/2 ait-bottom-1/2 ait-translate-x-[-100%] ait-translate-y-[-100%] ait-translate-x-[-120%] ait-translate-y-[-120%]"></div></li><li data-v-2db9de5a="" class="ait-relative ait-block ait-popper-container"><div class="ait-flex ait-px-4 ait-py-2.5 hover:ait-bg-neutral-100 dark:hover:ait-bg-neutral-700 hover:ait-rounded-lg ait-cursor-pointer" style=""><div class="ait-w-full"><div class="ait-gap-4 ait-text-neutral-900 dark:ait-text-white ait-text-sm dark:ait-text-neutral-400"><div class="ait-ai-icon ait-flex ait-gap-2 ait-text-[12px]"><span><svg class="ait-w-[16px] ait-h-[16px] ait-Gemini 1.5 Flash" width="16" height="16" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none"><rect width="100%" height="100%" rx="8" fill="#F3F3FA"></rect><path d="M8 14C8 13.17 7.84002 12.39 7.52 11.66C7.21002 10.93 6.785 10.295 6.245 9.755C5.705 9.215 5.07 8.78998 4.34 8.48C3.61 8.15998 2.83 8 2 8C2.83 8 3.61 7.84498 4.34 7.535C5.07 7.21498 5.705 6.785 6.245 6.245C6.785 5.705 7.21002 5.07 7.52 4.34C7.84002 3.61 8 2.83 8 2C8 2.83 8.15502 3.61 8.465 4.34C8.78502 5.07 9.215 5.705 9.755 6.245C10.295 6.785 10.93 7.21498 11.66 7.535C12.39 7.84498 13.17 8 14 8C13.17 8 12.39 8.15998 11.66 8.48C10.93 8.78998 10.295 9.215 9.755 9.755C9.215 10.295 8.78502 10.93 8.465 11.66C8.15502 12.39 8 13.17 8 14Z" fill="url(#gemini_flash_linear)"></path><defs><radialgradient id="gemini_flash_linear" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(3.1909 6.87693) rotate(18.6832) scale(12.7725 102.316)"><stop offset="0.0671246" stop-color="#9168C0"></stop><stop offset="0.342551" stop-color="#5684D1"></stop><stop offset="0.672076" stop-color="#1BA1E3"></stop></radialgradient></defs></svg></span> Gemini 1.5 Flash</div></div></div></div><div data-v-2db9de5a="" class="ait-absolute ait-z-[9999999]  ait-popper ait-popper-y ait-py-1 ait-px-2 ait-rounded-md ait-shadow-lg dark:ait-bg-neutral-100 ait-bg-neutral-800 ait-text-gray-200 dark:ait-text-gray-900 ait:border-[1px] dark:ait-border-neutral-200 ait-border-neutral-300" style="display: none;"><div data-v-2db9de5a="" class="ait-text-xs ait-font-medium ait-text-gray-200 dark:ait-text-gray-800 ait-whitespace-nowrap ait-max-w-[170px] ait-w-full"><div class="ait-text-xs"><div class="ait-text-xs ait-break-words ait-whitespace-pre-wrap ait-text-gray-200 dark:ait-text-gray-800">Google's most balanced model, perfectly blending capability with efficiency.</div><div class="ait-text-[10px] ait-opacity-60 ait-mt-1 ait-text-gray-200 dark:ait-text-gray-800">Cost 1 Fast query.</div></div></div></div><div data-v-2db9de5a="" class="ait-hide ait-left-0 ait-top-0 ait-bottom-0 ait-left-0 ait-top-1/2 ait-left-1/2 ait-right-1/2 ait-bottom-1/2 ait-translate-x-[-100%] ait-translate-y-[-100%] ait-translate-x-[-120%] ait-translate-y-[-120%]"></div></li><li data-v-2db9de5a="" class="ait-relative ait-block ait-popper-container"><div class="ait-flex ait-px-4 ait-py-2.5 hover:ait-bg-neutral-100 dark:hover:ait-bg-neutral-700 hover:ait-rounded-lg ait-cursor-pointer" style=""><div class="ait-w-full"><div class="ait-gap-4 ait-text-neutral-900 dark:ait-text-white ait-text-sm dark:ait-text-neutral-400"><div class="ait-ai-icon ait-flex ait-gap-2 ait-text-[12px]"><span><svg class="ait-w-[16px] ait-h-[16px] ait-Gemini 1.5 Pro" width="16" height="16" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none"><rect width="100%" height="100%" rx="8" fill="#000002"></rect><path d="M8 14C8 13.17 7.84002 12.39 7.52 11.66C7.21002 10.93 6.785 10.295 6.245 9.755C5.705 9.215 5.07 8.78998 4.34 8.48C3.61 8.15998 2.83 8 2 8C2.83 8 3.61 7.84498 4.34 7.535C5.07 7.21498 5.705 6.785 6.245 6.245C6.785 5.705 7.21002 5.07 7.52 4.34C7.84002 3.61 8 2.83 8 2C8 2.83 8.15502 3.61 8.465 4.34C8.78502 5.07 9.215 5.705 9.755 6.245C10.295 6.785 10.93 7.21498 11.66 7.535C12.39 7.84498 13.17 8 14 8C13.17 8 12.39 8.15998 11.66 8.48C10.93 8.78998 10.295 9.215 9.755 9.755C9.215 10.295 8.78502 10.93 8.465 11.66C8.15502 12.39 8 13.17 8 14Z" fill="url(#gemini_flash_linear)"></path><defs><radialgradient id="gemini_flash_linear" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(3.1909 6.87693) rotate(18.6832) scale(12.7725 102.316)"><stop offset="0.0671246" stop-color="#9168C0"></stop><stop offset="0.342551" stop-color="#5684D1"></stop><stop offset="0.672076" stop-color="#1BA1E3"></stop></radialgradient></defs></svg></span> Gemini 1.5 Pro</div></div></div></div><div data-v-2db9de5a="" class="ait-absolute ait-z-[9999999]  ait-popper ait-popper-y ait-py-1 ait-px-2 ait-rounded-md ait-shadow-lg dark:ait-bg-neutral-100 ait-bg-neutral-800 ait-text-gray-200 dark:ait-text-gray-900 ait:border-[1px] dark:ait-border-neutral-200 ait-border-neutral-300" style="display: none;"><div data-v-2db9de5a="" class="ait-text-xs ait-font-medium ait-text-gray-200 dark:ait-text-gray-800 ait-whitespace-nowrap ait-max-w-[170px] ait-w-full"><div class="ait-text-xs"><div class="ait-text-xs ait-break-words ait-whitespace-pre-wrap ait-text-gray-200 dark:ait-text-gray-800">Google's lightweight model, optimized for speed and efficiency.</div><div class="ait-text-[10px] ait-opacity-60 ait-mt-1 ait-text-gray-200 dark:ait-text-gray-800">Cost 1 Advanced query.</div></div></div></div><div data-v-2db9de5a="" class="ait-hide ait-left-0 ait-top-0 ait-bottom-0 ait-left-0 ait-top-1/2 ait-left-1/2 ait-right-1/2 ait-bottom-1/2 ait-translate-x-[-100%] ait-translate-y-[-100%] ait-translate-x-[-120%] ait-translate-y-[-120%]"></div></li><li data-v-2db9de5a="" class="ait-relative ait-block ait-popper-container"><div class="ait-flex ait-px-4 ait-py-2.5 hover:ait-bg-neutral-100 dark:hover:ait-bg-neutral-700 hover:ait-rounded-lg ait-cursor-pointer" style=""><div class="ait-w-full"><div class="ait-gap-4 ait-text-neutral-900 dark:ait-text-white ait-text-sm dark:ait-text-neutral-400"><div class="ait-ai-icon ait-flex ait-gap-2 ait-text-[12px]"><span><svg class="ait-w-[16px] ait-h-[16px] ait-Llama 3.1 70B" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none"> <rect width="16" height="16" rx="8" fill="white"></rect> <rect width="16" height="16" rx="8" fill="#F3F3FA"></rect> <path d="M2.85869 9.20519C2.85869 9.70602 2.96857 10.0905 3.11229 10.3231C3.30068 10.6278 3.58164 10.7571 3.8681 10.7571C4.23751 10.7571 4.5755 10.6654 5.22681 9.76459C5.74874 9.04257 6.36346 8.0291 6.77715 7.39372L7.47774 6.31729C7.9644 5.56972 8.52769 4.73871 9.17356 4.17541C9.70098 3.71562 10.2696 3.46021 10.842 3.46021C11.8031 3.46021 12.7185 4.01713 13.4191 5.06164C14.1858 6.20554 14.558 7.64644 14.558 9.13333C14.558 10.0173 14.3838 10.6668 14.0873 11.1799C13.8009 11.6762 13.2426 12.172 12.3034 12.172V10.7569C13.1076 10.7569 13.3082 10.018 13.3082 9.17234C13.3082 7.96729 13.0273 6.63 12.4083 5.67444C11.9691 4.99665 11.3999 4.58251 10.7736 4.58251C10.0963 4.58251 9.55122 5.09345 8.93864 6.00419C8.61296 6.48815 8.27865 7.07787 7.90325 7.74336L7.48999 8.47543C6.65997 9.94753 6.44966 10.2827 6.03459 10.8358C5.30713 11.8045 4.68587 12.1718 3.8681 12.1718C2.89792 12.1718 2.28446 11.7516 1.9045 11.1186C1.59436 10.6027 1.44202 9.92583 1.44202 9.15459L2.85869 9.20519Z" fill="#0081FB"></path> <path d="M2.55908 5.16128C3.20858 4.16011 4.1459 3.45996 5.22096 3.45996C5.84354 3.45996 6.4626 3.64423 7.1088 4.17198C7.81576 4.74885 8.56926 5.6993 9.50933 7.26508L9.84639 7.82684C10.6601 9.18242 11.123 9.87982 11.394 10.2087C11.7424 10.631 11.9865 10.7569 12.3035 10.7569C13.1076 10.7569 13.3082 10.018 13.3082 9.17237L14.558 9.13314C14.558 10.0171 14.3838 10.6666 14.0873 11.1798C13.8009 11.676 13.2426 12.1718 12.3035 12.1718C11.7196 12.1718 11.2024 12.045 10.6304 11.5054C10.1909 11.0912 9.67668 10.3555 9.28122 9.69418L8.10491 7.72927C7.51475 6.74315 6.97315 6.0079 6.66 5.67491C6.32289 5.31681 5.88958 4.88444 5.19811 4.88444C4.63849 4.88444 4.16321 5.27714 3.7655 5.87786L2.55908 5.16128Z" fill="url(#llama_flash_linear_color)"></path> <path d="M5.19808 4.88444C4.63846 4.88444 4.16318 5.27715 3.76547 5.87786C3.20305 6.72667 2.85896 7.991 2.85896 9.20517C2.85896 9.706 2.96884 10.0905 3.11257 10.3231L1.9045 11.1185C1.59436 10.6027 1.44202 9.92581 1.44202 9.15457C1.44202 7.75201 1.82698 6.29018 2.55905 5.16128C3.20855 4.16011 4.14587 3.45996 5.22093 3.45996L5.19808 4.88444Z" fill="url(#llama_flash_linear_color)"></path> <defs> <lineargradient id="llama_flash_linear_color" x1="4.28529" y1="7.5901" x2="13.3163" y2="8.04621" gradientUnits="userSpaceOnUse"> <stop stop-color="#0064E1"></stop> <stop offset="0.4" stop-color="#0064E1"></stop> <stop offset="0.83" stop-color="#0073EE"></stop> <stop offset="1" stop-color="#0082FB"></stop> </lineargradient> <lineargradient id="paint1_linear_38187_34499_:r11:" x1="3.33145" y1="9.79994" x2="3.33145" y2="6.47032" gradientUnits="userSpaceOnUse"> <stop stop-color="#0082FB"></stop> <stop offset="1" stop-color="#0064E0"></stop> </lineargradient> </defs> </svg></span> Llama 3.1 70B</div></div></div></div><div data-v-2db9de5a="" class="ait-absolute ait-z-[9999999]  ait-popper ait-popper-y ait-py-1 ait-px-2 ait-rounded-md ait-shadow-lg dark:ait-bg-neutral-100 ait-bg-neutral-800 ait-text-gray-200 dark:ait-text-gray-900 ait:border-[1px] dark:ait-border-neutral-200 ait-border-neutral-300" style="display: none;"><div data-v-2db9de5a="" class="ait-text-xs ait-font-medium ait-text-gray-200 dark:ait-text-gray-800 ait-whitespace-nowrap ait-max-w-[170px] ait-w-full"><div class="ait-text-xs"><div class="ait-text-xs ait-break-words ait-whitespace-pre-wrap ait-text-gray-200 dark:ait-text-gray-800">Meta's versatile model, excelling in long-form summarization, multilingual conversations, and coding assistance.</div><div class="ait-text-[10px] ait-opacity-60 ait-mt-1 ait-text-gray-200 dark:ait-text-gray-800">Cost 1 Fast query.</div></div></div></div><div data-v-2db9de5a="" class="ait-hide ait-left-0 ait-top-0 ait-bottom-0 ait-left-0 ait-top-1/2 ait-left-1/2 ait-right-1/2 ait-bottom-1/2 ait-translate-x-[-100%] ait-translate-y-[-100%] ait-translate-x-[-120%] ait-translate-y-[-120%]"></div></li><li data-v-2db9de5a="" class="ait-relative ait-block ait-popper-container"><div class="ait-flex ait-px-4 ait-py-2.5 hover:ait-bg-neutral-100 dark:hover:ait-bg-neutral-700 hover:ait-rounded-lg ait-cursor-pointer" style=""><div class="ait-w-full"><div class="ait-gap-4 ait-text-neutral-900 dark:ait-text-white ait-text-sm dark:ait-text-neutral-400"><div class="ait-ai-icon ait-flex ait-gap-2 ait-text-[12px]"><span><svg class="ait-w-[16px] ait-h-[16px] ait-Llama 3.1 405B" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none"> <rect width="16" height="16" rx="8" fill="url(#llama_flash_linear_color_1)"></rect> <rect width="16" height="16" rx="8" fill="url(#llama_flash_linear_color_1)"></rect> <path fill-rule="evenodd" clip-rule="evenodd" d="M9.05626 4.53918C9.53096 4.12535 10.0427 3.89546 10.5579 3.89546C11.4229 3.89546 12.2468 4.39672 12.8774 5.33682C13.5675 6.36638 13.9025 7.66325 13.9025 9.00152C13.9025 9.79713 13.7457 10.3817 13.4788 10.8436C13.221 11.2902 12.7185 11.7364 11.8733 11.7364C11.3478 11.7364 10.8823 11.6222 10.3675 11.1365C9.97189 10.7637 9.50905 10.1016 9.15313 9.50633L8.09439 7.73782C8.0642 7.68737 8.03415 7.63765 8.00426 7.58868L7.91292 7.75048L7.54097 8.40938C6.79391 9.73434 6.60462 10.036 6.23104 10.5338C5.5763 11.4057 5.01714 11.7362 4.28111 11.7362C3.40791 11.7362 2.85577 11.3581 2.51379 10.7883C2.23465 10.3241 2.09753 9.7148 2.09753 9.02065C2.09753 7.75829 2.44402 6.44258 3.10291 5.42652C3.68749 4.52543 4.53112 3.89526 5.49872 3.89526C6.05906 3.89526 6.61624 4.06111 7.19785 4.53611C7.50448 4.78632 7.82084 5.11461 8.16103 5.54533C8.43741 5.17199 8.73552 4.8189 9.05626 4.53918ZM3.60085 10.0724C3.4715 9.863 3.3726 9.51696 3.3726 9.06619C3.3726 7.97339 3.68254 6.83544 4.18874 6.07147C4.54669 5.53081 4.97447 5.17735 5.47815 5.17735C6.1005 5.17735 6.4905 5.56651 6.79391 5.88881C6.94538 6.04988 7.15623 6.31551 7.40134 6.66455L6.89938 7.43579C6.52704 8.00766 5.97377 8.91983 5.50401 9.56967C4.9178 10.3805 4.6136 10.463 4.28111 10.463C4.02329 10.463 3.77041 10.3466 3.60085 10.0724ZM12.7776 9.03662C12.7776 9.79773 12.597 10.4628 11.8733 10.4628C11.5879 10.4628 11.3683 10.3495 11.0547 9.96941C10.8108 9.67341 10.3942 9.04572 9.6618 7.82564L9.35843 7.32003C9.14209 6.95969 8.93672 6.63555 8.74022 6.34399C8.77523 6.28974 8.81008 6.23678 8.84482 6.18515C9.39616 5.36545 9.88674 4.90559 10.4964 4.90559C11.06 4.90559 11.5724 5.27833 11.9677 5.88837C12.5247 6.74842 12.7776 7.95203 12.7776 9.03662Z" fill="white"></path> <defs> <lineargradient id="llama_flash_linear_color_1" x1="14" y1="2" x2="2" y2="14" gradientUnits="userSpaceOnUse"> <stop stop-color="#0081FB"></stop> <stop offset="1" stop-color="#0165E2"></stop> </lineargradient> <lineargradient id="llama_flash_linear_color_1" x1="12.5" y1="1.5" x2="2.5" y2="16" gradientUnits="userSpaceOnUse"> <stop stop-color="#F0A3DB"></stop> <stop offset="1" stop-color="#006AFF"></stop> </lineargradient> </defs> </svg></span> Llama 3.1 405B</div></div></div></div><div data-v-2db9de5a="" class="ait-absolute ait-z-[9999999]  ait-popper ait-popper-y ait-py-1 ait-px-2 ait-rounded-md ait-shadow-lg dark:ait-bg-neutral-100 ait-bg-neutral-800 ait-text-gray-200 dark:ait-text-gray-900 ait:border-[1px] dark:ait-border-neutral-200 ait-border-neutral-300" style="display: none;"><div data-v-2db9de5a="" class="ait-text-xs ait-font-medium ait-text-gray-200 dark:ait-text-gray-800 ait-whitespace-nowrap ait-max-w-[170px] ait-w-full"><div class="ait-text-xs"><div class="ait-text-xs ait-break-words ait-whitespace-pre-wrap ait-text-gray-200 dark:ait-text-gray-800">Meta's most powerful open-source model, rivaling top AI assistants in general knowledge, math, and multilingual capabilities.</div><div class="ait-text-[10px] ait-opacity-60 ait-mt-1 ait-text-gray-200 dark:ait-text-gray-800">Cost 1 Advanced query.</div></div></div></div><div data-v-2db9de5a="" class="ait-hide ait-left-0 ait-top-0 ait-bottom-0 ait-left-0 ait-top-1/2 ait-left-1/2 ait-right-1/2 ait-bottom-1/2 ait-translate-x-[-100%] ait-translate-y-[-100%] ait-translate-x-[-120%] ait-translate-y-[-120%]"></div></li></div></ul></div></div><span class="ait-hide ait-my-2.5 ait-left-[-8px]"></span><div class="ait-flex ait-items-center ait-relative ait-space-x-1 ait-grid-flow-row"><button data-ait-dropdown-toggle="ait-credit-dropdown" class="ait-flex ait-items-center ait-relative dark:ait-text-neutral-200 ait-border ait-rounded-full ait-w-[45px]"><img src="chrome-extension://becfinhbfclcgokjlobojlnldbfillpf/assets/images/coin.png" width="16px" height="16px" alt="coin image" class="ait-w-4 ait-h-4"><div class="ait-text-sm">10</div></button><button type="button" id="aitopia-pricing" class="ait-text-neutral-500 hover:ait-text-neutral-50 hover:ait-bg-green-700 ait-ml-1 ait-rounded-lg focus:ait-outline-none ait-text-[10px] ait-px-1 ait-py-1 ait-text-center aitopia-pricing">Upgrade</button></div><div id="ait-credit-dropdown" class="ait-z-20 ait-left-0 ait-bottom-[40px] ait-width-full ait-absolute ait-hide ait-w-full ait-max-w-[220px] ait-bg-white ait-divide-y ait-divide-neutral-100 ait-rounded-lg ait-shadow dark:ait-bg-neutral-900 dark:ait-divide-neutral-700"><div class="ait-block ait-px-3 ait-py-3 ait-font-medium ait-text-center ait-text-neutral-700 ait-rounded-t-lg ait-bg-neutral-50 dark:ait-bg-neutral-950 dark:ait-text-white"><span class="ait-font-bold">Free Plan</span><button type="button" class="ait-text-white ait-bg-green-700 hover:ait-bg-green-800 focus:ait-outline-none focus:ait-ring-4 focus:ait-ring-green-300 ait-font-medium ait-rounded-full ait-text-sm ait-px-5 ait-py-1 ait-text-center ait-ml-5 ait-mr-2 dark:ait-bg-green-600 dark:hover:ait-bg-green-700 dark:focus:ait-ring-green-800 aitopia-pricing">Upgrade</button></div><div class="ait-divide-y ait-divide-neutral-100 dark:ait-divide-neutral-700"><div class="ait-flex ait-px-3 ait-py-3 hover:ait-bg-neutral-100 dark:hover:ait-bg-neutral-700"><div class="ait-w-full ait-px-1 ait-cursor-pointer"><div class="ait-grid ait-grid-cols-4 ait-gap-4 ait-text-neutral-900 dark:ait-text-white ait-text-sm ait-mb-1.5 dark:ait-text-neutral-400"><div class="ait-col-span-3">Fast Text <div class="ait-text-[10px] ait-text-neutral-500 ait-pt-2 dark:ait-text-neutral-400">GPT-4o Mini, Claude 3 Haiku, Gemini 1.5 Flash, Llama 3.1 70B, Dall-e 3, SDXL v1.0</div></div><div class="ait-text-right"><span class="ait-text-sm">10</span></div></div></div></div><div class="ait-flex ait-px-3 ait-py-3 hover:ait-bg-neutral-100 dark:hover:ait-bg-neutral-700"><div class="ait-w-full ait-px-1 ait-cursor-pointer"><div class="ait-grid ait-grid-cols-4 ait-gap-4 ait-text-neutral-900 dark:ait-text-white ait-text-sm ait-mb-1.5 dark:ait-text-neutral-400"><div class="ait-col-span-3">Advanced Text <div class="ait-text-[10px] ait-text-neutral-500 ait-pt-2 dark:ait-text-neutral-400">GPT-4o, GPT-4, o1 Mini, o1 Preview, Claude 3 Sonnet, Claude 3 Opus, Gemini 1.5 Pro, Llama 3.1 405B, SD3, SD3 Large Turbo</div></div><div class="ait-text-right"><span class="ait-text-sm">0</span></div></div></div></div></div></div><div class="ait-flex ait-flex-row ait-items-center ait-gap-1 ait-ml-2"><li data-v-2db9de5a="" class="ait-relative ait-block ait-popper-container"><button class="ait-cursor-pointer ait-p-1.5 ait-rounded-full ait-flex ait-items-center ait-justify-center hover:ait-bg-neutral-300 dark:hover:ait-bg-neutral-900 ait-border ait-transition-all"><svg class="svg-inline--fa fa-scissors ait-w-4 ait-h-4" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="scissors" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path class="" fill="currentColor" d="M256 192l-39.5-39.5c4.9-12.6 7.5-26.2 7.5-40.5C224 50.1 173.9 0 112 0S0 50.1 0 112s50.1 112 112 112c14.3 0 27.9-2.7 40.5-7.5L192 256l-39.5 39.5c-12.6-4.9-26.2-7.5-40.5-7.5C50.1 288 0 338.1 0 400s50.1 112 112 112s112-50.1 112-112c0-14.3-2.7-27.9-7.5-40.5L499.2 76.8c7.1-7.1 7.1-18.5 0-25.6c-28.3-28.3-74.1-28.3-102.4 0L256 192zm22.6 150.6L396.8 460.8c28.3 28.3 74.1 28.3 102.4 0c7.1-7.1 7.1-18.5 0-25.6L342.6 278.6l-64 64zM64 112a48 48 0 1 1 96 0 48 48 0 1 1 -96 0zm48 240a48 48 0 1 1 0 96 48 48 0 1 1 0-96z"></path></svg></button><div data-v-2db9de5a="" class="ait-absolute ait-z-[9999999] ait-top-0 ait-left-1/2 ait-transform ait--translate-x-1/2 ait-py-1 ait-px-2 ait-rounded-md ait-shadow-lg dark:ait-bg-neutral-100 ait-bg-neutral-800 ait-text-gray-900 dark:ait-text-gray-200 ait-translate-y-[-120%] ait:border-[1px] dark:ait-border-neutral-200 ait-border-neutral-300" style="display: none;"><div data-v-2db9de5a="" class="ait-text-xs ait-font-medium ait-text-gray-200 dark:ait-text-gray-800 ait-whitespace-nowrap ait-max-w-[200px] ait-w-full">Take a screenshot</div></div><div data-v-2db9de5a="" class="ait-hide ait-left-0 ait-top-0 ait-bottom-0 ait-left-0 ait-top-1/2 ait-left-1/2 ait-right-1/2 ait-bottom-1/2 ait-translate-x-[-100%] ait-translate-y-[-100%] ait-translate-x-[-120%] ait-translate-y-[-120%]"></div></li><li data-v-2db9de5a="" class="ait-relative ait-block ait-popper-container"><button class="ait-cursor-pointer ait-p-1.5 ait-rounded-full ait-flex ait-items-center ait-justify-center hover:ait-bg-neutral-300 dark:hover:ait-bg-neutral-900 ait-border ait-transition-all"><svg class="svg-inline--fa fa-image ait-w-4 ait-h-4" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="image" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path class="" fill="currentColor" d="M0 96C0 60.7 28.7 32 64 32l384 0c35.3 0 64 28.7 64 64l0 320c0 35.3-28.7 64-64 64L64 480c-35.3 0-64-28.7-64-64L0 96zM323.8 202.5c-4.5-6.6-11.9-10.5-19.8-10.5s-15.4 3.9-19.8 10.5l-87 127.6L170.7 297c-4.6-5.7-11.5-9-18.7-9s-14.2 3.3-18.7 9l-64 80c-5.8 7.2-6.9 17.1-2.9 25.4s12.4 13.6 21.6 13.6l96 0 32 0 208 0c8.9 0 17.1-4.9 21.2-12.8s3.6-17.4-1.4-24.7l-120-176zM112 192a48 48 0 1 0 0-96 48 48 0 1 0 0 96z"></path></svg></button><div data-v-2db9de5a="" class="ait-absolute ait-z-[9999999] ait-top-0 ait-left-1/2 ait-transform ait--translate-x-1/2 ait-py-1 ait-px-2 ait-rounded-md ait-shadow-lg dark:ait-bg-neutral-100 ait-bg-neutral-800 ait-text-gray-900 dark:ait-text-gray-200 ait-translate-y-[-120%] ait:border-[1px] dark:ait-border-neutral-200 ait-border-neutral-300" style="display: none;"><div data-v-2db9de5a="" class="ait-text-xs ait-font-medium ait-text-gray-200 dark:ait-text-gray-800 ait-whitespace-nowrap ait-max-w-[200px] ait-w-full">Upload an image</div></div><div data-v-2db9de5a="" class="ait-hide ait-left-0 ait-top-0 ait-bottom-0 ait-left-0 ait-top-1/2 ait-left-1/2 ait-right-1/2 ait-bottom-1/2 ait-translate-x-[-100%] ait-translate-y-[-100%] ait-translate-x-[-120%] ait-translate-y-[-120%]"></div></li><li data-v-2db9de5a="" class="ait-relative ait-block ait-popper-container"><button data-ait-dropdown-toggle="ait-pdf-upload-modal" class="ait-cursor-pointer ait-p-1.5 ait-rounded-full ait-flex ait-items-center ait-justify-center hover:ait-bg-neutral-300 dark:hover:ait-bg-neutral-900 ait-border ait-transition-all"><span><svg class="ait-w-4 ait-h-4 lg:ait-w-4 lg:ait-h-4 ait-chat_pdf_icon" fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><path fill-rule="evenodd" clip-rule="evenodd" d="M3.79128 0.289062C2.90763 0.289062 2.19128 1.00541 2.19128 1.88906V2.33517C1.42585 2.45474 0.84021 3.11694 0.84021 3.91592V4.6181C0.84021 5.41709 1.42585 6.07929 2.19128 6.19886V13.4C2.19128 14.2837 2.90763 15 3.79128 15H12.6C13.4837 15 14.2 14.2837 14.2 13.4V1.88906C14.2 1.00541 13.4837 0.289062 12.6 0.289062H3.79128ZM3.39128 6.21811V13.4C3.39128 13.6209 3.57037 13.8 3.79128 13.8H12.6C12.8209 13.8 13 13.6209 13 13.4V1.88906C13 1.66815 12.8209 1.48906 12.6 1.48906H3.79128C3.57037 1.48906 3.39128 1.66815 3.39128 1.88906V2.31592H5.84458C6.72824 2.31592 7.44458 3.03226 7.44458 3.91592V4.6181C7.44458 5.50176 6.72824 6.21811 5.84458 6.21811H3.39128ZM2.04021 3.91592C2.04021 3.695 2.2193 3.51592 2.44021 3.51592H5.84458C6.0655 3.51592 6.24458 3.695 6.24458 3.91592V4.6181C6.24458 4.83902 6.0655 5.01811 5.84458 5.01811H2.44021C2.2193 5.01811 2.04021 4.83902 2.04021 4.6181V3.91592Z" fill="currentColor" fill-opacity="0.5"></path><path d="M11.2151 11.1661C10.6578 11.1247 10.1215 10.9178 9.68817 10.5452C8.84183 10.7314 8.03717 11.0006 7.23237 11.3317C6.59261 12.4699 5.99415 13.0492 5.47818 13.0492C5.37504 13.0492 5.2512 13.0284 5.1687 12.9664C4.94166 12.8629 4.81787 12.6353 4.81787 12.4078C4.81787 12.2215 4.85907 11.704 6.81975 10.8558C7.27352 10.028 7.62435 9.17948 7.91333 8.28976C7.6657 7.79314 7.12923 6.57219 7.50066 5.95153C7.62435 5.72383 7.87213 5.59969 8.14042 5.6204C8.34676 5.6204 8.55309 5.72387 8.67679 5.88942C8.94522 6.26196 8.92452 7.04815 8.5736 8.20704C8.90392 8.82784 9.3373 9.38643 9.85317 9.86254C10.2866 9.77972 10.7198 9.71756 11.1533 9.71756C12.1232 9.73822 12.2676 10.1937 12.2471 10.4624C12.2471 11.1661 11.5661 11.1661 11.2151 11.1661ZM5.43693 12.4491L5.49888 12.4281C5.78776 12.3248 6.01475 12.118 6.17989 11.8489C5.87026 11.9731 5.62262 12.1803 5.43693 12.4491ZM8.18162 6.2411H8.11957C8.09897 6.2411 8.05773 6.2411 8.03713 6.26191C7.95453 6.6136 8.01662 6.98614 8.16107 7.31722C8.28476 6.96543 8.28476 6.59284 8.18162 6.2411ZM8.32611 9.24164L8.30536 9.2831L8.28471 9.26235C8.09893 9.73817 7.89264 10.2144 7.66555 10.6694L7.70685 10.6488V10.6901C8.16107 10.5244 8.65619 10.3799 9.11031 10.2764L9.08951 10.2556H9.15146C8.84178 9.94502 8.553 9.59333 8.32611 9.24164ZM11.1326 10.3385C10.9469 10.3385 10.7819 10.3385 10.5961 10.3799C10.8024 10.4831 11.0087 10.5245 11.215 10.5453C11.3598 10.566 11.504 10.5453 11.6278 10.504C11.6278 10.4417 11.5453 10.3385 11.1326 10.3385Z" fill="currentColor"></path></svg></span></button><div data-v-2db9de5a="" class="ait-absolute ait-z-[9999999] ait-top-0 ait-left-1/2 ait-transform ait--translate-x-1/2 ait-py-1 ait-px-2 ait-rounded-md ait-shadow-lg dark:ait-bg-neutral-100 ait-bg-neutral-800 ait-text-gray-900 dark:ait-text-gray-200 ait-translate-y-[-120%] ait:border-[1px] dark:ait-border-neutral-200 ait-border-neutral-300" style="display: none;"><div data-v-2db9de5a="" class="ait-text-xs ait-font-medium ait-text-gray-200 dark:ait-text-gray-800 ait-whitespace-nowrap ait-max-w-[200px] ait-w-full">Upload an pdf</div></div><div data-v-2db9de5a="" class="ait-hide ait-left-0 ait-top-0 ait-bottom-0 ait-left-0 ait-top-1/2 ait-left-1/2 ait-right-1/2 ait-bottom-1/2 ait-translate-x-[-100%] ait-translate-y-[-100%] ait-translate-x-[-120%] ait-translate-y-[-120%]"></div></li><input type="file" id="imageFileInput" accept="image/jpeg,image/png" style="display: none;"></div><div id="ait-image-vision-modal" class="ait-hide"><div tabindex="-1" aria-hide="true" class="ait-fixed !ait-z-[999999] ait-modal ait-m-0 ait-bg-[rgba(0,0,0,.5)] ait-top-0 ait-left-0 ait-bottom-0 ait-right-0 ait-w-full ait-p-4 ait-overflow-x-hidden ait-overflow-y-auto lg:ait-inset-0 h-[calc(100%)] ait-max-h-full ait-justify-center ait-flex ait-w-full ait-items-center" data-modal-hide=""><div class="ait-relative ait-w-full lg:ait-max-w-screen-xl ait-max-h-full"><div class="ait-relative ait-bg-white ait-rounded-lg ait-shadow dark:ait-bg-neutral-800"><div class="ait-px-6 ait-py-4 ait-border-b ait-rounded-t dark:ait-border-neutral-600"><h3 class="ait-text-base ait-font-semibold ait-text-neutral-900 lg:ait-text-lg dark:ait-text-white ait-text-center">Vision</h3><button type="button" class="ait-absolute ait-top-3 ait-right-2.5 ait-text-neutral-400 ait-bg-transparent hover:ait-bg-neutral-200 hover:ait-text-neutral-900 ait-rounded-lg ait-text-sm ait-w-8 ait-h-8 ait-ml-auto ait-inline-flex ait-justify-center ait-items-center dark:hover:ait-bg-neutral-600 dark:hover:ait-text-white" data-modal-hide=""><svg class="ait-w-3 ait-h-3" aria-hide="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"></path></svg><span class="ait-sr-only">Close modal</span></button></div><div class="ait-p-4 ait-w-full ait-text-center ait-flex ait-flex-col ait-items-center ait-gap-3"><p class="ait-px-4 ait-text-sm ait-text-neutral-600 dark:ait-text-neutral-400">Upload an image to easily get intelligent explanations and extract text from your images.</p><input type="file" id="imageFileInput" accept="image/jpeg,image/png" style="display: none;"><label for="imageFileInput" class="ait-w-full ait-h-full ait-relative ait-z-[9999]"><div class="ait-flex ait-flex-col ait-w-full ait-p-6 ait-items-center ait-justify-center ait-mt-4 ait-border-2 ait-border-dotted ait-border-neutral-300 dark:ait-border-neutral-700 ait-rounded-lg ait-text-neutral-600 dark:ait-text-neutral-400 ait-text-center ait-cursor-pointer ait-transition-all hover:ait-opacity-70"><svg class="svg-inline--fa fa-image ait-w-24 ait-h-24 ait-opacity-60" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="image" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path class="" fill="currentColor" d="M0 96C0 60.7 28.7 32 64 32l384 0c35.3 0 64 28.7 64 64l0 320c0 35.3-28.7 64-64 64L64 480c-35.3 0-64-28.7-64-64L0 96zM323.8 202.5c-4.5-6.6-11.9-10.5-19.8-10.5s-15.4 3.9-19.8 10.5l-87 127.6L170.7 297c-4.6-5.7-11.5-9-18.7-9s-14.2 3.3-18.7 9l-64 80c-5.8 7.2-6.9 17.1-2.9 25.4s12.4 13.6 21.6 13.6l96 0 32 0 208 0c8.9 0 17.1-4.9 21.2-12.8s3.6-17.4-1.4-24.7l-120-176zM112 192a48 48 0 1 0 0-96 48 48 0 1 0 0 96z"></path></svg><div class="ait-flex ait-flex-col ait-items-center ait-gap-1 ait-mt-2"><span class="ait-block ait-text-xs">Supported image types are JPEG and PNG</span></div><span class="ait-block ait-mt-2 ait-opacity-60">Drag your Image here or click to upload</span></div></label></div><div class="ait-flex ait-flex-row ait-justify-center ait-items-center ait-gap-2 ait-px-4"><div class="ait-h-[1px] ait-w-1/2 ait-bg-neutral-200 dark:ait-bg-neutral-600"></div><p class="ait-text-center ait-text-neutral-600 dark:ait-text-neutral-400 ait-text-lg ait-my-4">Or</p><div class="ait-h-[1px] ait-w-1/2 ait-bg-neutral-200 dark:ait-bg-neutral-600"></div></div><div class="ait-flex ait-flex-col ait-px-4 ait-mt-2"><button class="ait-cursor-pointer ait-w-full ait-font-semibold ait-text-white ait-text-sm ait-py-2 ait-rounded-md ait-flex ait-items-center ait-gap-2 ait-justify-center ait-bg-[var(--ait-link-color)] hover:ait-opacity-80 ait-transition-all"><svg class="svg-inline--fa fa-scissors ait-w-3 ait-h-3" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="scissors" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path class="" fill="currentColor" d="M256 192l-39.5-39.5c4.9-12.6 7.5-26.2 7.5-40.5C224 50.1 173.9 0 112 0S0 50.1 0 112s50.1 112 112 112c14.3 0 27.9-2.7 40.5-7.5L192 256l-39.5 39.5c-12.6-4.9-26.2-7.5-40.5-7.5C50.1 288 0 338.1 0 400s50.1 112 112 112s112-50.1 112-112c0-14.3-2.7-27.9-7.5-40.5L499.2 76.8c7.1-7.1 7.1-18.5 0-25.6c-28.3-28.3-74.1-28.3-102.4 0L256 192zm22.6 150.6L396.8 460.8c28.3 28.3 74.1 28.3 102.4 0c7.1-7.1 7.1-18.5 0-25.6L342.6 278.6l-64 64zM64 112a48 48 0 1 1 96 0 48 48 0 1 1 -96 0zm48 240a48 48 0 1 1 0 96 48 48 0 1 1 0-96z"></path></svg> Take a screenshot</button></div><div class="ait-h-6 ait-mt-4"><!----></div><!----></div></div></div></div><div id="ait-pdf-upload-modal" class="ait-hide"><div tabindex="-1" aria-hide="true" class="ait-fixed !ait-z-[999999] ait-modal ait-m-0 ait-bg-[rgba(0,0,0,.5)] ait-top-0 ait-left-0 ait-bottom-0 ait-right-0 ait-w-full ait-p-4 ait-overflow-x-hidden ait-overflow-y-auto lg:ait-inset-0 h-[calc(100%)] ait-max-h-full ait-justify-center ait-flex ait-items-center" data-modal-hide=""><div class="ait-relative ait-w-full lg:ait-max-w-screen-xl ait-max-h-full"><div class="ait-relative ait-bg-white ait-rounded-lg ait-shadow dark:ait-bg-neutral-800"><div class="ait-px-6 ait-py-4 ait-border-b ait-rounded-t dark:ait-border-neutral-600"><h3 class="ait-text-base ait-font-semibold ait-text-neutral-900 lg:ait-text-lg dark:ait-text-white ait-text-center">Upload PDF</h3><button type="button" class="ait-absolute ait-top-3 ait-right-2.5 ait-text-neutral-400 ait-bg-transparent hover:ait-bg-neutral-200 hover:ait-text-neutral-900 ait-rounded-lg ait-text-sm ait-w-8 ait-h-8 ait-ml-auto ait-inline-flex ait-justify-center ait-items-center dark:hover:ait-bg-neutral-600 dark:hover:ait-text-white" data-modal-hide=""><svg class="ait-w-3 ait-h-3" aria-hide="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"></path></svg><span class="ait-sr-only">Close modal</span></button></div><div class="ait-p-4 ait-w-full ait-text-center ait-flex ait-flex-col ait-items-center ait-gap-3"><p class="ait-px-4 ait-text-sm ait-text-neutral-600 dark:ait-text-neutral-400">Upload a PDF File to easily get intelligent summaries and answers for your documents.</p><input type="file" id="fileInputPDF" accept="application/pdf" style="display: none;"><label for="fileInputPDF" class="ait-w-full ait-h-full ait-relative ait-z-[9999]"><div class="ait-flex ait-flex-col ait-w-full ait-p-6 ait-items-center ait-justify-center ait-mt-4 ait-border-2 ait-border-dotted ait-border-neutral-300 dark:ait-border-neutral-700 ait-rounded-lg ait-text-neutral-600 dark:ait-text-neutral-400 ait-text-center ait-cursor-pointer ait-transition-all hover:ait-opacity-70"><span><svg class="ait-w-16 ait-h-16 ait-opacity-60 ait-pdf_icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 309.267 309.267" xml:space="preserve"><path style="fill:#e2574c" d="M38.658 0h164.23l87.049 86.711v203.227c0 10.679-8.659 19.329-19.329 19.329H38.658c-10.67 0-19.329-8.65-19.329-19.329V19.329C19.329 8.65 27.989 0 38.658 0z"></path><path style="fill:#b53629" d="M289.658 86.981h-67.372c-10.67 0-19.329-8.659-19.329-19.329V.193l86.701 86.788z"></path><path style="fill:#fff" d="M217.434 146.544c3.238 0 4.823-2.822 4.823-5.557 0-2.832-1.653-5.567-4.823-5.567h-18.44c-3.605 0-5.615 2.986-5.615 6.282v45.317c0 4.04 2.3 6.282 5.412 6.282 3.093 0 5.403-2.242 5.403-6.282v-12.438h11.153c3.46 0 5.19-2.832 5.19-5.644 0-2.754-1.73-5.49-5.19-5.49h-11.153v-16.903h13.24zm-62.327-11.124h-13.492c-3.663 0-6.263 2.513-6.263 6.243v45.395c0 4.629 3.74 6.079 6.417 6.079h14.159c16.758 0 27.824-11.027 27.824-28.047-.009-17.995-10.427-29.67-28.645-29.67zm.648 46.526h-8.225v-35.334h7.413c11.221 0 16.101 7.529 16.101 17.918 0 9.723-4.794 17.416-15.289 17.416zM106.33 135.42H92.964c-3.779 0-5.886 2.493-5.886 6.282v45.317c0 4.04 2.416 6.282 5.663 6.282s5.663-2.242 5.663-6.282v-13.231h8.379c10.341 0 18.875-7.326 18.875-19.107.001-11.529-8.233-19.261-19.328-19.261zm-.222 27.738h-7.703v-17.097h7.703c4.755 0 7.78 3.711 7.78 8.553-.01 4.833-3.025 8.544-7.78 8.544z"></path></svg></span><div class="ait-flex ait-flex-col ait-items-center ait-gap-1 ait-mt-2"><span class="ait-block ait-text-xs">Supported file type is PDF</span></div><span class="ait-block ait-mt-2 ait-opacity-60">Drag your PDF here or click to upload</span></div></label><!----><div class="ait-h-6"><!----></div></div><!----><!----></div></div></div></div></div></div><div class="ait-flex ait-flex-col ait-relative ait-items-end ait-p-1 ait-space-x-1 ait-border ait-rounded-xl ait-h-[120px]"><!----><div class="ait-flex ait-flex-wrap ait-gap-[6px] ait-max-w-full ait-place-self-end ait-self-center ait-min-w-[55px]"><ul class="ait-group-model-dropdown ait-hide ait-absolute ait-z-20 ait-width-full ait-w-max ait-min-w-[180px] ait-max-w-[250px] ait-bg-white ait-divide-y ait-divide-neutral-100 ait-rounded-lg ait-shadow dark:ait-bg-neutral-900 dark:ait-divide-neutral-700"><div class="ait-divide-y ait-divide-neutral-100 dark:ait-divide-neutral-700"><!----></div></ul></div><span class="ait-hide ait-my-2.5 ait-left-[-8px]"></span><textarea name="ask" class="ait-selection-container ait-ai-text-item ait-relative ait-w-full ait-h-full ait-p-1 ait-border-0 focus:ait-border-0 focus:ait-shadow-none ait-outline-none ait-resize-none dark:ait-bg-neutral-700 dark:ait-text-neutral-200" id="ask" rows="4" placeholder="Ask me anything..."></textarea><div class="ait-flex ait-w-full ait-justify-between ait-items-center ait-gap-2 ait-p-0.5"><div class="ait-flex ait-gap-1.5 ait-ml-1.5"><div class="ait-flex ait-items-center ait-justify-start ait-gap-3"><li data-v-2db9de5a="" class="ait-relative ait-block ait-popper-container"><button class="ait-cursor-pointer ait-p-1 ait-rounded-full ait-flex ait-items-center ait-justify-center hover:ait-bg-neutral-300 dark:hover:ait-bg-neutral-900 ait-border ait-transition-all"><span><svg class="ait-w-3.5 ait-h-3.5 ait-at_icon" width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.03 2.31c1.014 1.048 1.36 2.47 1.36 3.688 0 1.294-.638 2.546-1.408 3.393-.388.427-.833.778-1.286.983-.445.201-.977.293-1.459.053a1 1 0 0 1-.475-.528c-.797.772-1.983 1.077-3.082.528-1.136-.568-1.424-1.846-1.347-2.948.077-1.114.535-2.308 1.21-2.983.945-.945 2.12-1.329 3.126-1.055.555.152 1.01.49 1.313.973q.073-.171.142-.326a.525.525 0 0 1 .977.38c-.26.779-.765 2.243-1.208 3.38-.129.509-.203.95-.207 1.28a1.1 1.1 0 0 0 .047.37c.**************.53-.08.29-.132.622-.382.942-.733.644-.708 1.136-1.719 1.136-2.687 0-1.045-.3-2.168-1.066-2.958-.746-.771-2.02-1.314-4.164-1.046-1.827.228-3.242 1.494-3.944 3.13-.703 1.638-.661 3.581.357 5.108.611.917 1.893 1.64 3.475 1.855a7.17 7.17 0 0 0 4.911-1.117.525.525 0 1 1 .58.875 8.22 8.22 0 0 1-5.633 1.282c-1.778-.242-3.379-1.07-4.207-2.313C.405 8.947.382 6.621 1.202 4.71 2.023 2.797 3.72 1.235 5.981.952c2.38-.297 4.017.291 5.049 1.358M8.35 6.039v-.041c0-.937-.446-1.405-.957-1.544-.55-.15-1.354.031-2.107.784-.45.45-.84 1.377-.906 2.314-.066.951.209 1.655.77 1.936.824.412 1.826.025 2.287-.897.124-.247.282-.624.454-1.063l.017-.066c.122-.464.277-.95.443-1.423" fill="currentColor"></path></svg></span></button><div data-v-2db9de5a="" class="ait-absolute ait-z-[9999999] ait-top-0 ait-left-1/2 ait-transform ait--translate-x-1/2 ait-py-1 ait-px-2 ait-rounded-md ait-shadow-lg dark:ait-bg-neutral-100 ait-bg-neutral-800 ait-text-gray-900 dark:ait-text-gray-200 ait-translate-y-[-120%] ait:border-[1px] dark:ait-border-neutral-200 ait-border-neutral-300" style="display: none;"><div data-v-2db9de5a="" class="ait-text-xs ait-font-medium ait-text-gray-200 dark:ait-text-gray-800 ait-whitespace-nowrap ait-max-w-[200px] ait-w-full">Group Chat</div></div><div data-v-2db9de5a="" class="ait-hide ait-left-0 ait-top-0 ait-bottom-0 ait-left-0 ait-top-1/2 ait-left-1/2 ait-right-1/2 ait-bottom-1/2 ait-translate-x-[-100%] ait-translate-y-[-100%] ait-translate-x-[-120%] ait-translate-y-[-120%]"></div></li></div><div class="ait-flex ait-items-center ait-justify-start ait-gap-3"><label class="ait-relative ait-inline-flex ait-items-center ait-cursor-pointer"><input type="checkbox" class="ait-ai-web-access-checkbox ait-sr-only ait-peer" value="1"><div class="ait-w-7 ait-h-4 ait-bg-neutral-200 ait-rounded-full ait-peer peer-focus:ait-ring-3 dark:peer-focus:ait-ring-[var(--ait-link-color)] dark:ait-bg-neutral-700 peer-checked:after:ait-translate-x-full rtl:peer-checked:after:-ait-translate-x-full peer-checked:after:ait-border-white after:ait-content-[&#39;&#39;] after:ait-absolute after:ait-top-0.5 after:ait-start-[2px] after:ait-bg-white after:ait-border-neutral-300 after:ait-border after:ait-rounded-full after:ait-h-3 after:ait-w-3 after:ait-transition-all dark:ait-border-neutral-600 peer-checked:ait-bg-[var(--ait-link-color)]"></div><span class="ait-ms-1.5 ait-text-xs ait-font-medium ait-text-[var(--ait-link-color)] ait-opacity-60 ait-transition-all ait-select-none peer-checked:ait-opacity-100">Web Access</span></label></div></div><li data-v-2db9de5a="" class="ait-relative ait-block ait-popper-container"><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABkAAAAZCAYAAADE6YVjAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAz0lEQVR4nN3VoU7CURTH8Y9IsNAwSjDZfALGeAgak2IxWbXpE5jceIZ/hsRG+j8BFDpJosnirnO7bMY/jPNX+W3fdu++O7v3nMOxpokRhjiLkjwiZda4zeKDpvgh2bLCACeRki2LLAuVpEyJbrQkZWa4jpYkfOY7l7tIxjtKUuYDLzivIrnABG97yt7xhFbVqtro4Q6vmGNTUfZ97h4Ne+YKz1hWkPV/rZIOptFvMq7jdxV19EnxVzq+jJxdi8gpfPB98lDHZjzFTfSO92/zBcBszGtUUreMAAAAAElFTkSuQmCC" class="ait-w-5 ait-h-5 ait-opacity-80 ait-cursor-pointer dark:ait-invert" style="filter: invert(0.5) !important;"><div data-v-2db9de5a="" class="ait-absolute ait-z-[9999999] ait-top-0 ait-left-1/2 ait-transform ait--translate-x-1/2 ait-py-1 ait-px-2 ait-rounded-md ait-shadow-lg dark:ait-bg-neutral-100 ait-bg-neutral-800 ait-text-gray-900 dark:ait-text-gray-200 ait-translate-y-[-120%] ait:border-[1px] dark:ait-border-neutral-200 ait-border-neutral-300" style="display: none;"><div data-v-2db9de5a="" class="ait-text-xs ait-font-medium ait-text-gray-200 dark:ait-text-gray-800 ait-whitespace-nowrap ait-max-w-[200px] ait-w-full">Send</div></div><div data-v-2db9de5a="" class="ait-hide ait-left-0 ait-top-0 ait-bottom-0 ait-left-0 ait-top-1/2 ait-left-1/2 ait-right-1/2 ait-bottom-1/2 ait-translate-x-[-100%] ait-translate-y-[-100%] ait-translate-x-[-120%] ait-translate-y-[-120%]"></div></li></div></div></div></div></div><div class="ait-flex ait-relative ait-items-end ait-justify-between"><div class="ait-w-full ait-text-center ait-pr-3"><button type="button" data-ait-dropdown-toggle="ait-make-a-review" class="ait-flex-col ait-text-[11px] ait-text-[var(--ait-link-color)] hover:ait-text-neutral-700 dark:hover:ait-text-neutral-100">Make a Review &amp; Earn Credit <span class="ait-text-red-600 ait-text-[10px]">❤</span></button></div></div></div><!----><!----><!----><!----><!----><div class="ait-switchbar ait-w-[60px] ait-min-w-[60px] ait-bg-[var(--ait-switchbar-bg-color)] ait-py-2 ait-flex ait-flex-col ait-items-center ait-gap-4 ait-h-full ait-flex-grow ait-overflow-x-hidden" style="box-shadow: none;"><div><div class="ait-w-8 ait-h-8 ait-flex ait-items-center ait-justify-center ait-rounded-lg ait-cursor-pointer text-[var(--ait-secondary-text-color)] hover:bg-[var(--ait-switchbar-btn-hover-bg-color)] !w-[30px] !h-[30px]"><span><svg class="ait-w-4.5 ait-h-4.5 ait-collapse_sidebar_icon" aria-hide="true" focusable="false" role="img" viewBox="0 0 16 16" width="16" fill="currentColor" style="display: inline-block; user-select: none; vertical-align: text-bottom; overflow: visible;"> <path d="M6.823 7.823a.25.25 0 0 1 0 .354l-2.396 2.396A.25.25 0 0 1 4 10.396V5.604a.25.25 0 0 1 .427-.177Z"></path> <path d="M1.75 0h12.5C15.216 0 16 .784 16 1.75v12.5A1.75 1.75 0 0 1 14.25 16H1.75A1.75 1.75 0 0 1 0 14.25V1.75C0 .784.784 0 1.75 0ZM1.5 1.75v12.5c0 .138.112.25.25.25H9.5v-13H1.75a.25.25 0 0 0-.25.25ZM11 14.5h3.25a.25.25 0 0 0 .25-.25V1.75a.25.25 0 0 0-.25-.25H11Z"></path> </svg></span></div></div><div class="ait-flex ait-flex-col ait-gap-2 ait-justify-center"><div class="ait-switchbar-tab-item ait-rounded-lg ait-p-2 ait-cursor-pointer ait-arc-edge ait-flex ait-flex-col ait-items-center  active"><span><span><svg class="ait-w-5 ait-h-5 ait-chat_icon" viewBox="0 0 31 32" version="1.1" xmlns="http://www.w3.org/2000/svg"> <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" sketch:type="MSPage"> <g sketch:type="MSLayerGroup" transform="translate(-259.000000, -257.000000)" fill="currentColor"> <path d="M265.5,267 C266.329,267 267,267.672 267,268.5 C267,269.329 266.329,270 265.5,270 C264.671,270 264,269.329 264,268.5 C264,267.672 264.671,267 265.5,267 L265.5,267 Z M271.5,267 C272.329,267 273,267.672 273,268.5 C273,269.329 272.329,270 271.5,270 C270.671,270 270,269.329 270,268.5 C270,267.672 270.671,267 271.5,267 L271.5,267 Z M277.5,267 C278.329,267 279,267.672 279,268.5 C279,269.329 278.329,270 277.5,270 C276.671,270 276,269.329 276,268.5 C276,267.672 276.671,267 277.5,267 L277.5,267 Z M268.637,279.736 C269.414,279.863 271.181,280 272,280 C279.18,280 284,274.657 284,268.375 C284,262.093 277.977,257 272,257 C264.811,257 259,262.093 259,268.375 C259,272.015 260.387,275.104 263,277.329 L263,283 L268.637,279.736 L268.637,279.736 Z M285.949,266.139 L286,267 C286.008,267.817 286,267.742 286,268.5 C286,276.475 279.716,282 271,282 L268,282 C270.38,284.328 273.149,285.75 277,285.75 C277.819,285.75 278.618,285.676 279.395,285.549 L285,289 L285,283.329 C288.04,281.246 290,278.015 290,274.375 C290,271.131 288.439,268.211 285.949,266.139 L285.949,266.139 Z" id="comments" sketch:type="MSShapeGroup"></path> </g> </g> </svg></span></span><span class="ait-pt-2 ait-text-[10px]">Chat</span></div><div class="ait-switchbar-tab-item ait-rounded-lg ait-p-2 ait-cursor-pointer ait-arc-edge ait-flex ait-flex-col ait-items-center"><span><span><svg class="ait-w-5 ait-h-5 ait-ask_icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256"><rect width="256" height="256" fill="none"></rect><path fill="currentColor" d="M225.86,102.82c-3.77-3.94-7.67-8-9.14-11.57-1.36-3.27-1.44-8.69-1.52-13.94-.15-9.76-.31-20.82-8-28.51s-18.75-7.85-28.51-8c-5.25-.08-10.67-.16-13.94-1.52-3.56-1.47-7.63-5.37-11.57-9.14C146.28,23.51,138.44,16,128,16s-18.27,7.51-25.18,14.14c-3.94,3.77-8,7.67-11.57,9.14C88,40.64,82.56,40.72,77.31,40.8c-9.76.15-20.82.31-28.51,8S41,67.55,40.8,77.31c-.08,5.25-.16,10.67-1.52,13.94-1.47,3.56-5.37,7.63-9.14,11.57C23.51,109.72,16,117.56,16,128s7.51,18.27,14.14,25.18c3.77,3.94,7.67,8,9.14,11.57,1.36,3.27,1.44,8.69,1.52,13.94.15,9.76.31,20.82,8,28.51s18.75,7.85,28.51,8c5.25.08,10.67.16,13.94,1.52,3.56,1.47,7.63,5.37,11.57,9.14C109.72,232.49,117.56,240,128,240s18.27-7.51,25.18-14.14c3.94-3.77,8-7.67,11.57-9.14,3.27-1.36,8.69-1.44,13.94-1.52,9.76-.15,20.82-.31,28.51-8s7.85-18.75,8-28.51c.08-5.25.16-10.67,1.52-13.94,1.47-3.56,5.37-7.63,9.14-11.57C232.49,146.28,240,138.44,240,128S232.49,109.73,225.86,102.82ZM128,192a12,12,0,1,1,12-12A12,12,0,0,1,128,192Zm8-48.72V144a8,8,0,0,1-16,0v-8a8,8,0,0,1,8-8c13.23,0,24-9,24-20s-10.77-20-24-20-24,9-24,20v4a8,8,0,0,1-16,0v-4c0-19.85,17.94-36,40-36s40,16.15,40,36C168,125.38,154.24,139.93,136,143.28Z"></path></svg></span></span><span class="ait-pt-2 ait-text-[10px]">Ask</span></div><div class="ait-switchbar-tab-item ait-rounded-lg ait-p-2 ait-cursor-pointer ait-arc-edge ait-flex ait-flex-col ait-items-center"><span><svg class="svg-inline--fa fa-magnifying-glass ait-h-5 ait-w-5" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="magnifying-glass" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path class="" fill="currentColor" d="M416 208c0 45.9-14.9 88.3-40 122.7L502.6 457.4c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L330.7 376c-34.4 25.2-76.8 40-122.7 40C93.1 416 0 322.9 0 208S93.1 0 208 0S416 93.1 416 208zM208 352a144 144 0 1 0 0-288 144 144 0 1 0 0 288z"></path></svg></span><span class="ait-pt-2 ait-text-[10px]">Search</span></div><div class="ait-switchbar-tab-item ait-rounded-lg ait-p-2 ait-cursor-pointer ait-arc-edge ait-flex ait-flex-col ait-items-center"><span><svg class="svg-inline--fa fa-square-pen ait-h-5 ait-w-5" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="square-pen" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path class="" fill="currentColor" d="M64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32zM325.8 139.7l14.4 14.4c15.6 15.6 15.6 40.9 0 56.6l-21.4 21.4-71-71 21.4-21.4c15.6-15.6 40.9-15.6 56.6 0zM119.9 289L225.1 183.8l71 71L190.9 359.9c-4.1 4.1-9.2 7-14.9 8.4l-60.1 15c-5.5 1.4-11.2-.2-15.2-4.2s-5.6-9.7-4.2-15.2l15-60.1c1.4-5.6 4.3-10.8 8.4-14.9z"></path></svg></span><span class="ait-pt-2 ait-text-[10px]">Write</span></div><div class="ait-switchbar-tab-item ait-rounded-lg ait-p-2 ait-cursor-pointer ait-arc-edge ait-flex ait-flex-col ait-items-center"><span><svg class="svg-inline--fa fa-image ait-h-5 ait-w-5" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="image" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path class="" fill="currentColor" d="M0 96C0 60.7 28.7 32 64 32l384 0c35.3 0 64 28.7 64 64l0 320c0 35.3-28.7 64-64 64L64 480c-35.3 0-64-28.7-64-64L0 96zM323.8 202.5c-4.5-6.6-11.9-10.5-19.8-10.5s-15.4 3.9-19.8 10.5l-87 127.6L170.7 297c-4.6-5.7-11.5-9-18.7-9s-14.2 3.3-18.7 9l-64 80c-5.8 7.2-6.9 17.1-2.9 25.4s12.4 13.6 21.6 13.6l96 0 32 0 208 0c8.9 0 17.1-4.9 21.2-12.8s3.6-17.4-1.4-24.7l-120-176zM112 192a48 48 0 1 0 0-96 48 48 0 1 0 0 96z"></path></svg></span><span class="ait-pt-2 ait-text-[10px]">Image</span></div><div class="ait-switchbar-tab-item ait-rounded-lg ait-p-2 ait-cursor-pointer ait-arc-edge ait-flex ait-flex-col ait-items-center" data-ait-dropdown-toggle="ait-pdf-upload-modal"><span><svg class="ait-w-5 ait-h-5 ait-chat_pdf_icon" fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><path fill-rule="evenodd" clip-rule="evenodd" d="M3.79128 0.289062C2.90763 0.289062 2.19128 1.00541 2.19128 1.88906V2.33517C1.42585 2.45474 0.84021 3.11694 0.84021 3.91592V4.6181C0.84021 5.41709 1.42585 6.07929 2.19128 6.19886V13.4C2.19128 14.2837 2.90763 15 3.79128 15H12.6C13.4837 15 14.2 14.2837 14.2 13.4V1.88906C14.2 1.00541 13.4837 0.289062 12.6 0.289062H3.79128ZM3.39128 6.21811V13.4C3.39128 13.6209 3.57037 13.8 3.79128 13.8H12.6C12.8209 13.8 13 13.6209 13 13.4V1.88906C13 1.66815 12.8209 1.48906 12.6 1.48906H3.79128C3.57037 1.48906 3.39128 1.66815 3.39128 1.88906V2.31592H5.84458C6.72824 2.31592 7.44458 3.03226 7.44458 3.91592V4.6181C7.44458 5.50176 6.72824 6.21811 5.84458 6.21811H3.39128ZM2.04021 3.91592C2.04021 3.695 2.2193 3.51592 2.44021 3.51592H5.84458C6.0655 3.51592 6.24458 3.695 6.24458 3.91592V4.6181C6.24458 4.83902 6.0655 5.01811 5.84458 5.01811H2.44021C2.2193 5.01811 2.04021 4.83902 2.04021 4.6181V3.91592Z" fill="currentColor" fill-opacity="0.5"></path><path d="M11.2151 11.1661C10.6578 11.1247 10.1215 10.9178 9.68817 10.5452C8.84183 10.7314 8.03717 11.0006 7.23237 11.3317C6.59261 12.4699 5.99415 13.0492 5.47818 13.0492C5.37504 13.0492 5.2512 13.0284 5.1687 12.9664C4.94166 12.8629 4.81787 12.6353 4.81787 12.4078C4.81787 12.2215 4.85907 11.704 6.81975 10.8558C7.27352 10.028 7.62435 9.17948 7.91333 8.28976C7.6657 7.79314 7.12923 6.57219 7.50066 5.95153C7.62435 5.72383 7.87213 5.59969 8.14042 5.6204C8.34676 5.6204 8.55309 5.72387 8.67679 5.88942C8.94522 6.26196 8.92452 7.04815 8.5736 8.20704C8.90392 8.82784 9.3373 9.38643 9.85317 9.86254C10.2866 9.77972 10.7198 9.71756 11.1533 9.71756C12.1232 9.73822 12.2676 10.1937 12.2471 10.4624C12.2471 11.1661 11.5661 11.1661 11.2151 11.1661ZM5.43693 12.4491L5.49888 12.4281C5.78776 12.3248 6.01475 12.118 6.17989 11.8489C5.87026 11.9731 5.62262 12.1803 5.43693 12.4491ZM8.18162 6.2411H8.11957C8.09897 6.2411 8.05773 6.2411 8.03713 6.26191C7.95453 6.6136 8.01662 6.98614 8.16107 7.31722C8.28476 6.96543 8.28476 6.59284 8.18162 6.2411ZM8.32611 9.24164L8.30536 9.2831L8.28471 9.26235C8.09893 9.73817 7.89264 10.2144 7.66555 10.6694L7.70685 10.6488V10.6901C8.16107 10.5244 8.65619 10.3799 9.11031 10.2764L9.08951 10.2556H9.15146C8.84178 9.94502 8.553 9.59333 8.32611 9.24164ZM11.1326 10.3385C10.9469 10.3385 10.7819 10.3385 10.5961 10.3799C10.8024 10.4831 11.0087 10.5245 11.215 10.5453C11.3598 10.566 11.504 10.5453 11.6278 10.504C11.6278 10.4417 11.5453 10.3385 11.1326 10.3385Z" fill="currentColor"></path></svg></span><span class="ait-pt-2 ait-text-[10px]">ChatPDF</span></div><div class="ait-switchbar-tab-item ait-rounded-lg ait-p-2 ait-cursor-pointer ait-arc-edge ait-flex ait-flex-col ait-items-center" data-ait-dropdown-toggle="ait-image-vision-modal"><span><svg class="svg-inline--fa fa-eye ait-h-5 ait-w-5" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="eye" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512"><path class="" fill="currentColor" d="M288 32c-80.8 0-145.5 36.8-192.6 80.6C48.6 156 17.3 208 2.5 243.7c-3.3 7.9-3.3 16.7 0 24.6C17.3 304 48.6 356 95.4 399.4C142.5 443.2 207.2 480 288 480s145.5-36.8 192.6-80.6c46.8-43.5 78.1-95.4 93-131.1c3.3-7.9 3.3-16.7 0-24.6c-14.9-35.7-46.2-87.7-93-131.1C433.5 68.8 368.8 32 288 32zM144 256a144 144 0 1 1 288 0 144 144 0 1 1 -288 0zm144-64c0 35.3-28.7 64-64 64c-7.1 0-13.9-1.2-20.3-3.3c-5.5-1.8-11.9 1.6-11.7 7.4c.3 6.9 1.3 13.8 3.2 20.7c13.7 51.2 66.4 81.6 117.6 67.9s81.6-66.4 67.9-117.6c-11.1-41.5-47.8-69.4-88.6-71.1c-5.8-.2-9.2 6.1-7.4 11.7c2.1 6.4 3.3 13.2 3.3 20.3z"></path></svg></span><span class="ait-pt-2 ait-text-[10px]">Vision</span></div></div><div class="divider w-[70%] ait-mx-auto h-[1px] bg-[var(--ait-switchbar-btn-hover-bg-color)]"></div><div class="bottom-area ait-mt-auto ait-flex ait-flex-col ait-justify-center ait-items-center ait-gap-2 ait-mb-4"><div class="ait-flex ait-flex-col ait-w-full ait-gap-1 ait-cursor-pointer"><div class="ait-switchbar-tab-item ait-rounded-lg ait-p-1 ait-cursor-pointer ait-items-center ait-flex ait-flex-col"><span><svg class="ait-w-4 ait-h-4 ait-full_screen_icon" style="width: 1em; height: 1em;vertical-align: middle;fill: currentColor;overflow: hidden;" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"><path d="M237.248 192H352a32 32 0 1 0 0-64H160a32 32 0 0 0-32 32v192a32 32 0 1 0 64 0v-114.752l137.36 137.36a32 32 0 1 0 45.232-45.264L237.248 192zM832 237.248V352a32 32 0 1 0 64 0V160a32 32 0 0 0-32-32H672a32 32 0 1 0 0 64h114.752l-137.36 137.36a32 32 0 1 0 45.264 45.232L832 237.248zM237.248 832H352a32 32 0 1 1 0 64H160a32 32 0 0 1-32-32V672a32 32 0 1 1 64 0v114.752l137.36-137.36a32 32 0 1 1 45.232 45.264L237.248 832zM832 786.752V672a32 32 0 1 1 64 0v192a32 32 0 0 1-32 32H672a32 32 0 1 1 0-64h114.752l-137.36-137.36a32 32 0 1 1 45.264-45.232L832 786.752z"></path></svg></span><label class="ait-cursor-pointer" style="font-size: 8px;">Full Page</label></div></div><div class="ait-flex ait-flex-col ait-w-full ait-gap-1 ait-cursor-pointer"><div class="ait-switchbar-tab-item ait-rounded-lg ait-p-1 ait-items-center ait-flex ait-flex-col ait-cursor-pointer" data-ait-dropdown-toggle="ait-invite-friends"><span><svg class="ait-w-4 ait-h-4 ait-gift_icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" fill="none"><g clip-path="url(#clip0_11821_42642)"><path d="M2.18208 6.77174H11.8179M2.18208 6.77174C1.64991 6.77174 1.21851 6.34033 1.21851 5.80816V4.84458C1.21851 4.31241 1.64991 3.881 2.18208 3.881H11.8179C12.35 3.881 12.7814 4.31241 12.7814 4.84458V5.80816C12.7814 6.34033 12.35 6.77174 11.8179 6.77174M2.18208 6.77174L2.18208 11.5896C2.18208 12.1218 2.61349 12.5532 3.14566 12.5532H10.8543C11.3865 12.5532 11.8179 12.1218 11.8179 11.5896V6.77174M6.99998 3.881L9.31257 3.11014C9.67652 2.98882 9.96211 2.70323 10.0834 2.33927C10.401 1.38644 9.49454 0.479936 8.5417 0.797549C8.17775 0.918867 7.89216 1.20446 7.77084 1.56841L6.99998 3.881ZM6.99998 3.881L4.68739 3.11014C4.32344 2.98882 4.03784 2.70323 3.91653 2.33927C3.59891 1.38644 4.50541 0.479936 5.45825 0.797549C5.8222 0.918867 6.1078 1.20446 6.22911 1.56841L6.99998 3.881ZM6.99998 3.881V12.5532" stroke="currentColor" stroke-width="1.2"></path></g><defs><clippath id="clip0_11821_42642"><rect width="14" height="14" fill="white"></rect></clippath></defs></svg></span><label class="ait-cursor-pointer ait-text-center ait-text-balance" style="font-size: 8px;">Invite &amp;
            Earn</label></div></div><div class="ait-flex ait-flex-col ait-gap-1 ait-w-full ait-cursor-pointer"><div class="ait-switchbar-tab-item ait-rounded-lg ait-p-1 ait-cursor-pointer ait-items-center ait-flex ait-flex-col ait-min-h-[36px] ait-justify-center"><svg class="svg-inline--fa fa-envelope ait-h-4 ait-w-4" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="envelope" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path class="" fill="currentColor" d="M48 64C21.5 64 0 85.5 0 112c0 15.1 7.1 29.3 19.2 38.4L236.8 313.6c11.4 8.5 27 8.5 38.4 0L492.8 150.4c12.1-9.1 19.2-23.3 19.2-38.4c0-26.5-21.5-48-48-48L48 64zM0 176L0 384c0 35.3 28.7 64 64 64l384 0c35.3 0 64-28.7 64-64l0-208L294.4 339.2c-22.8 17.1-54 17.1-76.8 0L0 176z"></path></svg></div></div><div class="ait-flex ait-flex-col ait-gap-1 ait-w-full ait-cursor-pointer"><div class="ait-switchbar-tab-item ait-rounded-lg ait-p-1 ait-cursor-pointer ait-items-center ait-flex ait-flex-col ait-min-h-[36px] ait-justify-center aitopia-options"><svg class="svg-inline--fa fa-gear ait-h-4 ait-w-4" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="gear" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path class="" fill="currentColor" d="M495.9 166.6c3.2 8.7 .5 18.4-6.4 24.6l-43.3 39.4c1.1 8.3 1.7 16.8 1.7 25.4s-.6 17.1-1.7 25.4l43.3 39.4c6.9 6.2 9.6 15.9 6.4 24.6c-4.4 11.9-9.7 23.3-15.8 34.3l-4.7 8.1c-6.6 11-14 21.4-22.1 31.2c-5.9 7.2-15.7 9.6-24.5 6.8l-55.7-17.7c-13.4 10.3-28.2 18.9-44 25.4l-12.5 57.1c-2 9.1-9 16.3-18.2 17.8c-13.8 2.3-28 3.5-42.5 3.5s-28.7-1.2-42.5-3.5c-9.2-1.5-16.2-8.7-18.2-17.8l-12.5-57.1c-15.8-6.5-30.6-15.1-44-25.4L83.1 425.9c-8.8 2.8-18.6 .3-24.5-6.8c-8.1-9.8-15.5-20.2-22.1-31.2l-4.7-8.1c-6.1-11-11.4-22.4-15.8-34.3c-3.2-8.7-.5-18.4 6.4-24.6l43.3-39.4C64.6 273.1 64 264.6 64 256s.6-17.1 1.7-25.4L22.4 191.2c-6.9-6.2-9.6-15.9-6.4-24.6c4.4-11.9 9.7-23.3 15.8-34.3l4.7-8.1c6.6-11 14-21.4 22.1-31.2c5.9-7.2 15.7-9.6 24.5-6.8l55.7 17.7c13.4-10.3 28.2-18.9 44-25.4l12.5-57.1c2-9.1 9-16.3 18.2-17.8C227.3 1.2 241.5 0 256 0s28.7 1.2 42.5 3.5c9.2 1.5 16.2 8.7 18.2 17.8l12.5 57.1c15.8 6.5 30.6 15.1 44 25.4l55.7-17.7c8.8-2.8 18.6-.3 24.5 6.8c8.1 9.8 15.5 20.2 22.1 31.2l4.7 8.1c6.1 11 11.4 22.4 15.8 34.3zM256 336a80 80 0 1 0 0-160 80 80 0 1 0 0 160z"></path></svg></div></div><div class="ait-flex ait-flex-col ait-gap-1 ait-w-full ait-cursor-pointer"><div data-ait-dropdown-toggle="ait-user-dropdown" data-placement="top-end" class="ait-switchbar-tab-item ait-rounded-lg ait-p-1 ait-cursor-pointer ait-items-center ait-flex ait-flex-col ait-min-h-[36px] ait-justify-center"><svg class="svg-inline--fa fa-user ait-h-4 ait-w-4" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="user" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path class="" fill="currentColor" d="M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-45.7 48C79.8 304 0 383.8 0 482.3C0 498.7 13.3 512 29.7 512l388.6 0c16.4 0 29.7-13.3 29.7-29.7C448 383.8 368.2 304 269.7 304l-91.4 0z"></path></svg></div></div></div></div><div id="ait-user-dropdown" class="ait-stroke ait-z-20 ait-width-full ait-hide ait-w-max ait-min-w-[180px] ait-max-w-[250px] ait-bg-white ait-divide-y ait-divide-neutral-100 ait-rounded-lg ait-shadow dark:ait-bg-neutral-900 dark:ait-divide-neutral-700"><div class="ait-p-2 ait-flex ait-flex-col ait-gap-2 ait-min-w-[240px]"><div class="ait-flex ait-items-center ait-cursor-pointer ait-rounded-lg ait-text-white ait-bg-green-700 hover:ait-bg-green-800 ait-px-3 ait-py-1 ait-text-center dark:ait-bg-green-600 dark:hover:ait-bg-green-700"><div class="ait-flex ait-cursor-pointer ait-h-8 ait-w-8 ait-flex-center ait-justify-center ait-items-center"><div class="ait-relative ait-flex-shrink-0"><svg class="svg-inline--fa fa-user ait-h-4 ait-w-4" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="user" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path class="" fill="currentColor" d="M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-45.7 48C79.8 304 0 383.8 0 482.3C0 498.7 13.3 512 29.7 512l388.6 0c16.4 0 29.7-13.3 29.7-29.7C448 383.8 368.2 304 269.7 304l-91.4 0z"></path></svg></div></div><div class="ait-flex ait-flex-col ait-ml-1"><div class="ait-flex ait-items-center"><div class="ait-text-sm ait-leading-5 ait-truncate ait-w-full ait-text-white aitopia-login-prefix">Log in as a user</div></div><!----></div></div><div><!----><!----><!----><div class="ait-flex ait-flex-align ait-gap-3 hover:ait-bg-[var(--ait-tab-menu-active-bg-color)] ait-rounded-lg ait-py-2 ait-px-3 ait-cursor-pointer"><div class="ait-size-[16px] ait-flex-center ait-items-center"><svg class="svg-inline--fa fa-envelope ait-h-4 ait-w-4" aria-hidden="true" focusable="false" data-prefix="far" data-icon="envelope" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path class="" fill="currentColor" d="M64 112c-8.8 0-16 7.2-16 16l0 22.1L220.5 291.7c20.7 17 50.4 17 71.1 0L464 150.1l0-22.1c0-8.8-7.2-16-16-16L64 112zM48 212.2L48 384c0 8.8 7.2 16 16 16l384 0c8.8 0 16-7.2 16-16l0-171.8L322 328.8c-38.4 31.5-93.7 31.5-132 0L48 212.2zM0 128C0 92.7 28.7 64 64 64l384 0c35.3 0 64 28.7 64 64l0 256c0 35.3-28.7 64-64 64L64 448c-35.3 0-64-28.7-64-64L0 128z"></path></svg></div><div>Feedback</div></div><!----><!----></div></div></div></div><div><!----></div><div id="ait-invite-friends" class="ait-hide"><div tabindex="-1" aria-hide="true" class="ait-fixed !ait-z-[999999] ait-modal ait-m-0 ait-bg-[rgba(0,0,0,.5)] ait-top-0 ait-left-0 ait-bottom-0 ait-right-0 ait-w-full ait-p-4 ait-overflow-x-hidden ait-overflow-y-auto lg:ait-inset-0 h-[calc(100%)] ait-max-h-full ait-justify-center ait-flex ait-items-center" data-modal-hide=""><div class="ait-relative ait-w-full lg:ait-max-w-screen-xl ait-max-h-full"><div class="ait-relative ait-bg-white ait-rounded-lg ait-shadow dark:ait-bg-neutral-800"><div class="ait-px-6 ait-py-4 ait-border-b ait-rounded-t dark:ait-border-neutral-600"><h3 class="ait-text-base ait-font-semibold ait-text-neutral-900 lg:ait-text-lg dark:ait-text-white ait-text-center">Invite Friends &amp; Earn Credits</h3><button type="button" class="ait-absolute ait-top-3 ait-right-2.5 ait-text-neutral-400 ait-bg-transparent hover:ait-bg-neutral-200 hover:ait-text-neutral-900 ait-rounded-lg ait-text-sm ait-w-8 ait-h-8 ait-ml-auto ait-inline-flex ait-justify-center ait-items-center dark:hover:ait-bg-neutral-600 dark:hover:ait-text-white" data-modal-hide=""><svg class="ait-w-3 ait-h-3" aria-hide="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"></path></svg><span class="ait-sr-only">Close modal</span></button></div><div class="ait-p-4 ait-w-full ait-text-center ait-flex ait-flex-col ait-items-center ait-gap-3"><div class="ait-w-full ait-text-start ait-p-[10px]"><span>Earn <span class="text-primary-600 font-bold">5 Fast credits &amp; 1 Advanced</span> credit for both of you and your friend.</span><span class="ait-font-bold">Earn more when you refer more!</span><div class="ait-w-full ait-px-[40px] ait-my-[30px]"><div class="ait-flex ait-items-center ait-gap-[12px]"><svg width="20" height="22" viewBox="0 0 43 40" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M22.8423 3.0436C24.885 1.06876 27.6211 -0.0242977 30.4616 0.000409932C33.302 0.0251175 36.0188 1.1656 38.0268 3.17566C40.0348 5.18565 41.1735 7.90432 41.1981 10.746C41.2228 13.5877 40.1315 16.3257 38.1588 18.3703L38.1371 18.3928L32.7165 23.8185C32.7164 23.8186 32.7165 23.8184 32.7165 23.8185C31.6186 24.9178 30.297 25.7683 28.8418 26.3117C27.3864 26.8551 25.8313 27.0789 24.2819 26.9678C22.7324 26.8568 21.2251 26.4134 19.862 25.668C18.499 24.9225 17.3122 23.8924 16.382 22.6477C15.7881 21.8528 15.9509 20.7269 16.7458 20.133C17.5407 19.539 18.6665 19.7019 19.2605 20.4967C19.8823 21.3288 20.6755 22.0172 21.5862 22.5153C22.4969 23.0133 23.5039 23.3095 24.5388 23.3837C25.5737 23.4579 26.6125 23.3084 27.5848 22.9454C28.557 22.5823 29.44 22.0142 30.1738 21.2793L35.5831 15.8647C36.8952 14.4992 37.6214 12.673 37.6049 10.7772C37.5884 8.87653 36.8268 7.05875 35.4847 5.71527C34.1426 4.37185 32.3275 3.61014 30.4303 3.59364C28.5373 3.57717 26.713 4.304 25.3488 5.61846L22.2507 8.70169C21.5473 9.40164 20.4098 9.3989 19.7098 8.69556C19.0098 7.99222 19.0126 6.85462 19.7159 6.15467L22.8423 3.0436ZM14.0283 13.2153C15.4836 12.6719 17.0388 12.4481 18.5882 12.5592C20.1376 12.6703 21.645 13.1136 23.008 13.8591C24.3711 14.6045 25.5579 15.6346 26.488 16.8793C27.082 17.6742 26.9191 18.8001 26.1243 19.3941C25.3294 19.988 24.2035 19.8252 23.6095 19.0303C22.9878 18.1982 22.1946 17.5098 21.2838 17.0117C20.3731 16.5137 19.3662 16.2176 18.3312 16.1434C17.2963 16.0692 16.2575 16.2186 15.2853 16.5817C14.3131 16.9447 13.43 17.5129 12.6962 18.2477L7.28698 23.6624C5.97482 25.0279 5.24869 26.8541 5.26515 28.7498C5.28165 30.6505 6.04327 32.4683 7.3854 33.8118C8.72747 35.1552 10.5426 35.9169 12.4397 35.9334C14.3319 35.9499 16.1554 35.2237 17.5195 33.9103L20.5976 30.829C21.2989 30.127 22.4365 30.1265 23.1385 30.8278C23.8405 31.5291 23.8411 32.6667 23.1398 33.3687L20.0279 36.4836C17.9853 38.4585 15.2489 39.5513 12.4085 39.5266C9.56802 39.5019 6.85126 38.3614 4.84322 36.3514C2.83525 34.3414 1.69659 31.6227 1.67192 28.781C1.64725 25.9393 2.73854 23.2013 4.7113 21.1567L4.73298 21.1342L10.1536 15.7085C10.1537 15.7085 10.1535 15.7086 10.1536 15.7085C11.2515 14.6092 12.573 13.7587 14.0283 13.2153Z" fill="white"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M22.8423 3.0436C24.885 1.06876 27.6211 -0.0242977 30.4616 0.000409932C33.302 0.0251175 36.0188 1.1656 38.0268 3.17566C40.0348 5.18565 41.1735 7.90432 41.1981 10.746C41.2228 13.5877 40.1315 16.3257 38.1588 18.3703L38.1371 18.3928L32.7165 23.8185C32.7164 23.8186 32.7165 23.8184 32.7165 23.8185C31.6186 24.9178 30.297 25.7683 28.8418 26.3117C27.3864 26.8551 25.8313 27.0789 24.2819 26.9678C22.7324 26.8568 21.2251 26.4134 19.862 25.668C18.499 24.9225 17.3122 23.8924 16.382 22.6477C15.7881 21.8528 15.9509 20.7269 16.7458 20.133C17.5407 19.539 18.6665 19.7019 19.2605 20.4967C19.8823 21.3288 20.6755 22.0172 21.5862 22.5153C22.4969 23.0133 23.5039 23.3095 24.5388 23.3837C25.5737 23.4579 26.6125 23.3084 27.5848 22.9454C28.557 22.5823 29.44 22.0142 30.1738 21.2793L35.5831 15.8647C36.8952 14.4992 37.6214 12.673 37.6049 10.7772C37.5884 8.87653 36.8268 7.05875 35.4847 5.71527C34.1426 4.37185 32.3275 3.61014 30.4303 3.59364C28.5373 3.57717 26.713 4.304 25.3488 5.61846L22.2507 8.70169C21.5473 9.40164 20.4098 9.3989 19.7098 8.69556C19.0098 7.99222 19.0126 6.85462 19.7159 6.15467L22.8423 3.0436ZM14.0283 13.2153C15.4836 12.6719 17.0388 12.4481 18.5882 12.5592C20.1376 12.6703 21.645 13.1136 23.008 13.8591C24.3711 14.6045 25.5579 15.6346 26.488 16.8793C27.082 17.6742 26.9191 18.8001 26.1243 19.3941C25.3294 19.988 24.2035 19.8252 23.6095 19.0303C22.9878 18.1982 22.1946 17.5098 21.2838 17.0117C20.3731 16.5137 19.3662 16.2176 18.3312 16.1434C17.2963 16.0692 16.2575 16.2186 15.2853 16.5817C14.3131 16.9447 13.43 17.5129 12.6962 18.2477L7.28698 23.6624C5.97482 25.0279 5.24869 26.8541 5.26515 28.7498C5.28165 30.6505 6.04327 32.4683 7.3854 33.8118C8.72747 35.1552 10.5426 35.9169 12.4397 35.9334C14.3319 35.9499 16.1554 35.2237 17.5195 33.9103L20.5976 30.829C21.2989 30.127 22.4365 30.1265 23.1385 30.8278C23.8405 31.5291 23.8411 32.6667 23.1398 33.3687L20.0279 36.4836C17.9853 38.4585 15.2489 39.5513 12.4085 39.5266C9.56802 39.5019 6.85126 38.3614 4.84322 36.3514C2.83525 34.3414 1.69659 31.6227 1.67192 28.781C1.64725 25.9393 2.73854 23.2013 4.7113 21.1567L4.73298 21.1342L10.1536 15.7085C10.1537 15.7085 10.1535 15.7086 10.1536 15.7085C11.2515 14.6092 12.573 13.7587 14.0283 13.2153Z" fill="url(#paint0_linear_18047_3202)"></path><defs><lineargradient id="paint0_linear_18047_3202" x1="10.1416" y1="17.5048" x2="25.9524" y2="40.0917" gradientUnits="userSpaceOnUse"><stop stop-color="rgba(1, 167, 125, .9)"></stop><stop offset="1" stop-color="#60a5fa"></stop></lineargradient></defs></svg><span class="ait-text-[14px]">Refer your friend with an Invitation link</span></div><svg class="svg-inline--fa fa-arrow-down ait-w-[11px] ait-mx-[4px] ait-mt-[4px] ait-mb-[10px]" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="arrow-down" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512"><path class="" fill="currentColor" d="M169.4 470.6c12.5 12.5 32.8 12.5 45.3 0l160-160c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L224 370.8 224 64c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 306.7L54.6 265.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l160 160z"></path></svg><div class="ait-flex ait-items-center ait-gap-[12px]"><svg width="18" height="19" viewBox="0 0 38 39" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.8795 10.3401C11.8795 6.79968 14.7411 3.84276 18.3761 3.84276C22.011 3.84276 24.8727 6.79968 24.8727 10.3401C24.8727 13.8804 22.011 16.8374 18.3761 16.8374C18.1848 16.8374 18.0003 16.8673 17.8267 16.9229C15.64 17.0126 13.4838 17.502 11.4584 18.3704C9.20642 19.336 7.16019 20.7513 5.43658 22.5354C3.71298 24.3196 2.34573 26.4377 1.41292 28.7689C0.480111 31.1 0 33.5985 0 36.1217C0 37.1828 0.831034 38.043 1.85617 38.043H35.2672C36.2923 38.043 37.1233 37.1828 37.1233 36.1217C37.1233 33.5985 36.6432 31.1 35.7104 28.7689C34.7776 26.4377 33.4104 24.3196 31.6867 22.5354C30.9619 21.7851 29.7866 21.7851 29.0617 22.5354C28.3368 23.2858 28.3368 24.5023 29.0617 25.2527C30.4406 26.68 31.5344 28.3745 32.2807 30.2394C32.7889 31.5096 33.1292 32.8419 33.2945 34.2003L3.8288 34.2003C3.99413 32.8419 4.3344 31.5096 4.84267 30.2394C5.58892 28.3745 6.68271 26.68 8.0616 25.2527C9.44049 23.8254 11.0775 22.6931 12.8791 21.9207C14.6807 21.1482 16.6116 20.7506 18.5617 20.7506C18.7692 20.7506 18.9688 20.7154 19.1551 20.6503C24.3899 20.2483 28.585 15.8289 28.585 10.3401C28.585 4.58142 23.9673 0 18.3761 0C12.7848 0 8.16717 4.58142 8.16717 10.3401C8.16717 11.4012 8.9982 12.2614 10.0233 12.2614C11.0485 12.2614 11.8795 11.4012 11.8795 10.3401Z" fill="white"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M11.8795 10.3401C11.8795 6.79968 14.7411 3.84276 18.3761 3.84276C22.011 3.84276 24.8727 6.79968 24.8727 10.3401C24.8727 13.8804 22.011 16.8374 18.3761 16.8374C18.1848 16.8374 18.0003 16.8673 17.8267 16.9229C15.64 17.0126 13.4838 17.502 11.4584 18.3704C9.20642 19.336 7.16019 20.7513 5.43658 22.5354C3.71298 24.3196 2.34573 26.4377 1.41292 28.7689C0.480111 31.1 0 33.5985 0 36.1217C0 37.1828 0.831034 38.043 1.85617 38.043H35.2672C36.2923 38.043 37.1233 37.1828 37.1233 36.1217C37.1233 33.5985 36.6432 31.1 35.7104 28.7689C34.7776 26.4377 33.4104 24.3196 31.6867 22.5354C30.9619 21.7851 29.7866 21.7851 29.0617 22.5354C28.3368 23.2858 28.3368 24.5023 29.0617 25.2527C30.4406 26.68 31.5344 28.3745 32.2807 30.2394C32.7889 31.5096 33.1292 32.8419 33.2945 34.2003L3.8288 34.2003C3.99413 32.8419 4.3344 31.5096 4.84267 30.2394C5.58892 28.3745 6.68271 26.68 8.0616 25.2527C9.44049 23.8254 11.0775 22.6931 12.8791 21.9207C14.6807 21.1482 16.6116 20.7506 18.5617 20.7506C18.7692 20.7506 18.9688 20.7154 19.1551 20.6503C24.3899 20.2483 28.585 15.8289 28.585 10.3401C28.585 4.58142 23.9673 0 18.3761 0C12.7848 0 8.16717 4.58142 8.16717 10.3401C8.16717 11.4012 8.9982 12.2614 10.0233 12.2614C11.0485 12.2614 11.8795 11.4012 11.8795 10.3401Z" fill="url(#paint0_linear_18047_3240)"></path><defs><lineargradient id="paint0_linear_18047_3240" x1="7.955" y1="16.8477" x2="23.2962" y2="38.2338" gradientUnits="userSpaceOnUse"><stop stop-color="rgba(1, 167, 125, .9)"></stop><stop offset="1" stop-color="#60a5fa"></stop></lineargradient></defs></svg><span class="ait-text-[14px]">Your friends sign up</span></div><svg class="svg-inline--fa fa-arrow-down ait-w-[11px] ait-mx-[4px] ait-mt-[10px] ait-mb-[3px]" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="arrow-down" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512"><path class="" fill="currentColor" d="M169.4 470.6c12.5 12.5 32.8 12.5 45.3 0l160-160c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L224 370.8 224 64c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 306.7L54.6 265.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l160 160z"></path></svg><div class="ait-flex ait-items-center ait-gap-[12px]"><svg width="25" height="25" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg" style="max-width: 25px;"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.1524 6.83074C11.1524 3.6117 13.7641 1 16.9831 1C20.2021 1 22.8138 3.6117 22.8138 6.83074V7.76822H27.9815C30.7352 7.76822 32.9662 9.99923 32.9662 12.7529V17.9206H33.9037C37.1227 17.9206 39.7344 20.5323 39.7344 23.7514C39.7344 26.9704 37.1227 29.5821 33.9037 29.5821H32.9662V34.7497C32.9662 37.5034 30.7352 39.7344 27.9815 39.7344H19.9511V35.5957C19.9511 33.9586 18.6203 32.6278 16.9831 32.6278C15.3459 32.6278 14.0151 33.9586 14.0151 35.5957V39.7344H5.98473C3.23101 39.7344 1 37.5034 1 34.7497L1.01059 26.7193H5.13867C6.77585 26.7193 8.10663 25.3885 8.10663 23.7513C8.10663 22.1141 6.77585 20.7833 5.13867 20.7833H1.00633L1.01692 12.7529C1.01692 12.7526 1.01692 12.7522 1.01692 12.7518C1.01755 10.0091 3.22096 7.76822 5.98473 7.76822H11.1524V6.83074ZM16.9831 4.20119C15.532 4.20119 14.3535 5.37967 14.3535 6.83074V10.9694H5.98473C5.00942 10.9694 4.21811 11.7567 4.21811 12.7529L4.21174 17.5822H5.13867C8.54382 17.5822 11.3078 20.3462 11.3078 23.7513C11.3078 27.1565 8.54382 29.9205 5.13867 29.9205H4.20756L4.20119 34.7497C4.20119 34.75 4.20119 34.7503 4.20119 34.7506C4.20168 35.7359 4.99928 36.5332 5.98473 36.5332H10.8139V35.5957C10.8139 32.1906 13.5779 29.4266 16.9831 29.4266C20.3882 29.4266 23.1522 32.1906 23.1522 35.5957V36.5332H27.9815C28.9672 36.5332 29.765 35.7354 29.765 34.7497V26.3809H33.9037C35.3547 26.3809 36.5332 25.2024 36.5332 23.7514C36.5332 22.3003 35.3547 21.1218 33.9037 21.1218H29.765V12.7529C29.765 11.7672 28.9672 10.9694 27.9815 10.9694H19.6126V6.83074C19.6126 5.37967 18.4342 4.20119 16.9831 4.20119Z" fill="white"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M11.1524 6.83074C11.1524 3.6117 13.7641 1 16.9831 1C20.2021 1 22.8138 3.6117 22.8138 6.83074V7.76822H27.9815C30.7352 7.76822 32.9662 9.99923 32.9662 12.7529V17.9206H33.9037C37.1227 17.9206 39.7344 20.5323 39.7344 23.7514C39.7344 26.9704 37.1227 29.5821 33.9037 29.5821H32.9662V34.7497C32.9662 37.5034 30.7352 39.7344 27.9815 39.7344H19.9511V35.5957C19.9511 33.9586 18.6203 32.6278 16.9831 32.6278C15.3459 32.6278 14.0151 33.9586 14.0151 35.5957V39.7344H5.98473C3.23101 39.7344 1 37.5034 1 34.7497L1.01059 26.7193H5.13867C6.77585 26.7193 8.10663 25.3885 8.10663 23.7513C8.10663 22.1141 6.77585 20.7833 5.13867 20.7833H1.00633L1.01692 12.7529C1.01692 12.7526 1.01692 12.7522 1.01692 12.7518C1.01755 10.0091 3.22096 7.76822 5.98473 7.76822H11.1524V6.83074ZM16.9831 4.20119C15.532 4.20119 14.3535 5.37967 14.3535 6.83074V10.9694H5.98473C5.00942 10.9694 4.21811 11.7567 4.21811 12.7529L4.21174 17.5822H5.13867C8.54382 17.5822 11.3078 20.3462 11.3078 23.7513C11.3078 27.1565 8.54382 29.9205 5.13867 29.9205H4.20756L4.20119 34.7497C4.20119 34.75 4.20119 34.7503 4.20119 34.7506C4.20168 35.7359 4.99928 36.5332 5.98473 36.5332H10.8139V35.5957C10.8139 32.1906 13.5779 29.4266 16.9831 29.4266C20.3882 29.4266 23.1522 32.1906 23.1522 35.5957V36.5332H27.9815C28.9672 36.5332 29.765 35.7354 29.765 34.7497V26.3809H33.9037C35.3547 26.3809 36.5332 25.2024 36.5332 23.7514C36.5332 22.3003 35.3547 21.1218 33.9037 21.1218H29.765V12.7529C29.765 11.7672 28.9672 10.9694 27.9815 10.9694H19.6126V6.83074C19.6126 5.37967 18.4342 4.20119 16.9831 4.20119Z" fill="url(#paint0_linear_18267_3259)"></path><defs><lineargradient id="paint0_linear_18267_3259" x1="9.30023" y1="18.1538" x2="24.794" y2="40.2877" gradientUnits="userSpaceOnUse"><stop stop-color="rgba(1, 167, 125, .9)"></stop><stop offset="1" stop-color="#60a5fa"></stop></lineargradient></defs></svg><span class="ait-text-[14px]">They install extension &amp; login. You both earn credits!</span></div></div><div class="ait-copy-link-wrapper ait-flex ait-items-center ait-justify-between ait-mt-[10px] ait-cursor-not-allowed"><span class="ait-overflow-hidden ait-w-5/12 ait-whitespace-nowrap ait-text-ellipsis ait-px-[18px] ait-opacity-50"></span><button type="button" class="ait-w-7/12 ait-h-[36px] ait-rounded-full hover:ait-opacity-90 active:ait-opacity-80 ait-text-white ait-bg-gradient-to-r ait-from-[#01a77d] ait-to-[#60a5fa]" style="text-shadow: rgba(0, 0, 0, 0.3) 1px 1px 1px;"><span class="ait-text-white">Copy Invitation Link</span></button></div><div class="ait-mt-[18px] ait-text-[--ait-tab-menu-active-bg-color] ait-cursor-pointer ait-text-center"><span class="ait-cursor-[pointer]">Check Invitation Records</span></div></div></div><!----></div></div></div></div><div class="ai-el-resize"></div></div></div><div id="aitopia-sidebar-opener" class="ait-cursor-pointer ait-show" style="bottom: 87px; right: 0px;"><div class="ai-opener-close"><svg class="svg-inline--fa fa-circle-xmark ait-w-4 ait-h-4" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="circle-xmark" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path class="" fill="currentColor" d="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM175 175c9.4-9.4 24.6-9.4 33.9 0l47 47 47-47c9.4-9.4 24.6-9.4 33.9 0s9.4 24.6 0 33.9l-47 47 47 47c9.4 9.4 9.4 24.6 0 33.9s-24.6 9.4-33.9 0l-47-47-47 47c-9.4 9.4-24.6 9.4-33.9 0s-9.4-24.6 0-33.9l47-47-47-47c-9.4-9.4-9.4-24.6 0-33.9z"></path></svg></div><div class="opener-logo"><span><svg class="ait-w-[45px] ait-h-[45px] ait-logo" xmlns="http://www.w3.org/2000/svg" baseProfile="tiny" viewBox="0 0 125 121.7" overflow="visible"><circle fill="#01a77d" cx="63.4" cy="61.2" r="57.8"></circle><g fill="#fff"><path d="M46.9 60.5h-.4c-1.9-.2-3.3-1.9-3.1-3.8.6-6.3 4.5-38 17.3-41.2 8.2-2 14.4 7.5 16.5 10.6 1.1 1.6.6 3.8-1 4.9s-3.8.6-4.9-1c-3.5-5.3-6.9-8.2-9-7.7-4.2 1-10 14.8-12 35.1-.2 1.7-1.7 3.1-3.4 3.1zm51.9-4.9c-.5 0-1-.1-1.4-.3-1.8-.8-2.6-2.9-1.8-4.6 2.6-5.8 3.2-10.2 1.7-11.7-3.1-3-17.8-.6-36.1 8.6-1.7.9-3.8.2-4.7-1.6-.9-1.7-.2-3.8 1.6-4.7.3-.2 8.3-4.1 17.4-7.1 13.4-4.5 22.2-4.5 26.7-.2 6.1 5.8 1.4 16.2-.1 19.6-.7 1.3-2 2-3.3 2zM88.2 89.5c-1.8 0-3.3-1.3-3.5-3.1-.2-1.9 1.1-3.7 3.1-3.9 6.3-.7 10.4-2.5 10.9-4.6 1-4.2-8.7-15.6-25.9-26.6-1.6-1-2.1-3.2-1.1-4.8s3.2-2.1 4.8-1.1c.3.2 7.8 5 15.1 11.3 10.7 9.3 15.3 16.7 13.9 22.8-1.9 8.2-13.2 9.5-16.9 10h-.4zm-25.9 18.1c-7.2 0-12.4-8.3-14.2-11.2-1-1.6-.5-3.8 1.1-4.8s3.8-.5 4.8 1.1c3.4 5.4 6.7 8.3 8.8 7.9 4.2-.9 10.3-14.5 12.9-34.8.2-1.9 2-3.3 3.9-3 1.9.2 3.3 2 3 3.9-.8 6.3-5.4 37.9-18.4 40.7-.5.1-1.2.2-1.9.2zm-25-18.1c-5.5 0-9.4-1.3-11.8-4-5.6-6.3-.1-16.3 1.6-19.5.9-1.7 3.1-2.3 4.7-1.4 1.7.9 2.3 3.1 1.4 4.7-3.1 5.6-4 9.9-2.6 11.5 2.9 3.2 17.7 1.9 36.7-5.7a3.57 3.57 0 0 1 4.6 1.9 3.57 3.57 0 0 1-1.9 4.6c-.3.1-8.6 3.5-17.9 5.8-5.9 1.4-10.8 2.1-14.8 2.1zm16.5-12.6c-.6 0-1.2-.2-1.8-.5-.3-.2-7.9-4.8-15.4-10.8-11-8.9-15.8-16.2-14.6-22.3 1.6-8.2 12.9-9.9 16.6-10.5 1.9-.3 3.7 1 4 2.9s-1 3.7-2.9 4c-6.3.9-10.3 2.8-10.7 4.9-1 4.3 9 15.3 26.6 25.8 1.7 1 2.2 3.1 1.2 4.8-.7 1.1-1.8 1.7-3 1.7z"></path><circle cx="63.8" cy="60.5" r="8.4"></circle></g></svg></span></div></div><div style="display: none;"><div class="ait-mb-5 ait-max-w-[372px] ait-min-w-[360px]" id="aitopia-search-container"><div class="ait-bg-neutral-100 dark:ait-bg-neutral-900 dark:ait-text-gray-200 ait-rounded-lg ait-px-4 ait-py-3 ait-border ait-border-gray-50 dark:ait-border-neutral-700"><div class="ait-flex"><div class="ait-w-11/12 ait-flex ait-gap-2 xl:ait-gap-3 ait-items-center"><div class="ait-logo ait-flex ait-w-7/12 ait-place-items-center"><span><svg class="ait-w-7 ait-flex-initial ait-logo" xmlns="http://www.w3.org/2000/svg" baseProfile="tiny" viewBox="0 0 125 121.7" overflow="visible"><circle fill="#01a77d" cx="63.4" cy="61.2" r="57.8"></circle><g fill="#fff"><path d="M46.9 60.5h-.4c-1.9-.2-3.3-1.9-3.1-3.8.6-6.3 4.5-38 17.3-41.2 8.2-2 14.4 7.5 16.5 10.6 1.1 1.6.6 3.8-1 4.9s-3.8.6-4.9-1c-3.5-5.3-6.9-8.2-9-7.7-4.2 1-10 14.8-12 35.1-.2 1.7-1.7 3.1-3.4 3.1zm51.9-4.9c-.5 0-1-.1-1.4-.3-1.8-.8-2.6-2.9-1.8-4.6 2.6-5.8 3.2-10.2 1.7-11.7-3.1-3-17.8-.6-36.1 8.6-1.7.9-3.8.2-4.7-1.6-.9-1.7-.2-3.8 1.6-4.7.3-.2 8.3-4.1 17.4-7.1 13.4-4.5 22.2-4.5 26.7-.2 6.1 5.8 1.4 16.2-.1 19.6-.7 1.3-2 2-3.3 2zM88.2 89.5c-1.8 0-3.3-1.3-3.5-3.1-.2-1.9 1.1-3.7 3.1-3.9 6.3-.7 10.4-2.5 10.9-4.6 1-4.2-8.7-15.6-25.9-26.6-1.6-1-2.1-3.2-1.1-4.8s3.2-2.1 4.8-1.1c.3.2 7.8 5 15.1 11.3 10.7 9.3 15.3 16.7 13.9 22.8-1.9 8.2-13.2 9.5-16.9 10h-.4zm-25.9 18.1c-7.2 0-12.4-8.3-14.2-11.2-1-1.6-.5-3.8 1.1-4.8s3.8-.5 4.8 1.1c3.4 5.4 6.7 8.3 8.8 7.9 4.2-.9 10.3-14.5 12.9-34.8.2-1.9 2-3.3 3.9-3 1.9.2 3.3 2 3 3.9-.8 6.3-5.4 37.9-18.4 40.7-.5.1-1.2.2-1.9.2zm-25-18.1c-5.5 0-9.4-1.3-11.8-4-5.6-6.3-.1-16.3 1.6-19.5.9-1.7 3.1-2.3 4.7-1.4 1.7.9 2.3 3.1 1.4 4.7-3.1 5.6-4 9.9-2.6 11.5 2.9 3.2 17.7 1.9 36.7-5.7a3.57 3.57 0 0 1 4.6 1.9 3.57 3.57 0 0 1-1.9 4.6c-.3.1-8.6 3.5-17.9 5.8-5.9 1.4-10.8 2.1-14.8 2.1zm16.5-12.6c-.6 0-1.2-.2-1.8-.5-.3-.2-7.9-4.8-15.4-10.8-11-8.9-15.8-16.2-14.6-22.3 1.6-8.2 12.9-9.9 16.6-10.5 1.9-.3 3.7 1 4 2.9s-1 3.7-2.9 4c-6.3.9-10.3 2.8-10.7 4.9-1 4.3 9 15.3 26.6 25.8 1.7 1 2.2 3.1 1.2 4.8-.7 1.1-1.8 1.7-3 1.7z"></path><circle cx="63.8" cy="60.5" r="8.4"></circle></g></svg></span><div class="ait-logo-text"><div class="ait-font-bold ait-pl-1 ait-pr-2 ait-break-keep ait-text-sm">ChatGPT Sidebar</div></div></div><div class="ait-credits ait-mr-3 ait-w-5/12"><button type="button" class="ait-text-neutral-500 dark:ait-text-neutral-300 ait-border-2 ait-border-solid ait-border-[var(--ait-tab-menu-active-bg-color)] hover:ait-bg-[var(--ait-tab-menu-active-bg-color)] hover:ait-text-white ait-rounded-lg focus:ait-outline-none ait-text-xs ait-p-2 ait-text-center aitopia-ask-search">Ask ChatGPT</button></div></div><div class="ait-w-1/12 ait-flex ait-grid ait-grid-cols-1 ait-gap-3 ait-items-center"><div class="ait-switchbar-tab-item ait-gap-3 ait-place-items-end ait-rounded-lg ait-p-2 ait-cursor-pointer arc-edge aitopia-options-search"><svg class="svg-inline--fa fa-gear ait-h-4 ait-w-4" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="gear" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path class="" fill="currentColor" d="M495.9 166.6c3.2 8.7 .5 18.4-6.4 24.6l-43.3 39.4c1.1 8.3 1.7 16.8 1.7 25.4s-.6 17.1-1.7 25.4l43.3 39.4c6.9 6.2 9.6 15.9 6.4 24.6c-4.4 11.9-9.7 23.3-15.8 34.3l-4.7 8.1c-6.6 11-14 21.4-22.1 31.2c-5.9 7.2-15.7 9.6-24.5 6.8l-55.7-17.7c-13.4 10.3-28.2 18.9-44 25.4l-12.5 57.1c-2 9.1-9 16.3-18.2 17.8c-13.8 2.3-28 3.5-42.5 3.5s-28.7-1.2-42.5-3.5c-9.2-1.5-16.2-8.7-18.2-17.8l-12.5-57.1c-15.8-6.5-30.6-15.1-44-25.4L83.1 425.9c-8.8 2.8-18.6 .3-24.5-6.8c-8.1-9.8-15.5-20.2-22.1-31.2l-4.7-8.1c-6.1-11-11.4-22.4-15.8-34.3c-3.2-8.7-.5-18.4 6.4-24.6l43.3-39.4C64.6 273.1 64 264.6 64 256s.6-17.1 1.7-25.4L22.4 191.2c-6.9-6.2-9.6-15.9-6.4-24.6c4.4-11.9 9.7-23.3 15.8-34.3l4.7-8.1c6.6-11 14-21.4 22.1-31.2c5.9-7.2 15.7-9.6 24.5-6.8l55.7 17.7c13.4-10.3 28.2-18.9 44-25.4l12.5-57.1c2-9.1 9-16.3 18.2-17.8C227.3 1.2 241.5 0 256 0s28.7 1.2 42.5 3.5c9.2 1.5 16.2 8.7 18.2 17.8l12.5 57.1c15.8 6.5 30.6 15.1 44 25.4l55.7-17.7c8.8-2.8 18.6-.3 24.5 6.8c8.1 9.8 15.5 20.2 22.1 31.2l4.7 8.1c6.1 11 11.4 22.4 15.8 34.3zM256 336a80 80 0 1 0 0-160 80 80 0 1 0 0 160z"></path></svg></div></div></div><!----></div></div></div><textarea id="ait_clipboard" style="position: fixed; bottom: -10px; z-index: -1; height: 1px; width: 1px; font-size: 0.1px;"></textarea><!----><!----><span class="ait-min-h-[180px] ait-max-h-[180px] ait-min-h-[145px] ait-max-h-[145px] ait-min-h-[150px] ait-max-h-[150px] ait-min-h-[160px] ait-h-[160px] ait-max-h-[160px] ait-min-h-[265px] ait-h-[265px] ait-max-h-[265px] ait-hide"></span><!----></div><div data-v-88d74122="" class="aitopia light" nocolor=""><div data-v-88d74122="" class="aitopia-context-container" style="position: absolute; left: 0px; top: 0px; pointer-events: none; z-index: 2147483645;"><div data-v-88d74122="" style="top: 0px; left: 0px; position: absolute;"><div data-v-88d74122="" style="top: 514px; left: 0px; width: 1px; height: 1px; position: absolute;"><!----><div data-v-88d74122="" class="aitopia-writing-btn-wrapper" id="aitopia-writing-btn" style="bottom: 14px; right: 14px; width: 16px; height: 16px; position: absolute;"><div data-v-88d74122="" class="ait-bg-[--ait-tab-menu-active-bg-color]" style="display: inline-block; width: 12px; height: 12px; border-radius: 16px; cursor: pointer; pointer-events: all;"><!----></div></div></div></div></div><!----><!----><!----></div></html>