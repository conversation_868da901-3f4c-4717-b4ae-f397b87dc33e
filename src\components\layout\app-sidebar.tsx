import React, { useState, useEffect } from 'react';
import {
  Sidebar,
  SidebarContent,
} from '@/components/ui/sidebar';
import { NavGroup } from '@/components/layout/nav-group';
import { sidebarData } from './data/sidebar-data';
import If<PERSON><PERSON><PERSON> from '../../assets/images/Ifza_logo.svg';
import IfzaLogoWhite from '../../assets/images/Ifza_logoWhite.png';
import IfzaLogoWithoutText from '../../assets/images/ifza-logo_withoutText.svg';


export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const [logoSrc, setLogoSrc] = useState(IfzaLogo);


  useEffect(() => {
    const updateLogo = () => {
      const htmlTag = document.querySelector('html');
      if (htmlTag && htmlTag.classList.contains('dark')) {
        setLogoSrc(IfzaLogoWhite);
      } else {
        setLogoSrc(IfzaLogo);
      }
    };

    // Initial check
    updateLogo();

    // Add a mutation observer to detect changes to the class attribute on the HTML tag
    const observer = new MutationObserver(updateLogo);
    const htmlTag = document.querySelector('html');
    if (htmlTag) {
      observer.observe(htmlTag, { attributes: true, attributeFilter: ['class'] });
    }

    // Cleanup observer on component unmount
    return () => {
      if (htmlTag) {
        observer.disconnect();
      }
    };
  }, []);

  return (
    <Sidebar collapsible='icon' variant='sidebar' {...props}>
      {/* <SidebarHeader>
        <TeamSwitcher teams={sidebarData.teams} />
      </SidebarHeader> */}
      <div className='flex justify-center bg-logo'>
        <img src={logoSrc} alt="Logo" className='w-40 pt-4 pb-4 mx-auto' id="logo" />
        <img src={IfzaLogoWithoutText} alt="Collapsed Logo" className='w-8 pt-4 pb-4 mx-auto hidden' id="logo-collapsed" />
      </div>
      <SidebarContent>
        {sidebarData.navGroups.map((props) => (
          <NavGroup key={props.title} {...props} />
        ))}
      </SidebarContent>
      {/* <SidebarFooter>
        <NavUser user={sidebarData.user} />
      </SidebarFooter> */}
      
    </Sidebar>
  );
}

export default AppSidebar;
      
