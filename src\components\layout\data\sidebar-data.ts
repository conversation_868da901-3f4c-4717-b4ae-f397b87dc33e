import { AudioWaveform, Command, GalleryVerticalEnd } from 'lucide-react'
import { type SidebarData } from '../types'
import { BookMarked, BlocksIcon, FileSymlink, FileBadge} from 'lucide-react'

export const sidebarData: SidebarData = {
  user: {
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/avatars/lenin.jpg',
  },
  teams: [
    {
      name: 'IFZA Admin',
      logo: Command,
      plan: 'Vite + ShadcnUI',
    },
    {
      name: 'IFZA Inc',
      logo: GalleryVerticalEnd,
      plan: 'Enterprise',
    },
    {
      name: 'IFZA Corp.',
      logo: AudioWaveform,
      plan: 'Startup',
    },
  ],
  navGroups: [
    {
      title: '',
      items: [
        {
          title: 'Home',
          url: '/home',
          icon: BlocksIcon,
        },
        {
          title: 'License Letter Requests',
          url: "/license-letter-request",
          icon: FileSymlink,
        },
        
        {
          title: 'Visa Letter Requests',
          url: '/visa-letter-request',
          icon: BookMarked,
        },
        {
          title: 'License Application',
          url: '/license-application',
          icon: BookMarked,
        },
        {
          title: 'Visa Application',
          url: '/visa-application',
          icon: FileBadge,
        }
        // {
        //   title: 'Renewal Application',
        //   url: '/renewal-application',
        //   icon: BookMarked,
        // },
        // {
        //   title: 'UI Components',
        //   url: '/component-blocks',
        //   icon: BlocksIcon,
        // }
      ],
    },

  ],
}
