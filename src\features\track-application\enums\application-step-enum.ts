export enum ApplicationStep {
  LicenseApplicationSubmitted = 'License application Submitted',
  EstimateSent = 'Estimate Sent',
  ApplicationVerified = 'Application Verified',
  ReceivedProofOfPayment = 'Received Proof of Payment',
  SummarySigned = 'Summary Signed',
  SummarySentForESign = 'Summary Sent for E-Sign',
  KYCApproved = 'KYC Approved',
  KYCSent = 'KYC Sent',
  CompanyNameApproved = 'Company Name Approved',
  ResolutionSigned = 'Resolution Signed',
  MOASent = 'MOA Sent',
  LicenseDocumentsIssued = 'License Documents Issued',
  EstablishmentCardSent = 'Establishment Card Sent',

  //visa
  ApplicationReceived = 'Application Received',
  SignedEC = 'Signed EC',
  POPReceived = 'POP Received',
  EVisaIssued = 'E-Visa Issued',
  StatusChangeIssued = 'Status Change Issued',
  ResidenceVisaStage = 'Residence Visa Stage',
  ResidenceVisaIssued = 'Residence Visa Issued',
  EIDFormCompleted = 'EID Form Completed',
  MedicalFitnessTestCompleted = 'Medical Fitness Test Completed',

  //Renewal
  RenewalApplicationReceived = 'Renewal Application Received',
  MOASigned = 'MOA Signed',
  EstablishmentCardIssued = 'Establishment Card Issued',
}


// can't .map() over enum, so made a array

export const licenseApplicationSteps = [
  ApplicationStep.LicenseApplicationSubmitted,
  ApplicationStep.EstimateSent,
  ApplicationStep.ApplicationVerified,
  ApplicationStep.ReceivedProofOfPayment,
  ApplicationStep.KYCSent,
  ApplicationStep.KYCApproved,
  ApplicationStep.SummarySentForESign,
  ApplicationStep.SummarySigned,
  ApplicationStep.CompanyNameApproved,
  ApplicationStep.ResolutionSigned,
  ApplicationStep.MOASigned,
  ApplicationStep.LicenseDocumentsIssued,
  ApplicationStep.EstablishmentCardSent,
]

export const visaApplicationSteps = [
  ApplicationStep.ApplicationReceived, 
  ApplicationStep.SignedEC,
  ApplicationStep.POPReceived,
  ApplicationStep.EVisaIssued,
  ApplicationStep.StatusChangeIssued, 
  ApplicationStep.EIDFormCompleted,
  ApplicationStep.MedicalFitnessTestCompleted,
  ApplicationStep.ResidenceVisaStage, 
  ApplicationStep.ResidenceVisaIssued, 
]   ;

// Ordered steps for Renewal License applications
export const renewalApplicationSteps = [
  ApplicationStep.RenewalApplicationReceived,
  ApplicationStep.EstimateSent,
  ApplicationStep.ApplicationVerified,
  ApplicationStep.MOASent,
  ApplicationStep.POPReceived,
  ApplicationStep.MOASigned,
  ApplicationStep.LicenseDocumentsIssued,
  ApplicationStep.EstablishmentCardIssued,
];