import Cookies from 'js-cookie'
import { create } from 'zustand'

const ACCESS_TOKEN = 'auth_token'
const USER_KEY = 'auth_user'

interface AuthUser {
  _id: string
  name: string
  email: string
  avatar?: string
}

interface AuthState {
  auth: {
    user: AuthUser | null
    setUser: (user: AuthUser | null) => void
    accessToken: string
    setAccessToken: (accessToken: string) => void
    resetAccessToken: () => void
    reset: () => void
  }
}

export const useAuthStore = create<AuthState>()((set) => {
  // Initialize token and user from cookies
  const cookieToken = Cookies.get(ACCESS_TOKEN)
  const initToken = cookieToken ? JSON.parse(cookieToken) : ''
  const cookieUser = Cookies.get(USER_KEY)
  const initUser = cookieUser ? JSON.parse(cookieUser) : null
  return {
    auth: {
      // hydrate user from cookie
      user: initUser,
      setUser: (user) =>
        set((state) => {
          Cookies.set(USER_KEY, JSON.stringify(user))
          return { ...state, auth: { ...state.auth, user } }
        }),
      accessToken: initToken,
      setAccessToken: (accessToken) =>
        set((state) => {
          Cookies.set(ACCESS_TOKEN, JSON.stringify(accessToken))
          return { ...state, auth: { ...state.auth, accessToken } }
        }),
      resetAccessToken: () =>
        set((state) => {
          Cookies.remove(ACCESS_TOKEN)
          return { ...state, auth: { ...state.auth, accessToken: '' } }
        }),
      reset: () =>
        set((state) => {
          Cookies.remove(ACCESS_TOKEN)
          Cookies.remove(USER_KEY)
          return {
            ...state,
            auth: { ...state.auth, user: null, accessToken: '' },
          }
        }),
    },
  }
})

// export const useAuth = () => useAuthStore((state) => state.auth)
