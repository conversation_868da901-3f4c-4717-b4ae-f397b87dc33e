import React from 'react';

interface ApplicationTypeStatusRowProps {
  applicationType: string;
  status: string;
}

export const ApplicationTypeStatusRow: React.FC<ApplicationTypeStatusRowProps> = ({ applicationType, status }) => (
  <div className="flex items-center gap-5 mb-4 ml-6">
    <p className="text-[#C2A01E] font-semibold">{applicationType}</p>
    <span className="bg-[#F0FDF4] text-xs text-[#22C55E] px-2 py-1 rounded-md font-semibold">
      {status}
    </span>
  </div>
);
