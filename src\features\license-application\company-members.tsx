import { But<PERSON> } from '@/components/ui/button'
import { CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { CountryDropdown } from '@/components/ui/country-dropdown'
import { DateTimePicker } from '@/components/ui/datetime-picker'
import { Pencil1Icon, TrashIcon } from '@radix-ui/react-icons'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogFooter,
  AlertDialogTitle,
  AlertDialogCancel,
  AlertDialogAction,
} from '@/components/ui/alert-dialog'
import FileUploadField from '@/components/ui/fileUpload'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { toast } from '@/hooks/use-toast'
import { zodResolver } from '@hookform/resolvers/zod'
import { PlusIcon } from '@radix-ui/react-icons'
import React, { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import PhoneInput from 'react-phone-input-2'
import { z } from 'zod'
import axios from 'axios'
import { BASE_URL } from '@/utils/network'
import countries from 'i18n-iso-countries';
import enLocale from 'i18n-iso-countries/langs/en.json';
import { countryNameOverrides } from '@/components/ui/country-dropdown'
import { nationalityOverrides } from '../visa-application/schemas/translation'

export interface Member {
  id: number
  roleGeneralManager: boolean
  roleShareholder: boolean
  roleSecretary: boolean
  roleDirector: boolean
  title?: string
  firstName?: string
  lastName?: string
  numberOfShares?: number
  gender: string
  email: string
  mobilePhone: string
  dateOfBirth: Date
  passportNumber: string
  passportIssueDate: Date
  passportExpiryDate: Date
  passportCountryOfIssue: string
  passportPlaceOfIssue: string
  nationality: string
  previousNationality?: string
  visitedUaeBefore: string
  areYouResidentValue: string
  emiratesID?: File | null
  eidNumber?: number
  eidIssueDate?: Date
  eidExpiryDate?: Date
  residentVisa?: File | null
  visaNumber?: number
  visaIssueDate?: Date
  visaExpiryDate?: Date
  fullAddress?: string
  passport?: any
}

const memberFormSchema = z
  .object({
    // roleGeneralManager: z.boolean().refine((val) => val === true, {
    //   message: 'Required.',
    // }),

    // roleShareholder: z.boolean().refine((val) => val === true, {
    //   message: 'Required.',
    // }),

    // roleSecretary: z.boolean().refine((val) => val === true, {
    //   message: 'Required.',
    // }),

    // roleDirector: z.boolean().refine((val) => val === true, {
    //   message: 'Required.',
    // }),
    roleGeneralManager: z.boolean(),
    roleShareholder: z.boolean(),
    roleSecretary: z.boolean(),
    roleDirector: z.boolean(),

    title: z.string().min(2, { message: '*' }),
    firstName: z
      .string()
      .min(2, { message: 'First Name must be at least 2 characters.' }),
    lastName: z
      .string()
      .min(2, { message: 'Last Name must be at least 2 characters.' }),
    numberOfShares: z.number().optional(),

    gender: z.string().min(4, { message: 'Select a choice.' }),
    email: z.string().email({ message: 'Invalid email address' }),
    mobilePhone: z
      .string()
      .min(5, { message: 'You must enter at least 5 digits.' }),
    dateOfBirth: z
      .union([z.date(), z.string().transform((str) => new Date(str))])
      .refine(
        (date) => {
          const currentDate = new Date() // Get the current date
          const age = currentDate.getFullYear() - date.getFullYear() // Calculate the age based on the year
          const month = currentDate.getMonth() - date.getMonth() // Calculate the difference in months

          // Check if the age is 18 or older
          return age > 18 || (age === 18 && month >= 0)
        },
        {
          message: 'Members must be at least 18 years old', // Error message if the age is less than 18
        }
      ),

    passportNumber: z
      .string()
      .min(8, { message: 'You must enter at least 8 digits.' }),

    passportIssueDate: z
      .union([
        z.date(), // Accept a JavaScript Date object
        z.string().transform((str) => new Date(str)), // Or transform a string into a Date
      ])
      .refine(
        (date) => {
          const today = new Date()

          // Set time to 00:00:00 to compare only the date portion
          const todayStart = new Date(today.setHours(0, 0, 0, 0))

          // Date must be strictly before today (not today, not future)
          return date < todayStart
        },
        {
          message: "Passport Issuing date can't be in the future", // Custom error message
        }
      ),

    passportExpiryDate: z
      .union([z.date(), z.string().transform((str) => new Date(str))])
      .refine(
        (date) => {
          const today = new Date()
          const expiryDate = new Date(date)

          // Calculate the difference between the expiry date and today's date
          const diffInTime = expiryDate.getTime() - today.getTime()
          const diffInDays = diffInTime / (1000 * 3600 * 24) // Convert from milliseconds to days

          // Ensure the passport expiry date is at least 120 days in the future
          return diffInDays >= 120
        },
        {
          message: "Passport Expiry Date can't be less than 4 months", // Custom error message
        }
      ),

    passportCountryOfIssue: z.string().min(1, { message: 'Select a choice.' }),

    passportPlaceOfIssue: z
      .string()
      .min(2, { message: 'Enter a value for this field.' }),
    nationality: z.string().min(1, { message: 'Select a choice.' }),

    previousNationality: z.string().min(1, { message: 'Select a choice.' }),

    visitedUaeBefore: z.string().min(1, { message: 'Select a choice.' }),
    areYouResidentValue: z.string().min(1, { message: 'Select a choice.' }),

    emiratesID: z.any().optional(),

    eidNumber: z.number().optional(),

    eidIssueDate: z
      .union([z.date(), z.string().transform((str) => new Date(str))])
      .optional(),

    eidExpiryDate: z
      .union([z.date(), z.string().transform((str) => new Date(str))])
      .optional(),

    residentVisa: z.any().optional(),

    visaNumber: z.number().optional(),

    visaIssueDate: z
      .union([z.date(), z.string().transform((str) => new Date(str))])
      .optional(),

    visaExpiryDate: z
      .union([z.date(), z.string().transform((str) => new Date(str))])
      .optional(),

    fullAddress: z
      .string()
      .min(2, { message: 'Enter a value for this field.' }),

    passport: z.any().refine((val) => val instanceof File && val.size > 0, {
      message: 'Upload a file here.',
    }),
  })
  .refine(
    (data) =>
      data.roleGeneralManager ||
      data.roleShareholder ||
      data.roleSecretary ||
      data.roleDirector,
    {
      message: 'Choose your option(s).',
      path: ['roles'],
    }
  )
  .refine(
    (data) =>
      data.areYouResidentValue !== 'Yes' ||
      (data.emiratesID instanceof File && data.emiratesID.size > 0),
    {
      message: 'Upload a file here.',
      path: ['emiratesID'],
    }
  )

  .refine(
    (data) =>
      data.areYouResidentValue !== 'Yes' ||
      (data.eidNumber !== undefined && data.eidNumber > 0),
    {
      message: 'EID Number is required and must be valid.',
      path: ['eidNumber'],
    }
  )

  .refine((data) => data.areYouResidentValue !== 'Yes' || !!data.eidIssueDate, {
    message: 'Choose a date.',
    path: ['eidIssueDate'],
  })

  .refine(
    (data) => data.areYouResidentValue !== 'Yes' || !!data.eidExpiryDate,
    {
      message: 'Choose a date.',
      path: ['eidExpiryDate'],
    }
  )

  .refine(
    (data) =>
      data.areYouResidentValue !== 'Yes' ||
      (data.residentVisa instanceof File && data.residentVisa.size > 0),
    {
      message: 'Upload a file here.',
      path: ['residentVisa'],
    }
  )

  .refine(
    (data) =>
      data.areYouResidentValue !== 'Yes' ||
      (data.visaNumber !== undefined && data.visaNumber > 0),
    {
      message: 'Visa Number is required',
      path: ['visaNumber'],
    }
  )

  .refine(
    (data) => data.areYouResidentValue !== 'Yes' || !!data.visaIssueDate,
    {
      message: 'Choose a date.',
      path: ['visaIssueDate'],
    }
  )

  .refine(
    (data) => data.areYouResidentValue !== 'Yes' || !!data.visaExpiryDate,
    {
      message: 'Choose a date.',
      path: ['visaExpiryDate'],
    }
  )
  .refine(
    (data) => {
      // Only validate numberOfShares if Shareholder is checked
      if (data.roleShareholder) {
        return (
          typeof data.numberOfShares === 'number' && data.numberOfShares >= 1
        )
      }
      return true
    },
    {
      message: 'Number of Shares is required and must be at least 1.',
      path: ['numberOfShares'],
    }
  )

type MemberFormData = z.infer<typeof memberFormSchema>

interface CompanyMembersProps {
  members: Member[]
  setMembers: React.Dispatch<React.SetStateAction<Member[]>>
  numberOfShareholders: number
  totalShares: number
}
const CompanyMembers: React.FC<CompanyMembersProps> = ({
  members,
  setMembers,
  numberOfShareholders,
  totalShares,
}) => {
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingMember, setEditingMember] = useState<Member | null>(null)
  const [deleteConfirmId, setDeleteConfirmId] = useState<number | null>(null)
  const memberToDelete = members.find((m) => m.id === deleteConfirmId)

  const form = useForm<MemberFormData>({
    mode: 'onSubmit', // Errors show only after form submission attempt
    defaultValues: {
      roleGeneralManager: false,
      roleShareholder: false,
      roleSecretary: false,
      roleDirector: false,
      title: '',
      firstName: '',
      lastName: '',
      numberOfShares: 0,
      gender: '',
      email: '',
      mobilePhone: '',
      dateOfBirth: new Date(),
      passportNumber: '',
      passportIssueDate: new Date(),
      passportExpiryDate: new Date(),
      passportCountryOfIssue: '',
      passportPlaceOfIssue: '',
      nationality: '',
      previousNationality: '',
      visitedUaeBefore: '',
      areYouResidentValue: '',
      emiratesID: null,
      eidNumber: 0,
      eidIssueDate: new Date(),
      eidExpiryDate: new Date(),
      residentVisa: null,
      visaNumber: 0,
      visaIssueDate: new Date(),
      visaExpiryDate: new Date(),
      fullAddress: '',
      passport: null,
    },
    resolver: zodResolver(memberFormSchema), // Your validation schema or resolver
  })

  const [localEidNumber, setLocalEidNumber] = useState(
    form.watch('eidNumber')?.toString() || ''
  )
  const [localVisaNumber, setLocalVisaNumber] = useState(
    form.watch('visaNumber')?.toString() || ''
  )

  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === 'eidNumber') {
        setLocalEidNumber(value?.eidNumber?.toString() || '')
      }
      if (name === 'visaNumber') {
        setLocalVisaNumber(value?.visaNumber?.toString() || '')
      }
    })

    return () => subscription.unsubscribe()
  }, [form])

  const handleEditRow = (member: Member) => {
    setEditingMember(member)

    // Reset the form fields with the selected member's data
    form.reset({
      roleGeneralManager: member.roleGeneralManager,
      roleShareholder: member.roleShareholder,
      roleSecretary: member.roleSecretary,
      roleDirector: member.roleDirector,
      title: member.title,
      firstName: member.firstName,
      lastName: member.lastName,
      numberOfShares: member.numberOfShares,
      gender: member.gender,
      email: member.email,
      mobilePhone: member.mobilePhone,
      dateOfBirth: member.dateOfBirth,
      passportNumber: member.passportNumber,
      passportIssueDate: member.passportIssueDate,
      passportExpiryDate: member.passportExpiryDate,
      passportCountryOfIssue: member.passportCountryOfIssue,
      passportPlaceOfIssue: member.passportPlaceOfIssue,
      nationality: member.nationality,
      previousNationality: member.previousNationality,
      visitedUaeBefore: member.visitedUaeBefore,
      areYouResidentValue: member.areYouResidentValue,
      emiratesID: member.emiratesID || null,
      eidNumber: member.eidNumber || 0,
      eidIssueDate: member.eidIssueDate,
      eidExpiryDate: member.eidExpiryDate,
      residentVisa: member.residentVisa || null,
      visaNumber: member.visaNumber || 0,
      visaIssueDate: member.visaIssueDate,
      visaExpiryDate: member.visaExpiryDate,
      fullAddress: member.fullAddress,
      passport: member.passport,
      // Add other fields if needed
    })

    // Clear any existing validation errors to avoid showing error messages on populated fields
    form.clearErrors()

    // Open the dialog for editing
    setDialogOpen(true)
  }

  countries.registerLocale(enLocale)

  const handleDeleteMember = (id: number) => {
    const deletedMember = members.find((m) => m.id === id)

    setMembers((prevMembers) =>
      prevMembers.filter((member) => member.id !== id)
    )

    toast({
      title: 'Member Deleted',
      description: deletedMember
        ? `${deletedMember.title} ${deletedMember.firstName} ${deletedMember.lastName} has been removed successfully.`
        : 'The member has been removed successfully.',
      variant: 'success',
    })
  }

  const handleSaveMember = (data: MemberFormData) => {
    // const numMembersAllowed = allowedMembers;

    // // Case: Only 1 member → must have all roles selected
    // if (numMembersAllowed === 1) {
    //   if (
    //     !data.roleGeneralManager ||
    //     !data.roleSecretary ||
    //     !data.roleShareholder ||
    //     !data.roleDirector
    //   ) {
    //     toast({
    //       title: 'Missing Roles',
    //       description:
    //         'When there is only one member, all roles must be selected: General Manager, Secretary, Shareholder, and Director.',
    //       variant: 'destructive',
    //     });
    //     return;
    //   }
    // }

    // No role distribution validation here for multiple members — it will be done in nextStep()

    // Save or update member
    if (editingMember) {
      const updated = members.map((m) =>
        m.id === editingMember.id ? { ...data, id: editingMember.id } : m
      )
      setMembers(updated)

      toast({
        title: 'Member Updated',
        description: 'The member details were successfully updated.',
        variant: 'success',
      })
    } else {
      const newId = Date.now()
      setMembers([...members, { ...data, id: newId } as Member])

      // Success message with green style
      toast({
        title: 'Success',
        description: 'The member was successfully added.',
        variant: 'success',
      })
    }

    // Reset and close
    form.reset()
    setEditingMember(null)
    setDialogOpen(false)
  }

  const handleAddRow = () => {
    setEditingMember(null)

    // Reset form with empty/default values explicitly
    form.reset({
      roleGeneralManager: false,
      roleShareholder: false,
      roleSecretary: false,
      roleDirector: false,
      title: '',
      firstName: '',
      lastName: '',
      numberOfShares: 0,
      gender: '',
      email: '',
      mobilePhone: '',
      dateOfBirth: new Date(),
      passportNumber: '',
      passportIssueDate: new Date(),
      passportExpiryDate: new Date(),
      passportCountryOfIssue: '',
      passportPlaceOfIssue: '',
      nationality: '',
      previousNationality: '',
      visitedUaeBefore: '',
      areYouResidentValue: '',
      emiratesID: null,
      eidNumber: 0,
      eidIssueDate: new Date(),
      eidExpiryDate: new Date(),
      residentVisa: null,
      visaNumber: 0,
      visaIssueDate: new Date(),
      visaExpiryDate: new Date(),
      fullAddress: '',
      passport: null,
    })

    setDialogOpen(true)
  }

  const handleCloseDialog = () => {
    setDialogOpen(false)
  }

  const handleMemberData = (data: MemberFormData) => {
    const newMember: Member = {
      id: members.length > 0 ? Math.max(...members.map((m) => m.id)) + 1 : 1,

      title: data.title,
      firstName: data.firstName,
      lastName: data.lastName,
      roleGeneralManager: data.roleGeneralManager,
      roleShareholder: data.roleShareholder,
      roleSecretary: data.roleSecretary,
      roleDirector: data.roleDirector,
      numberOfShares: data.numberOfShares,
      gender: data.gender,
      email: data.email,
      mobilePhone: data.mobilePhone,
      dateOfBirth: data.dateOfBirth,
      passportNumber: data.passportNumber,
      passportIssueDate: data.passportIssueDate,
      passportExpiryDate: data.passportExpiryDate,
      passportCountryOfIssue: data.passportCountryOfIssue,
      passportPlaceOfIssue: data.passportPlaceOfIssue,
      previousNationality: data.previousNationality,
      nationality: data.nationality,
      visitedUaeBefore: data.visitedUaeBefore,
      areYouResidentValue: data.areYouResidentValue,
      emiratesID: data.emiratesID,
      eidNumber: data.eidNumber,
      eidIssueDate: data.eidIssueDate,
      eidExpiryDate: data.eidExpiryDate,
      residentVisa: data.residentVisa,
      visaNumber: data.visaNumber,
      visaIssueDate: data.visaIssueDate,
      visaExpiryDate: data.visaExpiryDate,
      fullAddress: data.fullAddress,

      passport: data.passport ? data.passport : null,
    }

    setMembers((prev) => [...prev, newMember])
    handleCloseDialog()
  }

  const atLeastOneSelected =
    form.watch('roleGeneralManager') ||
    form.watch('roleShareholder') ||
    form.watch('roleSecretary') ||
    form.watch('roleDirector')

  // Count current assigned roles
  const gmAssigned = members.some(
    (m) => m.roleGeneralManager && m.id !== editingMember?.id
  )
  const secretaryAssigned = members.some(
    (m) => m.roleSecretary && m.id !== editingMember?.id
  )
  const shareholdersCount = members.filter((m) => m.roleShareholder).length

  // Disable logic
  const disableGM = gmAssigned && !form.watch('roleGeneralManager')
  const disableSecretary = secretaryAssigned && !form.watch('roleSecretary')
  const disableShareholder =
    shareholdersCount >= numberOfShareholders && !form.watch('roleShareholder')

  const handleFormSubmit = async (data: MemberFormData) => {
    const isValid = await form.trigger()
    // Compute sum excluding the member being edited
    const totalAssigned = members.reduce(
      (sum, m) => sum + (m.numberOfShares || 0),
      0
    )
    const priorSum = editingMember
      ? totalAssigned - (editingMember.numberOfShares || 0)
      : totalAssigned
    if ((data.numberOfShares || 0) + priorSum > totalShares) {
      toast({
        title: 'Error',
        description: `Sum of member shares exceeds total ${totalShares}.`,
        variant: 'destructive',
      })
      return
    }
    if (isValid) {
      // If validation passes, process add or update
      if (editingMember) {
        // update existing member
        // Build updated member object
        const updatedMember: Member = {
          id: editingMember.id,
          passport: data.passport || null,
          roleGeneralManager: data.roleGeneralManager ?? false,
          roleShareholder: data.roleShareholder ?? false,
          roleSecretary: data.roleSecretary ?? false,
          roleDirector: data.roleDirector ?? false,
          title: data.title || '',
          firstName: data.firstName || '',
          lastName: data.lastName || '',
          numberOfShares: data.numberOfShares ?? 0,
          gender: data.gender || '',
          email: data.email || '',
          mobilePhone: data.mobilePhone || '',
          dateOfBirth: data.dateOfBirth || new Date(),
          passportNumber: data.passportNumber || '',
          passportIssueDate: data.passportIssueDate || new Date(),
          passportExpiryDate: data.passportExpiryDate || new Date(),
          passportCountryOfIssue: data.passportCountryOfIssue || '',
          passportPlaceOfIssue: data.passportPlaceOfIssue || '',
          nationality: data.nationality || '',
          previousNationality: data.previousNationality || '',
          visitedUaeBefore: data.visitedUaeBefore || '',
          areYouResidentValue: data.areYouResidentValue || '',
          emiratesID: data.emiratesID || null,
          eidNumber: data.eidNumber ?? 0,
          eidIssueDate: data.eidIssueDate || new Date(),
          eidExpiryDate: data.eidExpiryDate || new Date(),
          residentVisa: data.residentVisa || null,
          visaNumber: data.visaNumber ?? 0,
          visaIssueDate: data.visaIssueDate || new Date(),
          visaExpiryDate: data.visaExpiryDate || new Date(),
        }
        setMembers((prev) =>
          prev.map((m) => (m.id === updatedMember.id ? updatedMember : m))
        )
        toast({ title: 'Member Updated', variant: 'success' })
      } else {
        handleMemberData(data)
      }
      // reset edit mode
      setEditingMember(null)
      // Reset the form
      form.reset()

      // Close the dialog
      handleCloseDialog()

      // Show success message
      toast({
        title: editingMember
          ? 'Member Updated Successfully'
          : 'Member Added Successfully',
        description: editingMember
          ? 'Member data has been updated.'
          : 'The new member has been added.',
        variant: 'success',
      })
    } else {
      // If validation fails, show an error message
      toast({
        title: 'Error',
        description: 'Please fill in all the required fields.',
        variant: 'destructive',
      })
    }
  }

  return (
    <>
      <Table className='w-full table-auto scrollbar-thin'>
        <TableHeader>
          <TableRow>
            <TableHead>Role*</TableHead>
            <TableHead>Name*</TableHead>
            <TableHead>Number of Shares*</TableHead>
            <TableHead>Gender*</TableHead>
            <TableHead>Email*</TableHead>
            <TableHead>Mobile Phone*</TableHead>
            <TableHead>Date of Birth*</TableHead>
            <TableHead>Passport Number*</TableHead>
            <TableHead>Passport Issue Date*</TableHead>
            <TableHead>Passport Expiry Date*</TableHead>
            <TableHead>Passport Country of Issue*</TableHead>
            <TableHead>Passport Place of Issue*</TableHead>
            <TableHead>Nationality*</TableHead>
            <TableHead>Previous Nationality*</TableHead>
            <TableHead>Visited UAE Before*</TableHead>
            <TableHead>Are you UAE Resident*</TableHead>
            <TableHead>Full Address*</TableHead>
            <TableHead>Passport*</TableHead>

            {/* Show Actions column only if there is at least one member */}
            {members.length > 0 && <TableHead>Actions</TableHead>}
          </TableRow>
        </TableHeader>
        <TableBody>
          {members.map((member) => (
            <TableRow key={member.id}>
              <TableCell>
                {' '}
                {[
                  member.roleGeneralManager && 'General Manager',
                  member.roleShareholder && 'Shareholder',
                  member.roleSecretary && 'Secretary',
                  member.roleDirector && 'Director',
                ]
                  .filter(Boolean)
                  .join(', ')}
              </TableCell>
              <TableCell>
                {member.title} {member.firstName} {member.lastName}
              </TableCell>

              <TableCell>{member.numberOfShares}</TableCell>
              <TableCell>{member.gender}</TableCell>
              <TableCell>{member.email}</TableCell>
              <TableCell>{member.mobilePhone}</TableCell>
              <TableCell>{member.dateOfBirth.toLocaleDateString()}</TableCell>
              <TableCell>{member.passportNumber}</TableCell>
              <TableCell>
                {member.passportIssueDate.toLocaleDateString()}
              </TableCell>
              <TableCell>
                {member.passportExpiryDate.toLocaleDateString()}
              </TableCell>
              <TableCell>{member.passportCountryOfIssue}</TableCell>
              <TableCell>{member.passportPlaceOfIssue}</TableCell>
              <TableCell>{member.nationality}</TableCell>
              <TableCell>{member.previousNationality}</TableCell>
              <TableCell>{member.visitedUaeBefore}</TableCell>
              <TableCell>{member.areYouResidentValue}</TableCell>
              <TableCell>{member.fullAddress}</TableCell>
              <TableCell>
                {/* If a file is uploaded, show a download link, else display a placeholder */}
                {member.passport ? (
                  <a
                    href={URL.createObjectURL(member.passport)}
                    target='_blank'
                    rel='noopener noreferrer'
                  >
                    Download Passport
                  </a>
                ) : (
                  'No file uploaded'
                )}
              </TableCell>

              <TableCell>
                <Button
                  type='button'
                  size='sm'
                  variant='link'
                  onClick={() => handleEditRow(member)}
                  className='flex items-center space-x-1 px-0'
                >
                  <Pencil1Icon className='w-4 h-4' />
                  Edit
                </Button>
                <Button
                  type='button'
                  size='sm'
                  variant='link'
                  className='text-red-600 hover:text-red-800 flex items-center space-x-1 px-0'
                  onClick={() => setDeleteConfirmId(member.id)}
                >
                  <TrashIcon className='w-4 h-4' />
                  <span>Delete</span>
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      <Button
        type='button'
        variant='link'
        className='mt-4'
        onClick={handleAddRow}
      >
        <PlusIcon className='mr-2' /> Add New Member
      </Button>
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className='h-[90vh] pb-14 pr-2' variant='default'>
          <DialogHeader className='static'>
            <DialogTitle>
              {editingMember ? 'Edit Member' : 'Add Company Members Subform'}
            </DialogTitle>
            <DialogDescription>
              {editingMember
                ? 'Update member details.'
                : 'Please fill out the following details to register a new member:'}
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(handleFormSubmit)}
              className='scrollbar-thin overflow-y-auto pr-1 pl-1 space-y-4 fz-form'
            >
              <CardHeader className='px-0 pt-4 pb-0'>
                <CardTitle>
                  Please fill out the following details to register a new
                  member:
                </CardTitle>
              </CardHeader>

              {/* Role */}
              <FormItem>
                <FormLabel>
                  Role <span className='text-red-500'>*</span>
                </FormLabel>

                <div className='flex flex-wrap gap-x-4 mt-2'>
                  {/* General Manager */}
                  <FormField
                    control={form.control}
                    name='roleGeneralManager'
                    render={({ field }) => (
                      <FormItem>
                        <div className='flex items-center space-x-2'>
                          <Checkbox
                            id='role-generalManager'
                            checked={field.value}
                            onCheckedChange={(val) => {
                              if (!field.value && disableGM) {
                                toast({
                                  title: 'Error',

                                  description:
                                    'Only one General Manager is allowed.',

                                  variant: 'destructive',
                                })

                                return
                              }

                              field.onChange(val)
                            }}
                            disabled={disableGM}
                          />
                          <label htmlFor='role-generalManager'>
                            General Manager
                          </label>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Shareholder Role */}
                  <FormField
                    control={form.control}
                    name='roleShareholder'
                    render={({ field }) => (
                      <FormItem>
                        <div className='flex items-center space-x-2'>
                          <Checkbox
                            id='role-shareholder'
                            checked={field.value}
                            onCheckedChange={(val) => {
                              field.onChange(val)
                              if (!val) {
                                form.setValue('numberOfShares', 0)
                                form.clearErrors('numberOfShares')
                              } else {
                                form.setValue('numberOfShares', undefined)
                                form.clearErrors('numberOfShares')
                              }
                            }}
                            disabled={disableShareholder}
                          />
                          <label htmlFor='role-shareholder'>Shareholder</label>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Secretary */}
                  <FormField
                    control={form.control}
                    name='roleSecretary'
                    render={({ field }) => (
                      <FormItem>
                        <div className='flex items-center space-x-2'>
                          <Checkbox
                            id='role-secretary'
                            checked={field.value}
                            onCheckedChange={(val) => {
                              if (!field.value && disableSecretary) {
                                toast({
                                  title: 'Error',

                                  description: 'Only one Secretary is allowed.',

                                  variant: 'destructive',
                                })

                                return
                              }

                              field.onChange(val)
                            }}
                            disabled={disableSecretary}
                          />
                          <label htmlFor='role-secretary'>Secretary</label>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Director */}
                  <FormField
                    control={form.control}
                    name='roleDirector'
                    render={({ field }) => (
                      <FormItem>
                        <div className='flex items-center space-x-2'>
                          <Checkbox
                            id='role-director'
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                          <label htmlFor='role-director'>Director</label>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                {!atLeastOneSelected && form.formState.isSubmitted && (
                  <p className='text-sm text-red-500'>Choose your option(s).</p>
                )}
              </FormItem>

              <FormDescription className='mt-2'>
                If there is only one member in the license, please select all
                roles and designations mentioned above. In case of multiple
                members, there can only be 1 General Manager and 1 Secretary.
                You can have multiple Shareholders & Directors.
              </FormDescription>

              {/* Passport field  */}

              <FormField
                control={form.control}
                name='passport'
                render={({ field }) => {
                  const [isUploading, setIsUploading] = React.useState(false)

                  return (
                    <FormItem data-field='coloredPassport'>
                      <FormLabel>
                        Passport <span className='text-red-500'>*</span>
                      </FormLabel>
                      <FormControl>
                        <div>
                          <FileUploadField
                            accept='image/*'
                            value={field.value}
                            onchoose={(file) => {
                              if (!file) {
                                form.setValue('passport', null)
                                form.trigger('passport')
                                return
                              }

                              setIsUploading(true)
                              const reader = new FileReader()

                              reader.onloadend = async () => {
                                const base64 = (reader.result as string).split(
                                  ','
                                )[1]

                                try {
                                  const response = await axios.post(
                                    `${BASE_URL}/api/utils/GetPassport`,
                                    { base64image: base64 }
                                  )

                                  let result = response.data

                                  if (
                                    typeof result.responseMessage === 'string'
                                  ) {
                                    try {
                                      result.responseMessage = JSON.parse(
                                        result.responseMessage
                                      )
                                    } catch (e) {
                                      console.error(
                                        'Error parsing responseMessage:',
                                        e
                                      )
                                    }
                                  }

                                  form.setValue('passport', file)
                                  form.trigger('passport')

                                  if (
                                    JSON.stringify(result).includes(
                                      'ContainerList'
                                    ) &&
                                    result.responseMessage?.ContainerList?.List
                                  ) {
                                    const list =
                                      result.responseMessage.ContainerList.List
                                    let mp: Record<string, string> = {}
                                    let doctypeFound = false

                                    for (const item of list) {
                                      const docType =
                                        item.OneCandidate?.FDSIDList
                                          ?.dDescription
                                      console.log('Detected docType:', docType)
                                      if (docType === 'Passport')
                                        doctypeFound = true

                                      if (
                                        doctypeFound &&
                                        item.Text?.fieldList
                                      ) {
                                        for (const field of item.Text
                                          .fieldList) {
                                          if (field.lcid === 0) {
                                            mp[field.fieldName] = field.value
                                          }
                                        }
                                      }
                                    }

                                    console.log(
                                      'Extracted passport fields:',
                                      mp
                                    )

                                    if (doctypeFound) {
                                      if (mp['Given Names']) {
                                        form.setValue(
                                          'firstName',
                                          mp['Given Names']
                                        )
                                        form.trigger('firstName')
                                      }
                                      if (mp['Surname']) {
                                        form.setValue('lastName', mp['Surname'])
                                        form.trigger('lastName')
                                      }
                                      if (mp['Document Number']) {
                                        form.setValue(
                                          'passportNumber',
                                          mp['Document Number']
                                        )
                                        form.trigger('passportNumber')
                                      }
                                      if (mp['Date of Birth']) {
                                        const dob = new Date(
                                          mp['Date of Birth']
                                        )
                                        if (!isNaN(dob.getTime())) {
                                          form.setValue('dateOfBirth', dob)
                                          form.trigger('dateOfBirth')
                                        }
                                      }

                                      if (mp['Date of Expiry']) {
                                        const expiry = new Date(
                                          mp['Date of Expiry']
                                        )
                                        if (!isNaN(expiry.getTime())) {
                                          form.setValue(
                                            'passportExpiryDate',
                                            expiry
                                          )
                                          form.trigger('passportExpiryDate')
                                        }
                                      }

                                      if (mp['Date of Issue']) {
                                        const issue = new Date(
                                          mp['Date of Issue']
                                        )
                                        if (!isNaN(issue.getTime())) {
                                          form.setValue(
                                            'passportIssueDate',
                                            issue
                                          )
                                          form.trigger('passportIssueDate')
                                        }
                                      }

                                      if (mp['Place of Issue']) {
                                        form.setValue(
                                          'passportPlaceOfIssue',
                                          mp['Place of Issue']
                                        )

                                        form.trigger('passportPlaceOfIssue')
                                      }

                                      if (mp['Issuing State Name']) {
                                        const rawNationality = mp[
                                          'Issuing State Name'
                                        ]
                                          .trim()
                                          .toLowerCase()
                                        const overridden =
                                          countryNameOverrides[rawNationality]
                                        const normalizedNationality =
                                          overridden || mp['Issuing State Name']

                                        form.setValue(
                                          'passportCountryOfIssue',
                                          normalizedNationality
                                        )
                                        form.trigger('passportCountryOfIssue')
                                      }

                                      if (mp['Nationality']) {
                                        let rawNationality = mp['Nationality']
                                          .toString()
                                          .trim()
                                          .toLowerCase()

                                        rawNationality = rawNationality.replace(
                                          /\s+/g,
                                          ' '
                                        )

                                        let normalizedNationality =
                                          countryNameOverrides[
                                            rawNationality
                                          ] ||
                                          nationalityOverrides[
                                            rawNationality
                                          ] ||
                                          mp['Nationality']

                                        form.setValue(
                                          'nationality',
                                          normalizedNationality
                                        )
                                        form.trigger('nationality')
                                      }

                                      if (mp['Sex']) {
                                        const gender =
                                          mp['Sex'] === 'M'
                                            ? 'Male'
                                            : mp['Sex'] === 'F'
                                              ? 'Female'
                                              : undefined

                                        if (gender) {
                                          form.setValue('gender', gender)
                                          form.trigger('gender')
                                        }
                                      }
                                    } else {
                                      toast({
                                        title: 'Invalid Passport',
                                        description:
                                          'Please upload a clear scan of a valid passport.',
                                        variant: 'destructive',
                                      })
                                    }
                                  } else {
                                    toast({
                                      title: 'Invalid Response',
                                      description:
                                        'Unexpected data format received.',
                                      variant: 'destructive',
                                    })
                                  }
                                } catch (error) {
                                  console.error(
                                    'Error uploading passport:',
                                    error
                                  )
                                  toast({
                                    title: 'Error',
                                    description:
                                      'Could not extract data from the passport.',
                                    variant: 'destructive',
                                  })
                                } finally {
                                  setIsUploading(false)
                                }
                              }

                              reader.readAsDataURL(file)
                            }}
                          />
                          {isUploading && (
                            <div className='relative flex items-center gap-2 mt-1 text-primary dark:text-primary-dark text-sm'>
                              <svg
                                className='animate-spin h-4 w-4'
                                viewBox='0 0 24 24'
                              >
                                <circle
                                  className='opacity-25'
                                  cx='12'
                                  cy='12'
                                  r='10'
                                  stroke='#c7a51f'
                                  strokeWidth='4'
                                  fill='none'
                                />
                                <path
                                  className='opacity-75'
                                  fill='#c7a51f'
                                  d='M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z'
                                />
                              </svg>
                              <span>Extracting passport data...</span>
                            </div>
                          )}
                        </div>
                      </FormControl>
                      <FormDescription>
                        Upload a clear passport scan with all corners visible.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )
                }}
              />
              {/* Title Dropdown */}
              <div className='flex items-start space-x-2'>
                <FormField
                  control={form.control}
                  name='title'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Name <span className='text-red-500'>*</span>
                      </FormLabel>
                      <FormControl>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <SelectTrigger className='w-[100px]'>
                            <SelectValue placeholder='Select Title' />
                          </SelectTrigger>
                          <SelectContent position='popper'>
                            <SelectItem value='Mr.'>Mr.</SelectItem>
                            <SelectItem value='Mrs.'>Mrs.</SelectItem>
                            <SelectItem value='Ms.'>Ms.</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormDescription>Title</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {/* First Name Input Field */}
                <div className='w-full'>
                  <FormField
                    control={form.control}
                    name='firstName'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>First Name</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder='First Name' />
                        </FormControl>
                        <FormDescription>First Name</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Last Name Input Field */}
                <div className='w-full'>
                  <FormField
                    control={form.control}
                    name='lastName'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Last Name</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder='Last Name' />
                        </FormControl>
                        <FormDescription>Last Name</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Number of Shares Field */}

              {form.watch('roleShareholder') && (
                <FormField
                  control={form.control}
                  name='numberOfShares'
                  render={({ field }) => {
                    const [localValue, setLocalValue] = useState(
                      field.value?.toString() || ''
                    )

                    useEffect(() => {
                      if (form.watch('roleShareholder')) {
                        setLocalValue(field.value?.toString() || '')
                      }
                    }, [field.value, form.watch('roleShareholder')])

                    return (
                      <FormItem>
                        <FormLabel>
                          Number of Shares{' '}
                          <span className='text-red-500'>*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder='Enter Number of Shares'
                            value={localValue}
                            onChange={(e) => {
                              const value = e.target.value
                              setLocalValue(value)

                              if (value === '') {
                                field.onChange(undefined)
                                form.setError('numberOfShares', {
                                  type: 'manual',
                                  message: 'This field is required',
                                })
                              } else {
                                const numberValue = Number(value)
                                if (!isNaN(numberValue)) {
                                  field.onChange(numberValue)
                                  form.clearErrors('numberOfShares')
                                }
                              }
                            }}
                          />
                        </FormControl>
                        <FormDescription>
                          Please mention the number of shares you want to assign
                          to this particular member.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )
                  }}
                />
              )}

              {/* Gender Dropdown */}

              <FormField
                control={form.control}
                name='gender'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Gender <span className='text-red-500'>*</span>
                    </FormLabel>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <SelectTrigger>
                        <SelectValue placeholder='Select gender' />
                      </SelectTrigger>
                      <SelectContent position='popper'>
                        <SelectItem value='Male'>Male</SelectItem>
                        <SelectItem value='Female'>Female</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Email Field */}

              <FormField
                control={form.control}
                name='email'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Email <span className='text-red-500'>*</span>
                    </FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormDescription>
                      This should be the email address of the company member.
                      Verification email and KYC will be sent to the email
                      address provided. Please make sure the email is valid and
                      belongs to the member of the company.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className='flex flex-wrap space-between gap-y-4'>
                {/* Mobile Phone Field */}
                <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                  <FormField
                    control={form.control}
                    name='mobilePhone'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Mobile Phone <span className='text-red-500'>*</span>
                        </FormLabel>
                        <PhoneInput
                          country={'ae'}
                          // value={field.value.toString()}
                          onChange={(phone) => field.onChange(phone)}
                          containerClass='fz-country-code flex h-9 w-full rounded-tl-[6px] rounded-bl-[6px] border border-input bg-transparent text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm'
                          inputClass=' flex  !h-9 !w-full rounded-tl-[6px] rounded-bl-[6px] border !border-transparent !bg-transparent px-3 text-base !shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm transform translate-y-[-2px]'
                          buttonClass='!h-100 !border-l-transparent !border-t-transparent !bg-slate-200 dark:!bg-slate-800 dark:!border-r-transparent !border-b-transparent !border-gray-200 !bg-transparent !rounded-tl-[8px] !rounded-bl-[6px]'
                          enableSearch={true}
                          searchPlaceholder='Search country...'
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                {/* Date of Birth Field */}
                <div className='flex-1'>
                  <FormField
                    control={form.control}
                    name='dateOfBirth'
                    render={({ field }) => {
                      return (
                        <FormItem>
                          <FormLabel>
                            Date of Birth{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <DateTimePicker
                              key={
                                field.value
                                  ? new Date(field.value).toISOString()
                                  : 'empty'
                              }
                              granularity='day'
                              value={
                                field.value ? new Date(field.value) : undefined
                              }
                              onChange={(val) => {
                                field.onChange(val)
                              }}
                              displayFormat={{
                                hour24: 'dd MMMM yyyy',
                              }}
                            />
                          </FormControl>
                          <FormDescription>
                            dd-MMM-yyyy
                            <br />
                            Shareholder must be 18 years or above at the time of
                            application.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )
                    }}
                  />
                </div>
              </div>

              {/* Passport Number Field */}

              <div className='flex flex-wrap space-between gap-y-4'>
                <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                  <div className='flex-1'>
                    <FormField
                      control={form.control}
                      name='passportNumber'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Passport Number{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder='Enter Passport Number'
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
                {/* Passport Issue Date Field */}
                <div className='flex-1'>
                  <FormField
                    control={form.control}
                    name='passportIssueDate'
                    render={({ field }) => {
                      return (
                        <FormItem>
                          <FormLabel>
                            Passport Issue Date{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <DateTimePicker
                              key={
                                field.value
                                  ? new Date(field.value).toISOString()
                                  : 'empty'
                              }
                              granularity='day'
                              value={
                                field.value ? new Date(field.value) : undefined
                              }
                              onChange={(val) => {
                                field.onChange(val)
                              }}
                              displayFormat={{
                                hour24: 'dd MMMM yyyy',
                              }}
                            />
                          </FormControl>
                          <FormDescription>dd-MMM-yyyy</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )
                    }}
                  />
                </div>
              </div>

              <div className='flex flex-wrap space-between gap-y-4'>
                {/*  Passport Expiry Date Field */}

                <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                  <FormField
                    control={form.control}
                    name='passportExpiryDate'
                    render={({ field }) => {
                      return (
                        <FormItem>
                          <FormLabel>
                            Passport Expiry Date{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <DateTimePicker
                              key={
                                field.value
                                  ? new Date(field.value).toISOString()
                                  : 'empty'
                              }
                              granularity='day'
                              value={
                                field.value ? new Date(field.value) : undefined
                              }
                              onChange={(val) => {
                                field.onChange(val)
                              }}
                              displayFormat={{
                                hour24: 'dd MMMM yyyy',
                              }}
                            />
                          </FormControl>
                          <FormDescription>
                            dd-MMM-yyyy
                            <br />
                            Passport should be valid for at least 120 days at
                            the time of application.
                          </FormDescription>

                          <FormMessage />
                        </FormItem>
                      )
                    }}
                  />
                </div>
                {/* Passport Country of Issue Dropdown */}
                <div className='flex-1'>
                  <FormField
                    control={form.control}
                    name='passportCountryOfIssue'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Passport Country of Issue{' '}
                          <span className='text-red-500'>*</span>
                        </FormLabel>
                        <FormControl>
                          <CountryDropdown
                            placeholder='Country'
                            defaultValue={field.value}
                            onChange={(country) => {
                              console.log('Country name:', country.name)
                              field.onChange(country.name)
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Passport Place of Issue Field */}

              <div className='flex flex-wrap space-between gap-y-4'>
                <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                  <FormField
                    control={form.control}
                    name='passportPlaceOfIssue'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Passport Place of Issue{' '}
                          <span className='text-red-500'>*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder='Enter Passport Place of Issue'
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Nationality Dropdown */}
                <div className='flex-1'>
                  <FormField
                    control={form.control}
                    name='nationality'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Nationality <span className='text-red-500'>*</span>
                        </FormLabel>
                        <FormControl>
                          <CountryDropdown
                            placeholder='Country'
                            defaultValue={field.value}
                            onChange={(country) => {
                              console.log('Country name:', country.name)
                              field.onChange(country.name)
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
              <div className='flex flex-wrap space-between gap-y-4'>
                {/* Previous Nationality Dropdown */}
                <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                  <FormField
                    control={form.control}
                    name='previousNationality'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Previous Nationality{' '}
                          <span className='text-red-500'>*</span>
                        </FormLabel>
                        <FormControl>
                          <CountryDropdown
                            placeholder='Country'
                            defaultValue={field.value}
                            onChange={(country) => {
                              console.log('Country name:', country.name)
                              field.onChange(country.name)
                            }}
                          />
                        </FormControl>
                        <FormDescription>
                          Please enter current Nationality if you do not have
                          previous/dual nationality.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                {/* Visited UAE Before Dropdown */}
                <div className='flex-1'>
                  <FormField
                    control={form.control}
                    name='visitedUaeBefore'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Visited UAE Before ?{' '}
                          <span className='text-red-500'>*</span>
                        </FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder='Select option' />
                          </SelectTrigger>
                          <SelectContent position='popper'>
                            <SelectItem value='Yes'>Yes</SelectItem>
                            <SelectItem value='No'>No</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Are you UAE resident ?*/}

              <FormField
                control={form.control}
                name='areYouResidentValue'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Are you UAE Resident{' '}
                      <span className='text-red-500'>*</span>
                    </FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder='Select option' />
                      </SelectTrigger>
                      <SelectContent position='popper'>
                        <SelectItem value='Yes'>Yes</SelectItem>
                        <SelectItem value='No'>No</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Show Emirates ID and other fields when UAE resident is "Yes" */}
              {form.watch('areYouResidentValue') === 'Yes' && (
                <>
                  <div className='flex flex-wrap space-between gap-y-4'>
                    {/* Emirates ID (File Field) */}
                    <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                      <FormField
                        control={form.control}
                        name='emiratesID'
                        render={({field}) => (
                          <FormItem>
                            <FormLabel>
                              Emirates ID{' '}
                              <span className='text-red-500'>*</span>
                            </FormLabel>
                            <FormControl>
                              <FileUploadField
                                accept='.pdf'
                                value = {field.value}
                                onchoose={(file) => {
                                  form.setValue('emiratesID', file || '')
                                  form.clearErrors('emiratesID')
                                }}
                              />
                            </FormControl>
                            <FormDescription>
                              Please upload the copy of the Emirates ID (Front
                              and Back as one PDF).
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* EID Number (Input Field) */}
                    <div className='flex-1'>
                      <FormField
                        control={form.control}
                        name='eidNumber'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              EID Number <span className='text-red-500'>*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                type='number'
                                placeholder='Enter EID Number'
                                value={localEidNumber}
                                onChange={(e) => {
                                  const value = e.target.value
                                  setLocalEidNumber(value)

                                  if (value === '') {
                                    field.onChange(undefined)
                                    form.setError('eidNumber', {
                                      type: 'manual',
                                      message: 'This field is required',
                                    })
                                  } else {
                                    const numberValue = Number(value)
                                    if (!isNaN(numberValue)) {
                                      field.onChange(numberValue)
                                      form.clearErrors('eidNumber')
                                    }
                                  }
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  <div className='flex flex-wrap space-between gap-y-4'>
                    {/* EID Issue Date (Date Field) */}

                    <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                      <FormField
                        control={form.control}
                        name='eidIssueDate'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              EID Issue Date{' '}
                              <span className='text-red-500'>*</span>
                            </FormLabel>
                            <DateTimePicker
                              granularity='day'
                              value={field.value}
                              onChange={field.onChange}
                              displayFormat={{
                                hour24: 'dd MMMM yyyy',
                              }}
                            />
                            <FormDescription>dd-MMM-yyyy</FormDescription>

                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* EID Expiry Date (Date Field) */}
                    <div className='flex-1'>
                      <FormField
                        control={form.control}
                        name='eidExpiryDate'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              EID Expiry Date{' '}
                              <span className='text-red-500'>*</span>
                            </FormLabel>
                            <DateTimePicker
                              granularity='day'
                              value={field.value}
                              onChange={field.onChange}
                              displayFormat={{
                                hour24: 'dd MMMM yyyy',
                              }}
                            />
                            <FormDescription>dd-MMM-yyyy</FormDescription>

                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  <div className='flex flex-wrap space-between gap-y-4'>
                    {/* Residence Visa */}
                    <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                      <FormField
                        control={form.control}
                        name='residentVisa'
                        render={({field}) => (
                          <FormItem>
                            <FormLabel>
                              Residence Visa{' '}
                              <span className='text-red-500'>*</span>
                            </FormLabel>
                            <FormControl>
                              <FileUploadField
                                accept='.pdf'
                                value={field.value}
                                onchoose={(file) => {
                                  form.setValue('residentVisa', file || '')
                                  form.clearErrors('residentVisa')
                                }}
                              />
                            </FormControl>
                            <FormDescription>
                              Please upload the copy of the resident Visa for
                              UAE.
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    {/* Visa Number (Input Field) */}
                    <div className='flex-1'>
                      <FormField
                        control={form.control}
                        name='visaNumber'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Visa Number{' '}
                              <span className='text-red-500'>*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                type='number'
                                placeholder='Enter Visa Number'
                                value={localVisaNumber}
                                onChange={(e) => {
                                  const value = e.target.value
                                  setLocalVisaNumber(value)

                                  if (value === '') {
                                    field.onChange(undefined)
                                    form.setError('visaNumber', {
                                      type: 'manual',
                                      message: 'This field is required',
                                    })
                                  } else {
                                    const numberValue = Number(value)
                                    if (!isNaN(numberValue)) {
                                      field.onChange(numberValue)
                                      form.clearErrors('visaNumber')
                                    }
                                  }
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                  <div className='flex flex-wrap space-between gap-y-4'>
                    {/* Visa Issue Date (Date Field) */}
                    <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                      <FormField
                        control={form.control}
                        name='visaIssueDate'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Visa Issue Date{' '}
                              <span className='text-red-500'>*</span>
                            </FormLabel>
                            <DateTimePicker
                              granularity='day'
                              value={field.value}
                              onChange={field.onChange}
                              displayFormat={{
                                hour24: 'dd MMMM yyyy',
                              }}
                            />
                            <FormDescription>dd-MMM-yyyy</FormDescription>

                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    {/* Visa Expiry Date (Date Field) */}
                    <div className='flex-1'>
                      <FormField
                        control={form.control}
                        name='visaExpiryDate'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Visa Expiry Date{' '}
                              <span className='text-red-500'>*</span>
                            </FormLabel>
                            <DateTimePicker
                              granularity='day'
                              value={field.value}
                              onChange={field.onChange}
                              displayFormat={{
                                hour24: 'dd MMMM yyyy',
                              }}
                            />
                            <FormDescription>dd-MMM-yyyy</FormDescription>

                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                </>
              )}

              {/* Full Address */}
              <FormField
                control={form.control}
                name='fullAddress'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='text-sm font-semibold'>
                      Full Address (Private Address){' '}
                      <span className='text-red-500'>*</span>
                    </FormLabel>
                    <FormControl>
                      <Input {...field} placeholder='Enter Full Address' />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Passport Guidelines link */}
              <div className='pb-4'>
                <a
                  href='https://cdn.ifza.com/docs/04_visa/pp_eid_reference.pdf'
                  target='_blank'
                  className='text-blue-500 underline'
                >
                  Passport Guidelines
                </a>
              </div>

              <div className='absolute bottom-0 left-0 border-t w-full border-gray-100 p-3 pr-10 bg-background flex justify-end space-x-2 z-10'>
                <Button
                  type='button'
                  variant='outline'
                  onClick={handleCloseDialog}
                >
                  Cancel
                </Button>
                <Button
                  type='button'
                  onClick={form.handleSubmit(handleSaveMember)}
                >
                  {editingMember ? 'Update Member' : 'Add Member'}
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      <AlertDialog
        open={deleteConfirmId !== null}
        onOpenChange={(open) => !open && setDeleteConfirmId(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className='text-center'>
              Are you sure you want to delete the member
              <p className='font-semibold text-red-600 dark:text-red-400'>
                {' '}
                “{memberToDelete?.title} {memberToDelete?.firstName}{' '}
                {memberToDelete?.lastName}”{' '}
                <span className='text-gray-800'>?</span>
              </p>
            </AlertDialogTitle>
          </AlertDialogHeader>
          <AlertDialogFooter className=' flex sm:justify-center'>
            <AlertDialogCancel onClick={() => setDeleteConfirmId(null)}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                if (deleteConfirmId !== null) {
                  handleDeleteMember(deleteConfirmId)
                  setDeleteConfirmId(null)
                }
              }}
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}

export default CompanyMembers
