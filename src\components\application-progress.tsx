import React from 'react'

interface Props {
  currentStep: number
  type?: 'license' | 'visa'
}

export default function ApplicationProgress({
  currentStep,
  type = 'license',
}: Props) {
  const steps =
    type === 'visa'
      ? [
          'Basic Information',
          'Application Information',
          'Passport Details',
          'Employment Details',
          'Salary Breakdown',
          'Company Details',
          'Attachments',
        ]
      : [
          'Company Information',
          'Select the License Type and Visa Package for your Business',
          'Shareholder Structure',
          'Business Activities',
          'Add Company Members',
          'ULTIMATE BENEFICIAL OWNERSHIP',
        ]
  return (
    <div className='flex items-center mt-4 mb-12'>
      {steps.map((label, idx) => {
        const isDone = idx < currentStep
        const isActive = idx === currentStep - 1

        // square box styles
        const boxClasses = isDone
          ? 'border-2 border-primary bg-primary text-white'
          : isActive
            ? 'border-2 border-primary text-primary bg-white dark:bg-slate-800'
            : 'bg-white border border-slate-200 text-slate-500 dark:bg-slate-800 dark:border-slate-600 dark:text-slate-400'
        // Adding margin for first and last step labels
        const labelClasses = `${idx === 0 ? 'ml-12' : ''} ${idx === steps.length - 1 ? 'mr-10' : ''} absolute bottom-[-25px] whitespace-nowrap mt-1 text-center text-xs text-slate-700 dark:text-slate-300 max-w-xs overflow-hidden text-ellipsis`

        return (
          <React.Fragment key={idx}>
            {/* Connector: only between items */}
            {idx > 0 && (
              <div
                className={`flex-1 h-1 transition-colors ${
                  idx < currentStep
                    ? 'bg-primary'
                    : 'bg-slate-200 dark:bg-slate-600'
                }`}
              />
            )}

            {/* Step box + (current only) label */}
            <div className='flex flex-col items-center relative'>
              <div
                className={`w-8 h-8 flex items-center justify-center rounded-lg transition-colors ${boxClasses}`}
              >
                {idx + 1}
              </div>

              {isActive && (
                <div
                  className={`${labelClasses} ${idx === 0 ? 'pl-10' : ''} ${idx === steps.length - 1 ? 'p-0 m-0 right-0' : ''}`}
                >
                  {label}
                </div>
              )}
            </div>
          </React.Fragment>
        )
      })}
    </div>
  )
}
