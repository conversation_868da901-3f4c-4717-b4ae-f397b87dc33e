import React, { useRef } from 'react'

import FzButton from '@/features/track-application/components/public-tracker/custom-ui/fz-button';

type FzCardProps = {
  title: string;
  description: string;
  image: string;
  link: string;
}

const FzCard = ({ title, description, image, link }: FzCardProps) => {
  const cardRef = useRef<HTMLDivElement>(null);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    const card = cardRef.current;
    if (!card) return;
    const rect = card.getBoundingClientRect();
    const x = (e.clientX - rect.left - rect.width / 2) / 20;
    const y = (e.clientY - rect.top - rect.height / 2) / 20;
    card.style.transform = `rotateY(${x}deg) rotateX(${y}deg) scale(1.03)`;
  };

  const handleMouseLeave = () => {
    if (cardRef.current) {
      cardRef.current.style.transform = `rotateY(0deg) rotateX(0deg) scale(1)`;
    }
  };

  return (
    <div style={{ perspective: "1000px" }} className="group">
      <div
        ref={cardRef}
        onMouseMove={handleMouseMove}
        onMouseLeave={handleMouseLeave}
        className="bg-white overflow-hidden transition-all duration-200 ease-linear border-x border-b"
        style={{ transformStyle: "preserve-3d" }}
      >
        <div className="w-full h-[255px] overflow-hidden">
          <img
            src={image}
            alt={title}
            className="w-full h-full object-cover transition-transform duration-500 ease-in-out group-hover:-translate-y-2"
          />
        </div>

        <div className="px-7 py-10 h-[415px] flex flex-col justify-between">
          <div>
            <h3 className="font-tt-firs-neue font-light group-hover:text-ifza-500 dark:text-neutral-900 text-2xl xl:text-3xl mb-4">
                {title}
            </h3>
            <p className="font-light text-slate-600 mb-8">
                {description}
            </p>
          </div>

          <FzButton link={link} className='w-fit after:absolute after:inset-0 after:content-[""]' />
        </div>
      </div>
    </div>
  );
}

export default FzCard