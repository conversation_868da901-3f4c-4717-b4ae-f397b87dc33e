import { Loader2 } from "lucide-react";
import { Main } from "./layout/main";
import { Button } from '@/components/ui/button'
import { Link } from '@tanstack/react-router'

type LoadingMessageProps = {
  loading: boolean;
};

type ErrorMessageProps = {
  error: string;
  trackNewApplicationUrl? : string;
};

export function LoadingMessage({ loading }: LoadingMessageProps) {
  if (!loading) return null;
  return <div className="flex items-center justify-center w-full h-[100vh] text-primary">
    <Loader2 className='mr-2 h-8 w-8 animate-spin' /></div>;
}

export function ErrorMessage({ error, trackNewApplicationUrl }: ErrorMessageProps) {



  if (!error) return null;

  return (
    <Main className="bg-[#F8FAFC] p-6">
      <div className="space-y-8">
        <div className="ml-1 mb-2 flex items-center justify-between">
          <h1 className="text-2xl font-bold tracking-tight lg:text-[28px]">
            Track Your Application
          </h1>
          <Link to={trackNewApplicationUrl}>
            <Button variant="default" className="bg-[#C2A01E] text-white hover:bg-[#b08f1a]">
              Track Another Application
            </Button>
          </Link>
        </div>
        </div>
        <div
      className="text-red-500 mt-8 text-center"
    >
      {error}
    </div>
    </Main>

  );
}



