import React, { useState, useEffect } from 'react'
import { z } from 'zod'
import { useForm, FormProvider } from 'react-hook-form'
import { useWatch } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Loader2 } from 'lucide-react'
import { toast } from '@/hooks/use-toast'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import ApplicationProgress from '@/components/application-progress'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import Step1Form from './components/steps/Step1VisaApplication'
import Step2Form from './components/steps/Step2VisaApplication'
import Step3Form from './components/steps/Step3VisaApplication'
import Step4Form from './components/steps/Step4VisaApplication'
import Step5Form from './components/steps/Step5VisaApplication'
import Step6Form from './components/steps/Step6VisaApplication'
import Step7Form from './components/steps/Step7VisaApplication'
import { applicationFormSchema } from './schemas/application-form-schema'
import { containsSpecialCharacters } from './schemas/translation'
import { ApplicationFormValues } from './types/application-form-types'
import { mainpulateVisaApplicationData } from '@/services/visaapplication'
import { submitVisaApplicationWithDocuments } from '@/services/visaapplication';

export default function VisaApplication() {
const setIsFormSubmitted = (_: boolean) => {}
  const [isLoading, setIsLoading] = useState(false)
  useState<boolean>(false)
  const [date, setDate] = useState<Date | undefined>(undefined)
  const form = useForm<ApplicationFormValues>({
    resolver: zodResolver(applicationFormSchema),
    defaultValues: {
      appUpdatesEmail: '',
      whatsAppNumber: '',
      applicationDate: new Date(),
      visaType: '',
      serviceType: '',
      tradeLicenseValidated: false, // Trade license must be valid for 90 days ...
      visaApplicationType: '',
      nationality: '',
      residentVisaStamping: '', // Resident Visa Stamping Type
      familyOnHoldLetter: '', // Do you require a family on hold letter ?
      renewalDisclaimer: false,
      visaFree: '', // Do you want to apply VFL for this VISA ?
      eVisaApplicationType: '',
      outsideCountryInstructions: false, // Applicant should not enter the UAE until...
      outsideCountry: false, // Outside Country Visa Declaration
      updatedRVCopy: '',
      idCardUpload: '', // National ID Card upload
      currentVisaStatus: '',
      visaValidUntil: new Date(),
      title: '',
      firstName: '',
      middleName: '',
      lastName: '',
      title1: '',
      arabicName: '', // Do you have Arabic Name in your Passport?
      firstNameArabic: '',
      middleNameArabic: '',
      lastNameArabic: '',
      emailAddress: '',
      phone: '',
      streetAddress: '', // UAE Address
      addressLine: '', // UAE Address
      cityAddress: '', // UAE Address
      stateProvince: '', // UAE Address
      country: '', // UAE Address
      streetAddress1: '', // Home Country Address
      addressLine2: '', // Home Country Address
      city: '', // Home Country Address
      province: '', // Home Country Address
      country1: '', // Home Country Address
      emiratesID: '',
      emiratesIDNumber: '',
      emiratesIDExpiryDate: undefined,
      emiratesIDCopy: null,
      passportNumber: '',
      placeOfIssue: '',
      placeOfIssueArabic: '',
      passportType: '',
      agreementPassportRules: false, // Agreement to passport rules
      Agreed: false, // I agree to the above status change statement and the rules associated
      countryOfIssuance: '',
      coloredPassport: null, // Colored Passport Copy Page 1
      coloredPassport2: null, // Colored Passport Copy Page 2
      title2: '',
      firstName2: '',
      lastName2: '',
      passportIssueDate: undefined,
      passportExpiryDate: undefined,
      cityOfBirth: '',
      cityOfBirthArabic: '',
      countryOfBirth: '',
      dateOfBirth: undefined, // Date of Birth
      gender: '',
      previousNationality: '',
      maritalStatus: '',
      religion: '',
      religionSubCategory: '',
      fatherFullName: '',
      motherFullName: '',
      motherFullNameArabic: '',
      photoOfApplicant: null,
      visaApplicantFiles: [],
      typeOfEmployment: 'Limited',
      employmentDuration: '', // Employment Duration in Months
      doYouHaveADegree: '', // Do you have an attested degree
      attestedDegree: '',
      noAttestedDegree: '',
      uploadAttestedDegree: null, // Upload Attested Degree
      jobTitleChange: '', // Do you want to change the Job title of the Visa holder ?
      educationQualification: '',
      employmentStartDate: undefined,
      probationPeriod: '',
      employmentTerminationNotice: '',
      returnTicket: '', // Return Ticket Eligibility
      ticketEntitlementPeriod: '',
      annualLeaveEntitlement: '',
      workingDays: 2,
      calendarDays: 3,
      salaryChange: '',
      basicSalary: 0,
      transportationAllowance: 0,
      accommodationAllowance: 0,
      otherAllowance: 0,
      totalMonthlySalary: 0,
      preferredPaymentMethod: '',
      salaryAmendmentBankStatement: null,
      visaFee: 3750,
      statusChange: 1600,
      vipStampingFee: 1500,
      amendmentFee: 250,
      partnerInvestorVisa: 1000, // Partner/Investor Visa
      totalAmountToBePaid: 0,
      proofOfPayment: null, // Proof of Payment
      companyName: '',
      tradeLicenseNumber: '',
      establishmentCardNumber: '',
      authorizedSignatory: '',
      emailAddressOfGeneralManager: '',
      termsAndConditions: false, // Terms & Conditions Agreement : Step 6
      affirmInformation: false, // I affirm that all the information provided .. : Step 7
    },
    // shouldUnregister: true,
    mode: 'all',
    criteriaMode: 'all',
  })

  const [showEVisaType, setShowEVisaType] = useState(false)

  const fieldNames = [
    'workingDays',
    'calendarDays',
    'basicSalary',
    'transportationAllowance',
    'accommodationAllowance',
    'otherAllowance',
  ] as const

  type FieldName = (typeof fieldNames)[number]

  const [localValues, setLocalValues] = useState<Record<FieldName, string>>(
    Object.fromEntries(
      fieldNames.map((name) => [name, form.watch(name)?.toString() || ''])
    ) as Record<FieldName, string>
  )

  useEffect(() => {
    const subscription = form.watch((values, { name }) => {
      if (name && fieldNames.includes(name as FieldName)) {
        setLocalValues((prev) => ({
          ...prev,
          [name as FieldName]: values?.[name as FieldName]?.toString() || '',
        }))
      }
    })
    return () => subscription.unsubscribe()
  }, [form])

  useEffect(() => {
    const subscription = form.watch((values) => {
      const totalSalary =
        Number(values.basicSalary || 0) +
        Number(values.transportationAllowance || 0) +
        Number(values.accommodationAllowance || 0) +
        Number(values.otherAllowance || 0)

      const current = form.getValues('totalMonthlySalary')

      if (current !== totalSalary) {
        form.setValue('totalMonthlySalary', totalSalary)
      }
    })

    return () => subscription.unsubscribe()
  }, [form])

  const nationality = useWatch({ control: form.control, name: 'nationality' })
  const visaType = useWatch({ control: form.control, name: 'visaType' })

  useEffect(() => {
    // Ensure that nationality is not undefined or empty, and compare it to the list of nationalities that require an eVisa
    // 'Palestine State' is Missing in the list of nationalities
    const shouldShowEVisa = Boolean(
      nationality &&
        visaType &&
        visaType !== 'New Employment Visa' &&
        visaType !== 'New Investor Visa' &&
        visaType !== 'New Partner Visa' &&
        ![
          'Afghanistan',
          'Bangladesh',
          'Algeria',
          'China',
          'Egypt',
          'Indonesia',
          'Iran',
          'Iraq',
          'Israel',
          'Lebanon',
          'Morocco',
          'Nepal',
          'Nigeria',
          'Libya',
          'Pakistan',
          'Somalia',
          'Sri Lanka',
          'Syria',
          'Tunisia',
          'Yemen',
        ].includes(nationality)
    )
    // Set the state to a boolean value (true or false)
    setShowEVisaType(shouldShowEVisa)
  }, [nationality, visaType])

  useEffect(() => {
    if (nationality && nationality !== 'Syria' && nationality !== 'Lebanon') {
      form.setValue('passportType', 'Ordinary')
    }
  }, [nationality, form])

  const [currentStep, setCurrentStep] = React.useState(1) // Track current step

  const { watch, setValue, getValues } = form

  useEffect(() => {
    // Watch for changes in the fields
    const sub = watch((values) => {
      const {
        visaApplicationType,
        visaType,
        residentVisaStamping,
        attestedDegree,
        noAttestedDegree,
        salaryChange,
      } = values

      let total = 0

      // Always add visaFee
      total += 3750

      // Add statusChange only when Visa App Type is 'In-Country'
      // and Visa Type is NOT 'Employment Visa Renewal' or 'Work Permit Renewal'
      const showStatusChange =
        visaApplicationType === 'Yes' &&
        visaType !== 'Employment Visa Renewal' &&
        visaType !== 'Work Permit Renewal'
      if (showStatusChange) {
        total += 1600
      }

      // Add VIP Stamping Fee only when stamping type is 'VIP'
      if (residentVisaStamping === 'VIP') {
        total += 1500
      }

      // Add Partner/Investor Visa fee only when job title is 'Partner' or 'Investor'
      const isPartnerOrInvestor =
        attestedDegree === 'Partner' ||
        attestedDegree === 'Investor' ||
        noAttestedDegree === 'Partner' ||
        noAttestedDegree === 'Investor'

      if (isPartnerOrInvestor) {
        total += 1000
      }

      // Add Amendment Fee when visaType is NOT 'Employment Visa Renewal' OR salaryChange is 'Yes'
      const shouldAddAmendmentFee =
        visaType !== 'Employment Visa Renewal' || salaryChange === 'Yes'

      if (shouldAddAmendmentFee) {
        total += 250
      }

      // Check if the total has changed before calling setValue
      const currentTotal = getValues('totalAmountToBePaid')
      if (currentTotal !== total) {
        setValue('totalAmountToBePaid', total)
      }
    })

    // Cleanup the subscription on unmount
    return () => sub.unsubscribe()
  }, [watch, setValue, getValues])

  const fieldsPerStep: Record<number, (keyof ApplicationFormValues)[]> = {
    1: [
      'appUpdatesEmail',
      'whatsAppNumber',
      'applicationDate',
      'visaType',
      'serviceType',
      'familyOnHoldLetter',
      'tradeLicenseValidated',
      'renewalDisclaimer',
      'visaApplicationType',
      'visaFree',
      'outsideCountryInstructions',
      'outsideCountry',
      'currentVisaStatus',
      'nationality',
      'Agreed',
      'updatedRVCopy',
      'idCardUpload',
      'eVisaApplicationType',
      'residentVisaStamping',
      'visaValidUntil',
    ], // Fields for step 1

    2: [
      'title',
      'firstName',
      'middleName',
      'lastName',
      'arabicName',
      'title1',
      'firstNameArabic',
      'middleNameArabic',
      'lastNameArabic',
      'emailAddress',
      'phone',
      'streetAddress',
      'addressLine',
      'cityAddress',
      'stateProvince',
      'country',
      'streetAddress1',
      'addressLine2',
      'city',
      'province',
      'country1',
      'emiratesID',
      'emiratesIDNumber',
      'emiratesIDExpiryDate',
      'emiratesIDCopy',
    ], // Fields for step 2

    3: [
      'passportNumber',
      'placeOfIssue',
      'placeOfIssueArabic',
      'passportType',
      'agreementPassportRules',
      'countryOfIssuance',
      'coloredPassport',
      'coloredPassport2',
      'title2',
      'firstName2',
      'lastName2',
      'passportIssueDate',
      'passportExpiryDate',
      'cityOfBirth',
      'cityOfBirthArabic',
      'countryOfBirth',
      'dateOfBirth',
      'gender',
      'previousNationality',
      'maritalStatus',
      'religion',
      'religionSubCategory',
      'fatherFullName',
      'motherFullName',
      'motherFullNameArabic',
    ], // Fields for step 3

    4: [
      'typeOfEmployment',
      'employmentDuration',
      'doYouHaveADegree',
      'attestedDegree',
      'noAttestedDegree',
      'uploadAttestedDegree',
      'jobTitleChange',
      'employmentStartDate',
      'probationPeriod',
      'employmentTerminationNotice',
      'educationQualification',
      'returnTicket',
      'ticketEntitlementPeriod',
      'annualLeaveEntitlement',
      'workingDays',
      'calendarDays',
    ], // Fields for step 4

    5: [
      'salaryChange',
      'basicSalary',
      'transportationAllowance',
      'accommodationAllowance',
      'otherAllowance',
      'totalMonthlySalary',
    ], // Fields for step 5

    6: [
      'companyName',
      'tradeLicenseNumber',
      'establishmentCardNumber',
      'authorizedSignatory',
      'emailAddressOfGeneralManager',
      'termsAndConditions',
    ], // Fields for step 6

    7: [
      'photoOfApplicant',
      'visaApplicantFiles',
      'proofOfPayment',
      'preferredPaymentMethod',
      'salaryAmendmentBankStatement',
      'visaFee',
      'statusChange',
      'vipStampingFee',
      'amendmentFee',
      'partnerInvestorVisa',
      'totalAmountToBePaid',
      'firstName',
      'lastName',
      'affirmInformation',
    ], // Fields for step 7
  }

  const [, setStepValues] = useState<Record<number, ApplicationFormValues>>({})

  const noSpecialChars = (val: string) => !containsSpecialCharacters(val)
  const getStepSchema = (step: number, values: ApplicationFormValues) => {
    const ineligibleNationalities = [
      'Afghanistan',
      'Bangladesh',
      'Algeria',
      'China',
      'Egypt',
      'Indonesia',
      'Iran',
      'Iraq',
      'Israel',
      'Lebanon',
      'Morocco',
      'Nepal',
      'Nigeria',
      'Libya',
      'Pakistan',
      'Somalia',
      'Sri Lanka',
      'Syria',
      'Tunisia',
      'Yemen',
    ]
    switch (step) {
      case 1:
        return z.object({
          appUpdatesEmail: applicationFormSchema.shape.appUpdatesEmail,
          visaType: applicationFormSchema.shape.visaType,

          tradeLicenseValidated:
            applicationFormSchema.shape.tradeLicenseValidated,

          ...(values.visaType === 'Employment Visa Renewal' && {
            renewalDisclaimer: applicationFormSchema.shape.renewalDisclaimer,
          }),
          visaApplicationType: applicationFormSchema.shape.visaApplicationType,
          ...((form.watch('visaType') === 'New Employment Visa' ||
            form.watch('visaType') === 'New Investor Visa' ||
            form.watch('visaType') === 'New Partner Visa') && {
            visaFree: applicationFormSchema.shape.visaFree,
          }),
          ...(values.visaApplicationType === 'No' && {
            outsideCountryInstructions:
              applicationFormSchema.shape.outsideCountryInstructions,
          }),
          ...(values.visaApplicationType === 'No' && {
            outsideCountry: applicationFormSchema.shape.outsideCountry,
          }),
          ...(values.visaApplicationType === 'Yes' && {
            currentVisaStatus: applicationFormSchema.shape.currentVisaStatus,
          }),
          nationality: applicationFormSchema.shape.nationality,
          ...(form.watch('visaApplicationType') === 'Yes' &&
            ['Afghanistan', 'Bangladesh', 'Pakistan', 'Nigeria'].includes(
              form.watch('nationality')
            ) && {
              Agreed: applicationFormSchema.shape.Agreed,
            }),

          ...(values.visaType === 'Work Permit Renewal' && {
            updatedRVCopy: applicationFormSchema.shape.updatedRVCopy,
          }),
          ...((values.nationality === 'Afghanistan' ||
            values.nationality === 'Iran' ||
            values.nationality === 'Iraq' ||
            values.nationality === 'Pakistan') && {
            idCardUpload: applicationFormSchema.shape.idCardUpload,
          }),

          ...(!ineligibleNationalities.includes(values.nationality ?? '') &&
            values.visaType !== 'New Employment Visa' &&
            values.visaType !== 'New Investor Visa' &&
            values.visaType !== 'New Partner Visa' && {
              eVisaApplicationType: z
                .string()
                .min(1, { message: 'Select a choice.' }),
            }),

          residentVisaStamping:
            applicationFormSchema.shape.residentVisaStamping,

          ...((form.watch('visaType') === 'New Employment Visa' ||
            form.watch('visaType') === 'New Investor Visa' ||
            form.watch('visaType') === 'New Partner Visa') && {
            familyOnHoldLetter: applicationFormSchema.shape.familyOnHoldLetter,
          }),
        })

      case 2:
        return z.object({
          title: applicationFormSchema.shape.title,
          firstName: applicationFormSchema.shape.firstName.refine(
            noSpecialChars,
            {
              message: 'First name contains invalid special characters.',
            }
          ),
          middleName: applicationFormSchema.shape.middleName
            .optional()
            .refine((val) => !val || noSpecialChars(val), {
              message: 'Middle name contains invalid special characters.',
            }),

          lastName: applicationFormSchema.shape.lastName.refine(
            noSpecialChars,
            {
              message: 'Last name contains invalid special characters.',
            }
          ),
          arabicName: applicationFormSchema.shape.arabicName,
          emailAddress: applicationFormSchema.shape.emailAddress,
          phone: applicationFormSchema.shape.phone,
          streetAddress1: applicationFormSchema.shape.streetAddress1,
          city: applicationFormSchema.shape.city,
          country1: applicationFormSchema.shape.country1,
          emiratesID: applicationFormSchema.shape.emiratesID,
          ...(values.emiratesID === 'Yes' && {
            emiratesIDNumber: applicationFormSchema.shape.emiratesIDNumber,
          }),
          ...(values.emiratesID === 'Yes' && {
            emiratesIDExpiryDate:
              applicationFormSchema.shape.emiratesIDExpiryDate,
          }),
          ...(values.emiratesID === 'Yes' && {
            emiratesIDCopy: applicationFormSchema.shape.emiratesIDCopy,
          }),
        })

      case 3:
        return z.object({
          passportNumber: applicationFormSchema.shape.passportNumber,
          placeOfIssue: applicationFormSchema.shape.placeOfIssue,
          passportType: applicationFormSchema.shape.passportType,
          agreementPassportRules:
            applicationFormSchema.shape.agreementPassportRules,
          countryOfIssuance: applicationFormSchema.shape.countryOfIssuance,
          coloredPassport: applicationFormSchema.shape.coloredPassport,

          ...(['Syria', 'India', 'Turkey'].includes(
            values.countryOfIssuance
          ) && {
            coloredPassport2: applicationFormSchema.shape.coloredPassport2,
          }),
          title2: applicationFormSchema.shape.title2,
          firstName2: applicationFormSchema.shape.firstName2,
          lastName2: applicationFormSchema.shape.lastName2,
          passportIssueDate: applicationFormSchema.shape.passportIssueDate,
          passportExpiryDate: applicationFormSchema.shape.passportExpiryDate,
          cityOfBirth: applicationFormSchema.shape.cityOfBirth,
          countryOfBirth: applicationFormSchema.shape.countryOfBirth,
          dateOfBirth: applicationFormSchema.shape.dateOfBirth,
          gender: applicationFormSchema.shape.gender,
          // previousNationality: applicationFormSchema.shape.previousNationality,
          maritalStatus: applicationFormSchema.shape.maritalStatus,
          religion: applicationFormSchema.shape.religion,
          ...(values.religion === 'Islam' && {
            religionSubCategory:
              applicationFormSchema.shape.religionSubCategory,
          }),
          fatherFullName: applicationFormSchema.shape.fatherFullName,
          motherFullName: applicationFormSchema.shape.motherFullName,
        })

      case 4:
        return z.object({
          employmentDuration: applicationFormSchema.shape.employmentDuration,

          doYouHaveADegree: applicationFormSchema.shape.doYouHaveADegree,

          ...(values.doYouHaveADegree === 'Yes' && {
            attestedDegree: applicationFormSchema.shape.attestedDegree,
          }),

          ...(values.doYouHaveADegree === 'No' && {
            noAttestedDegree: applicationFormSchema.shape.noAttestedDegree,
          }),

          ...(values.doYouHaveADegree === 'Yes' && {
            uploadAttestedDegree:
              applicationFormSchema.shape.uploadAttestedDegree,
          }),

          employmentStartDate: applicationFormSchema.shape.employmentStartDate,
          probationPeriod: applicationFormSchema.shape.probationPeriod,
          employmentTerminationNotice:
            applicationFormSchema.shape.employmentTerminationNotice,
          educationQualification:
            applicationFormSchema.shape.educationQualification,
          returnTicket: applicationFormSchema.shape.returnTicket,
          ...((watch('returnTicket') === 'Economy' ||
            watch('returnTicket') === 'Business' ||
            watch('returnTicket') === 'First Class') && {
            ticketEntitlementPeriod:
              applicationFormSchema.shape.ticketEntitlementPeriod,
          }),
          annualLeaveEntitlement:
            applicationFormSchema.shape.annualLeaveEntitlement,
          ...(watch('annualLeaveEntitlement') === 'Working Days' && {
            workingDays: applicationFormSchema.shape.workingDays,
          }),
          ...(watch('annualLeaveEntitlement') === 'Calendar Days' && {
            calendarDays: applicationFormSchema.shape.calendarDays,
          }),
        })

      case 5:
        return z.object({
          basicSalary: applicationFormSchema.shape.basicSalary,
        })

      case 6:
        return z.object({
          companyName: applicationFormSchema.shape.companyName,
          tradeLicenseNumber: applicationFormSchema.shape.tradeLicenseNumber,
          establishmentCardNumber:
            applicationFormSchema.shape.establishmentCardNumber,
          authorizedSignatory: applicationFormSchema.shape.authorizedSignatory,
          emailAddressOfGeneralManager:
            applicationFormSchema.shape.emailAddressOfGeneralManager,
          termsAndConditions: applicationFormSchema.shape.termsAndConditions,
        })

      case 7:
        return z.object({
          photoOfApplicant: applicationFormSchema.shape.photoOfApplicant,

          ...(values.salaryChange === 'Yes' && {
            salaryAmendmentBankStatement:
              applicationFormSchema.shape.salaryAmendmentBankStatement,
          }),

          visaApplicantFiles: applicationFormSchema.shape.visaApplicantFiles,
          preferredPaymentMethod:
            applicationFormSchema.shape.preferredPaymentMethod,
          ...(['Already Paid with the License Application'].includes(
            values.preferredPaymentMethod
          ) && {
            proofOfPayment: applicationFormSchema.shape.proofOfPayment,
          }),

          visaFee: applicationFormSchema.shape.visaFee,
          statusChange: applicationFormSchema.shape.statusChange,
          vipStampingFee: applicationFormSchema.shape.vipStampingFee,
          partnerInvestorVisa: applicationFormSchema.shape.partnerInvestorVisa,
          affirmInformation: applicationFormSchema.shape.affirmInformation,
        })

      default:
        return z.object({})
    }
  }

  useEffect(() => {
    if (
      form.formState.isSubmitted &&
      Object.keys(form.formState.errors).length > 0
    ) {
      const timer = setTimeout(() => {
        scrollToFirstErrorField()
      }, 300) // Delay to ensure DOM is rendered

      return () => clearTimeout(timer)
    }
  }, [form.formState.errors, form.formState.isSubmitted])

  const scrollToFirstErrorField = () => {
    setTimeout(() => {
      const errorKeys = Object.keys(form.formState.errors)
      if (errorKeys.length > 0) {
        const fieldName = errorKeys[0]

        const selectors = [
          `[name="${fieldName}"]`,
          `[data-error-field="${fieldName}"]`,
          `[data-field="${fieldName}"]`,
          `.form-item[data-error-field="${fieldName}"]`,
          `.form-item[data-field="${fieldName}"]`,
          `.form-item[name="${fieldName}"]`,
          `[data-select-name="${fieldName}"]`,
        ]

        let input = null
        for (const selector of selectors) {
          input = document.querySelector(selector)
          if (input) break
        }

        if (input) {
          const wrapper =
            input.closest('.form-group, .form-item, .form-control') || input
          wrapper.scrollIntoView({ behavior: 'smooth', block: 'center' })
          const focusable = wrapper.querySelector(
            'input, select, textarea, [tabindex]'
          )
          if (
            focusable &&
            typeof (focusable as HTMLElement).focus === 'function'
          ) {
            ;(focusable as HTMLElement).focus()
          }
        }
      }
    }, 300)
  }

  // Function to move between steps
  const nextStep = async () => {
    const values = form.getValues()

    // Get the schema for the current step
    const schema = getStepSchema(currentStep, values)

    // Extract the fields required for validation from the schema
    const requiredFields = Object.keys(
      schema.shape
    ) as (keyof ApplicationFormValues)[]

    // Trigger validation for only the required fields
    await form.trigger(requiredFields)

    // Use Zod to validate the values manually and catch detailed errors
    const result = schema.safeParse(values)

    if (!result.success) {
      const issues = result.error.issues

      // Set manual errors for the invalid fields
      issues.forEach((issue) => {
        const field = issue.path[0] as keyof ApplicationFormValues

        if (requiredFields.includes(field)) {
          form.setError(field, {
            type: 'manual',
            message: issue.message,
          })
        }
      })

      toast({
        title: 'Error',
        description: 'Please fill in all the required fields.',
        variant: 'destructive',
      })
      scrollToFirstErrorField()
      return // Stop if there are validation errors
    }

    // Clear errors and proceed to the next step
    form.clearErrors()
    setStepValues((prev) => ({ ...prev, [currentStep]: values }))

    if (currentStep < 7) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      form.clearErrors()
      setCurrentStep(currentStep - 1)
    }
  }

const onSubmit = async (data: ApplicationFormValues) => {
  const payload = mainpulateVisaApplicationData(data)

  // Prepare files array for upload
  // Map file type according to backend requirements
  const fileFieldMapping: { [key: string]: string } = {
    photoOfApplicant: 'photo',
    visaApplicantFiles: 'supporting_documents',
    coloredPassport: 'passport_copy_page_1',
    coloredPassport2: 'passport_copy_page_2',
    proofOfPayment: 'payment_receipt',
  }

  // Manual validation for proofOfPayment (after .trigger)
  const preferredPaymentMethod = form.getValues('preferredPaymentMethod')
  const proofOfPayment = form.getValues('proofOfPayment')

  let manualProofOfPaymentError = false
  if (
    preferredPaymentMethod === 'Already Paid with the License Application'
  ) {
    if (!(proofOfPayment instanceof File && proofOfPayment.size > 0)) {
      form.setError('proofOfPayment', {
        type: 'manual',
        message: 'Upload a file here.',
      })
      manualProofOfPaymentError = true
    }
  }

  // Log form data for the current step
  if (currentStep === 7) {
    const step7Values = fieldsPerStep[7].reduce((acc, field) => {
      acc[field] = form.getValues(field)
      return acc
    }, {} as Partial<ApplicationFormValues>)

    // Only for debugging, no assignment needed
    step7Values.visaApplicantFiles
      ? step7Values.visaApplicantFiles.map((file: File) => file.name)
      : []

    // Optional manual validation for visaApplicantFiles if needed:
    if (
      !step7Values.visaApplicantFiles ||
      (Array.isArray(step7Values.visaApplicantFiles) &&
        step7Values.visaApplicantFiles.length === 0)
    ) {
      form.setError('visaApplicantFiles', {
        type: 'manual',
        message: 'Upload at least one file.',
      })
    }

    // Manually trigger validation for all fields in Step 7
    const step7Fields = fieldsPerStep[7] || [];
    await form.trigger(step7Fields);
  }

  // Collect files from form fields
  const files: Array<{ type: string; base64: string }> = []

  // photoOfApplicant
  if (data.photoOfApplicant instanceof File) {
    files.push({
      type: fileFieldMapping.photoOfApplicant,
      base64: await fileToBase64(data.photoOfApplicant),
    })
  } else {
    files.push({ type: fileFieldMapping.photoOfApplicant, base64: '' })
  }

  // visaApplicantFiles (array)
  if (
    Array.isArray(data.visaApplicantFiles) &&
    data.visaApplicantFiles.length > 0
  ) {
    for (const file of data.visaApplicantFiles) {
      if (file instanceof File) {
        files.push({
          type: fileFieldMapping.visaApplicantFiles,
          base64: await fileToBase64(file),
        })
      }
    }
  } else {
    // If no files, still send one empty
    files.push({ type: fileFieldMapping.visaApplicantFiles, base64: '' })
  }

  // coloredPassport
  // Only send one Colored_passport_page1_copy file (prefer coloredPassport, fallback to coloredPassport2)
  if (data.coloredPassport instanceof File) {
    files.push({
      type: fileFieldMapping.coloredPassport,
      base64: await fileToBase64(data.coloredPassport),
    })
  } else if (data.coloredPassport2 instanceof File) {
    files.push({
      type: fileFieldMapping.coloredPassport,
      base64: await fileToBase64(data.coloredPassport2),
    })
  } else {
    files.push({ type: fileFieldMapping.coloredPassport, base64: '' })
  }

  // Check if there are validation errors or if everything is filled correctly
  const isValid = Object.keys(form.formState.errors).length === 0

  if (isValid && !manualProofOfPaymentError) {
    setIsLoading(true)
    setIsFormSubmitted(true)
    try {
      // Call the combined API to create application and upload files
      await submitVisaApplicationWithDocuments(payload, files)
      toast({
        title: 'Success!',
        description:
          'Your Visa Application and documents have been submitted successfully.',
        variant: 'success',
      })
    } catch (error) {
      toast({
        title: 'Error',
        description:
          'Failed to submit Visa Application or upload documents. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  } else {
    scrollToFirstErrorField()
    toast({
      title: 'Error',
      description: 'Please fill in all the required fields.',
      variant: 'destructive',
    })
  }

  // Utility to convert File to base64
  async function fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        resolve(reader.result as string)
      }
      reader.onerror = reject
      reader.readAsDataURL(file)
    })
  }
}

  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        {/* <TopNav links={topNav} /> */}
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Main ===== */}
      <Main>
        <div className='mb-2 flex items-center justify-between space-y-2'>
          <h1 className='text-2xl font-bold tracking-tight'>
            Visa Application
          </h1>
          {/* <div className='flex items-center space-x-2'>
            <Button>Download</Button>
          </div> */}
        </div>
        <ApplicationProgress currentStep={currentStep} type='visa' />

        {/* === Step 1 Content === */}
        {currentStep === 1 && (
          <Card className='mt-6'>
            <CardHeader className='pb-0'>
              <CardTitle>Basic Information ( Page 1 of 7 )</CardTitle>
            </CardHeader>
            <CardContent>
              <FormProvider {...form}>
                <Step1Form showEVisaType={showEVisaType} />
              </FormProvider>
            </CardContent>
          </Card>
        )}

        {/* === Step 2 Content === */}
        {currentStep === 2 && (
          <Card className='mt-6'>
            <CardHeader className='pb-0'>
              <CardTitle>Personal Details ( Page 2 of 7 )</CardTitle>
            </CardHeader>
            <CardContent>
              <FormProvider {...form}>
                <Step2Form date={date} setDate={setDate} />
              </FormProvider>
            </CardContent>
          </Card>
        )}

        {/* === Step 3 Content === */}
        {currentStep === 3 && (
          <>
            <Card className='mt-6'>
              <CardHeader className='pb-0'>
                <CardTitle>Passport Information ( Page 3 of 7 )</CardTitle>
              </CardHeader>
              <CardContent>
                <FormProvider {...form}>
                  <Step3Form setDate={setDate} />
                </FormProvider>
              </CardContent>
            </Card>
          </>
        )}

        {/* === Step 4 Content === */}
        {currentStep === 4 && (
          <>
            <Card className='mt-6'>
              <CardHeader className='pb-0'>
                <CardTitle>Employment Information ( Page 4 of 7 )</CardTitle>
              </CardHeader>
              <CardContent>
                <FormProvider {...form}>
                  <Step4Form
                    localValues={localValues}
                    setLocalValues={setLocalValues}
                    setDate={setDate}
                  />
                </FormProvider>
              </CardContent>
            </Card>
          </>
        )}

        {/* === Step 5 Content === */}
        {currentStep === 5 && (
          <Card className='mt-6'>
            <CardHeader className='pb-0'>
              <CardTitle>
                Monthly Salary Breakdown (AED) ( Page 5 of 7 )
              </CardTitle>
            </CardHeader>
            <CardContent>
              <FormProvider {...form}>
                <Step5Form
                  localValues={localValues}
                  setLocalValues={setLocalValues}
                />
              </FormProvider>
            </CardContent>
          </Card>
        )}

        {/* === Step 6 Content === */}

        {currentStep === 6 && (
          <Card className='mt-6'>
            <CardHeader className='pb-0'>
              <CardTitle>Company Details ( Page 6 of 7 )</CardTitle>
            </CardHeader>
            <CardContent>
              <FormProvider {...form}>
                <Step6Form />
              </FormProvider>
            </CardContent>
          </Card>
        )}

        {/* === Step 7 Content === */}
        {currentStep === 7 && (
          <Card className='mt-6'>
            <CardHeader className='pb-0'>
              <CardTitle>Visa Application ( Page 7 of 7 )</CardTitle>
            </CardHeader>
            <CardContent>
              <FormProvider {...form}>
                <Step7Form />
              </FormProvider>
            </CardContent>
          </Card>
        )}

        {/* Buttons Section */}

        <div className='fixed bottom-0 right-0 p-3 pt-4 bg-background flex justify-end space-x-2 z-10 fz-form fz-form-btns'>
          <div className='flex-1'>
            {/* <Button variant={'btn_outline'}>Save as Draft</Button> */}
          </div>
          <div className='flex gap-2'>
            {/* The 'Previous' button will not appear on the first step */}
            {currentStep !== 1 && (
              <Button variant='btn_outline' onClick={prevStep}>
                Previous
              </Button>
            )}

            {/* The 'Next' button will not appear on the third step */}
            {currentStep !== 7 && (
              <Button variant='default' onClick={nextStep}>
                Next
              </Button>
            )}
            {/* The Submit button will appear only in step 7 */}
            {currentStep === 7 && (
              <Button
                variant={'default'}
                onClick={() => {
                  onSubmit(form.getValues())
                }}
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                    Processing...
                  </>
                ) : (
                  'Submit'
                )}
              </Button>
            )}
          </div>
        </div>
      </Main>
    </>
  )
}
