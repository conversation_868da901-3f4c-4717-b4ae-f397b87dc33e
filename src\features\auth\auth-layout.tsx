import IfzaLogo from '../../assets/images/Ifza_logo.svg';

interface Props {
  children: React.ReactNode
}

export default function AuthLayout({ children }: Props) {
  return (
    <div className='container grid h-svh flex-col items-center justify-center bg-primary-foreground lg:max-w-none lg:px-0'>
      <div className='mx-auto flex w-full flex-col justify-center space-y-2 sm:w-[480px] lg:p-8'>
        <div className='mb-4 flex items-center justify-center'>
        <div className='flex justify-center bg-logo'>
          <img src={IfzaLogo} alt="Logo" className='w-40 pt-4 pb-4 mx-auto' id="logo" />
        </div>
        </div>
        {children}
      </div>
    </div>
  )
}
