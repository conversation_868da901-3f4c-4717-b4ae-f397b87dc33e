import React from 'react'
import { useFormContext, useWatch } from 'react-hook-form'
import { FilePenLine } from 'lucide-react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import FileUploadField from '@/components/ui/fileUpload'
import FileUploadFieldMultiple from '@/components/ui/fileUploadMultiple'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Tooltip,
  TooltipTrigger,
  TooltipContent,
} from '@/components/ui/tooltip'
import { ApplicationFormValues } from '../../types/application-form-types'

const Step7VisaApplication: React.FC = () => {
  const form = useFormContext<ApplicationFormValues>()

  const preferredPaymentMethod = useWatch({
    control: form.control,
    name: 'preferredPaymentMethod',
  })
  const visaApplicationType = useWatch({
    control: form.control,
    name: 'visaApplicationType',
  })
  const visaType = useWatch({ control: form.control, name: 'visaType' })
  const residentVisaStamping = useWatch({
    control: form.control,
    name: 'residentVisaStamping',
  })
  const attestedDegree = useWatch({
    control: form.control,
    name: 'attestedDegree',
  })
  const noAttestedDegree = useWatch({
    control: form.control,
    name: 'noAttestedDegree',
  })
  const salaryChange = useWatch({ control: form.control, name: 'salaryChange' })

  // Log all form values for debugging
  console.log('Step 7 Form Values:', form.watch());

  return (
    <>
      <form className='space-y-4 fz-form'>
        <div className='flex flex-wrap space-between gap-y-4'>
          <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
            <FormField
              control={form.control}
              name='photoOfApplicant'
              render={({ field }) => (
                <FormItem data-error-field='photoOfApplicant'>
                  <FormLabel>
                    Photo of the applicant (passport size){' '}
                    <span className='text-red-500'>*</span>
                  </FormLabel>
                  <FormControl>
                    <FileUploadField
                      accept='.jpg,.jpeg,.png'
                      value={field.value}
                      onchoose={(file) => {
                        form.setValue('photoOfApplicant', file || '')
                        form.clearErrors('photoOfApplicant')
                      }}
                    />
                  </FormControl>
                  <FormDescription>Passport size photo.</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          {/* Visa Applicant Files */}
          <div className='flex-1'>
            <FormField
              control={form.control}
              name='visaApplicantFiles'
              render={({ field }) => (
                <FormItem data-error-field='visaApplicantFiles'>
                  <FormLabel>
                    Visa Applicant Files <span style={{ color: 'red' }}>*</span>{' '}
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <span
                          className='text-yellow-600 cursor-pointer '
                          style={{ fontSize: '1.1rem', lineHeight: '1' }}
                        >
                          🛈
                        </span>
                      </TooltipTrigger>
                      <TooltipContent className='max-w-xs text-sm alert-bg-warning border border-yellow-300 rounded shadow-lg p-2 text-black'>
                        Please upload the attachments like Passport Copy,
                        Passport Size Picture, Cancellation Document, Entry
                        Stamp Copy or Tourist Visa copy.
                      </TooltipContent>
                    </Tooltip>
                  </FormLabel>
                  <FormControl>
                    <FileUploadFieldMultiple
                      accept='.pdf,.jpg,.jpeg'
                      value={field.value || []}
                      onchoose={(files) => {
                        form.setValue('visaApplicantFiles', files || [])
                        form.clearErrors('visaApplicantFiles')
                      }}
                    />
                  </FormControl>

                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        <CardHeader className='px-0 pt-0 pb-0'>
          <CardTitle>Payments </CardTitle>
        </CardHeader>

        {/* Preferred Payment Method */}
        <div className='mb-4'>
          <FormField
            control={form.control}
            name='preferredPaymentMethod'
            render={({ field }) => (
              <FormItem data-error-field='preferredPaymentMethod'>
                <FormLabel className='flex items-center gap-1'>
                  Preferred Payment Method{' '}
                  <span className='text-red-500'>*</span>{' '}
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <span
                        className='text-yellow-600 cursor-pointer'
                        style={{ fontSize: '1.1rem', lineHeight: '1' }}
                      >
                        🛈
                      </span>
                    </TooltipTrigger>
                    <TooltipContent className='max-w-xs text-sm alert-bg-warning border border-yellow-300 rounded shadow-lg p-2 text-black'>
                      For more details, please visit : <br />
                      <a
                        href='https://ifza.com/payments'
                        target='_blank'
                        rel='noopener noreferrer'
                        className='underline text-blue-600 font-semibold'
                      >
                        ifza.com/payments
                      </a>
                    </TooltipContent>
                  </Tooltip>
                </FormLabel>
                <FormControl>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <SelectTrigger>
                      <SelectValue placeholder='Choose' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='Cash'>
                        Cash Payment (IFZA Offices only)
                      </SelectItem>
                      <SelectItem value='Cheque'>Cheque</SelectItem>
                      <SelectItem value='Bank Transfer'>
                        Bank Transfer
                      </SelectItem>
                      <SelectItem value='ATM'>
                        ATM
                      </SelectItem>
                      <SelectItem value='Online Payment'>
                        Online Payment
                      </SelectItem>
                      <SelectItem value='Already Paid with the License Application'>
                        Already Paid with the License Application
                      </SelectItem>
                      <SelectItem value='Key Account'>
                        Key Account
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormDescription>
                  Please select the preferred payment method from the list
                  below.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        {/* Proof of Payment */}

        {preferredPaymentMethod ===
          'Already Paid with the License Application' && (
          <>
            <div className='mb-4'>
              <FormField
                control={form.control}
                name='proofOfPayment'
                render={({ field }) => (
                  <FormItem data-error-field='proofOfPayment'>
                    <FormLabel>
                      Proof of Payment <span className='text-red-500'>*</span>
                    </FormLabel>
                    <FormControl>
                      <FileUploadField
                        accept='.pdf,.jpg,.jpeg,.png'
                        value={field.value}
                        onchoose={(file) => {
                          form.setValue('proofOfPayment', file || '')
                          form.clearErrors('proofOfPayment')
                        }}
                      />
                    </FormControl>
                    <FormDescription>
                      Please upload the proof of payment for the Visa
                      Application. If payment was made earlier with the License
                      application, you can also attach it. Without the proof of
                      payment we are unable to proceed with your application.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </>
        )}

        {salaryChange === 'Yes' && (
          <>
            <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
              <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
              </span>
              <div className='flex flex-col ml-2'>
                <AlertTitle>
                  Please note the following requirements :
                </AlertTitle>
                <AlertDescription>
                  <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    During visa renewal with a salary amendment, the employee
                    must provide a one-month bank statement showing the new
                    salary transfer. The statement must :
                  </p>
                  <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    Be under the employee's name
                  </p>
                  <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    Clearly indicate the word <strong>“Salary”</strong> in the
                    transaction details (generic deposits will not be accepted)
                  </p>
                </AlertDescription>
              </div>
            </Alert>

            {/* //partner field
            // <div className='mb-4'>
              <FormField
                control={form.control}
                name='salaryAmendmentBankStatement'
                render={({ field }) => (
                  <FormItem data-error-field='salaryAmendmentBankStatement'>
                    <FormLabel>
                      One-month Bank Statement Showing New Salary{' '}
                      <span className='text-red-500'>*</span>
                    </FormLabel>
                    <FormControl>
                      <FileUploadField
                        accept='.pdf,.jpg,.jpeg,.png'
                        value={field.value}
                        onchoose={(file) => {
                          form.setValue(
                            'salaryAmendmentBankStatement',
                            file || ''
                          )
                          form.clearErrors('salaryAmendmentBankStatement')
                        }}
                      />
                    </FormControl>
                    <FormDescription>
                      Please upload the employee’s bank statement that clearly
                      shows the new salary paid under the employee's name.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div> */}
          </>
        )}

        <CardHeader className='px-0 pt-0 pb-0'>
          <CardTitle>Payment Details</CardTitle>
        </CardHeader>

        <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
          <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
            <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
          </span>
          <div className='flex flex-col ml-2'>
            <AlertTitle>DISCLAIMER :</AlertTitle>
            <AlertDescription>
              <p className='text-sm text-slate-700 dark:text-slate-400 relative before:absolute before:left-0 before:top-0'>
                International transfers may take up to 7-10 working days. Till
                the payment is reflected, your application will be kept on hold.
              </p>
            </AlertDescription>
          </div>
        </Alert>

        {/* Visa Fee */}
        <div className='mb-4'>
          <FormField
            control={form.control}
            name='visaFee'
            render={({ }) => (
              <FormItem>
                <FormLabel>Visa Fee</FormLabel>
                <FormControl>
                  <div className='flex items-center rounded-md '>
                    <Input
                      type='number'
                      value={3750}
                      disabled
                      className='pr-20'
                      onChange={() => {}}
                    />
                    <span className='px-4 absolute right-[70px] bg-primary dark:bg-primary-dark text-white rounded-[12px]'>
                      AED
                    </span>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Status Change */}
        {visaApplicationType === 'In-Country' &&
          (visaType === '' ||
            (visaType !== 'Employment Visa Renewal' &&
              visaType !== 'Work Permit Renewal')) && (
            <div className='mb-4'>
              <FormField
                control={form.control}
                name='statusChange'
                render={({ }) => (
                  <FormItem>
                    <FormLabel>Status Change</FormLabel>
                    <FormControl>
                      <div className='flex items-center rounded-md'>
                        <Input
                          type='number'
                          value={1600}
                          disabled
                          className='pr-20'
                          onChange={() => {}}
                        />
                        <span className='px-4 absolute right-[70px] bg-primary dark:bg-primary-dark text-white rounded-[12px]'>
                          AED
                        </span>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          )}

        {/* VIP Stamping Fee */}
        {residentVisaStamping === 'VIP' && (
          <div className='mb-4'>
            <FormField
              control={form.control}
              name='vipStampingFee'
              render={({ }) => (
                <FormItem>
                  <FormLabel>VIP Stamping Fee</FormLabel>
                  <FormControl>
                    <div className='flex items-center rounded-md'>
                      <Input
                        type='number'
                        value={1500}
                        disabled
                        className='pr-20'
                        onChange={() => {}}
                      />
                      <span className='px-4 absolute right-[70px] bg-primary dark:bg-primary-dark text-white rounded-[12px]'>
                        AED
                      </span>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )}

        {/* Amendment Fee */}

        {(visaType !== 'Employment Visa Renewal' || salaryChange === 'Yes') && (
          <div className='mb-4'>
            <FormField
              control={form.control}
              name='amendmentFee'
              render={({ }) => (
                <FormItem>
                  <FormLabel>Amendment Fee</FormLabel>
                  <FormControl>
                    <div className='flex items-center rounded-md'>
                      <Input
                        type='number'
                        value={250}
                        disabled
                        className='pr-20'
                        onChange={() => {}}
                      />
                      <span className='px-4 absolute right-[70px] bg-primary dark:bg-primary-dark text-white rounded-[12px]'>
                        AED
                      </span>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )}

        {/* Partner/Investor Visa */}
        {(attestedDegree === 'Investor' ||
          noAttestedDegree === 'Investor' ||
          attestedDegree === 'Partner' ||
          noAttestedDegree === 'Partner') && (
          <div className='mb-4'>
            <FormField
              control={form.control}
              name='partnerInvestorVisa'
              render={({ }) => (
                <FormItem>
                  <FormLabel>Partner/Investor Visa</FormLabel>
                  <FormControl>
                    <div className='flex items-center rounded-md'>
                      <Input
                        type='number'
                        value={1000}
                        disabled
                        className='pr-20'
                        onChange={() => {}}
                      />
                      <span className='px-4 absolute right-[70px] bg-primary dark:bg-primary-dark text-white rounded-[12px]'>
                        AED
                      </span>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )}

        {/* Total Amount to be Paid */}
        <div className='mb-4'>
          <FormField
            control={form.control}
            name='totalAmountToBePaid'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Total Amount to be Paid</FormLabel>
                <FormControl>
                  <div className='flex items-center rounded-md'>
                    <Input
                      type='number'
                      value={field.value}
                      disabled
                      className='pr-20'
                      onChange={() => {}}
                    />
                    <span className='px-4 absolute right-[70px] bg-primary dark:bg-primary-dark text-white rounded-[12px]'>
                      AED
                    </span>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <Alert className='border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
          <AlertDescription>
            This is the amount due for the Visa Application as per the selection
            of the required services. Please proceed to{' '}
            <a
              href='https://ifza.com/en/payments/'
              target='_blank'
              rel='noopener noreferrer'
              className='text-blue-500 underline'
            >
              https://ifza.com/en/payments/{' '}
            </a>
            for information on different payment methods available.
          </AlertDescription>
        </Alert>

        <CardHeader className='px-0 pt-0 pb-0'>
          <CardTitle>Important Information</CardTitle>
        </CardHeader>

        <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
          <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
            <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
          </span>
          <div className='flex flex-col ml-2'>
            <AlertDescription>
              <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                The General Directorate of Residency and Foreign Affairs – Dubai
                have updated the guidelines on Personal Photo Specifications for
                all GDRFA applications effective immediately.
              </p>
              <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                Detailed Guides can be found at{' '}
                <a
                  href='https://beta.smartservices.ica.gov.ae/echannels/web/client/manual/icao/icao_english.pdf'
                  target='_blank'
                  rel='noopener noreferrer'
                  className='text-blue-500 underline'
                >
                  this link
                </a>
              </p>
            </AlertDescription>
          </div>
        </Alert>

        {/* Name of the person completing the application form */}

        <div className='flex-1'>
          <div className='flex items-center space-x-2'>
            <div className='flex flex-col w-full'>
              <FormLabel>
                Name of the person completing the application form
              </FormLabel>
              <div className='flex space-x-2 mt-4'>
                <div className='w-full'>
                  <FormField
                    control={form.control}
                    name='firstName'
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input {...field} placeholder='First Name' />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className='w-full'>
                  <FormField
                    control={form.control}
                    name='lastName'
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input {...field} placeholder='Last Name' />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <FormField
          control={form.control}
          name='affirmInformation'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-start space-x-2 border border-dashed border-yellow-400 rounded-md p-4 bg-yellow-50 dark:bg-primary/20'>
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={(checked: boolean) =>
                      field.onChange(checked)
                    }
                    className='mt-1'
                    data-field='affirmInformation'
                  />
                </FormControl>
                <FormLabel className='text-base text-primary dark:text-primary-light'>
                  I affirm that all the information provided above is accurate
                  and true. I acknowledge that any delays or rejections
                  resulting from inaccuracies in the information are my sole
                  responsibility.
                </FormLabel>
              </div>

              <FormMessage />
            </FormItem>
          )}
        />
      </form>
    </>
  )
}

export default Step7VisaApplication
