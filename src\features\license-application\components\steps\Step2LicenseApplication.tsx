import React from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Form, FormField, FormItem, FormLabel, FormControl, FormDescription, FormMessage } from '@/components/ui/form';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import { DateTimePicker } from '@/components/ui/datetime-picker';
import { Lightbulb, FilePenLine } from 'lucide-react';

interface BusinessActivitiesProps {
  form: any;
  isDachSales: boolean;
}

const Step2LicenseApplication: React.FC<BusinessActivitiesProps> = ({ form, isDachSales }) => {
  return (
    <Card className="mt-6">
      <CardContent>
        <Form {...form}>
          <form className="space-y-4 fz-form">
            <CardHeader className="px-0 pt-0 pb-0">
              <CardTitle>
                Select the License Type and Visa Package for your Business ( Step 2 of 6 )
              </CardTitle>
            </CardHeader>

            {/* Trade License Validity */}
            <FormField
              control={form.control}
              name="tradeLicenseValidity"
              render={({ field }: any) => (
                <FormItem>
                  <FormLabel>
                    {isDachSales ? 'Package Type' : 'Trade License Validity'}{' '}
                    <span className="text-red-500">*</span>
                  </FormLabel>
                  <div data-field="tradeLicenseValidity">
                    <FormControl>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <SelectTrigger>
                          <SelectValue placeholder={isDachSales ? 'Select Package Type' : 'Select'} />
                        </SelectTrigger>
                        <SelectContent>
                          {isDachSales ? (
                            <>
                              <SelectItem value='Silver'>Silver</SelectItem>
                              <SelectItem value='Gold'>Gold</SelectItem>
                              <SelectItem value='Platinaum'>Platinaum</SelectItem>
                              <SelectItem value='Standard'>Standard</SelectItem>
                              <SelectItem value='Self Service'>Self Service</SelectItem>
                              <SelectItem value='Professional'>Professional</SelectItem>
                            </>
                          ) : (
                            <>
                              <SelectItem value="1 Year">1 Year</SelectItem>
                              <SelectItem value="2 Years">2 Years</SelectItem>
                              <SelectItem value="3 Years">3 Years</SelectItem>
                              <SelectItem value="4 Years">4 Years</SelectItem>
                              <SelectItem value="5 Years">5 Years</SelectItem>
                            </>
                          )}
                        </SelectContent>
                      </Select>
                    </FormControl>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Visa Package */}
            <FormField
              control={form.control}
              name="visaPackage"
              render={({ field }: any) => (
                <FormItem>
                  <FormLabel>
                    {isDachSales ? 'Additional Visas' : 'Visa Package'}{' '}
                    <span className="text-red-500">*</span>
                  </FormLabel>
                  <div data-field="visaPackage">
                    <FormControl>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <SelectTrigger>
                          <SelectValue placeholder={isDachSales ? 'Select Additional Visas' : 'Select Visa Package'} />
                        </SelectTrigger>
                        <SelectContent>
                          {isDachSales ? (
                            <>
                              <SelectItem value='0 Visa'>0 Visa</SelectItem>
                              <SelectItem value='1 Visa'>1 Visa</SelectItem>
                              <SelectItem value='2 Visa'>2 Visa</SelectItem>
                              <SelectItem value='3 Visa'>3 Visa</SelectItem>
                              <SelectItem value='4 Visa'>4 Visa</SelectItem>
                              <SelectItem value='5 Visa'>5 Visa</SelectItem>
                              <SelectItem value='6 Visa'>6 Visa</SelectItem>
                              <SelectItem value='7 Visa'>7 Visa</SelectItem>
                              <SelectItem value='More than 7 Visas'>
                                More than 7 Visas
                              </SelectItem>
                            </>
                          ) : (
                            <>
                              <SelectItem value="0 Visa">0 Visa</SelectItem>
                              <SelectItem value="1 Visa">1 Visa</SelectItem>
                              <SelectItem value="2 Visa">2 Visa</SelectItem>
                              <SelectItem value="3 Visa">3 Visa</SelectItem>
                              <SelectItem value="4 Visa">4 Visa</SelectItem>
                              <SelectItem value="5 Visa">5 Visa</SelectItem>
                              <SelectItem value="6 Visa">6 Visa</SelectItem>
                              <SelectItem value="7 Visa">7 Visa</SelectItem>
                              <SelectItem value="8 Visa">8 Visa</SelectItem>
                              <SelectItem value="9 Visa">9 Visa</SelectItem>
                              <SelectItem value="10 Visa">10 Visa</SelectItem>
                              <SelectItem value="More than 10 Visas">More than 10 Visas</SelectItem>
                            </>
                          )}
                        </SelectContent>
                      </Select>
                    </FormControl>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Do you want to include Visa cost in License Estimate/Quote?  */}
            <FormField
              control={form.control}
              name="includeVisaCostInQuote"
              render={({ field }: any) => (
                <FormItem>
                  <FormLabel>
                    Do you want to include Visa cost in License Estimate/Quote ?{' '}
                    <span className="text-red-500">*</span>
                  </FormLabel>
                  <div data-field="includeVisaCostInQuote">
                    <FormControl>
                      <RadioGroup
                        className="flex align-center"
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <RadioGroupItem className="mt-1" value="Yes" id="yes" />
                        <label htmlFor="yes">Yes</label>
                        <RadioGroupItem className="mt-1" value="No" id="no" />
                        <label htmlFor="no">No</label>
                      </RadioGroup>
                    </FormControl>
                  </div>
                  <FormDescription>
                    The cost of residence visa will be included in the quotation if you select yes.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Number of Inside Country Visas */}
            {form.watch('includeVisaCostInQuote') === 'Yes' && (
              <FormField
                control={form.control}
                name="numberOfInsideCountryVisas"
                render={({ field }: any) => (
                  <FormItem>
                    <FormLabel>
                      No. of Inside Country Visas <span className="text-red-500">*</span>
                    </FormLabel>
                    <div data-field="numberOfInsideCountryVisas">
                      <FormControl>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select number of visas" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="0">0</SelectItem>
                            <SelectItem value="1">1</SelectItem>
                            <SelectItem value="2">2</SelectItem>
                            <SelectItem value="3">3</SelectItem>
                            <SelectItem value="4">4</SelectItem>
                            <SelectItem value="5">5</SelectItem>
                            <SelectItem value="6">6</SelectItem>
                            <SelectItem value="7">7</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* Alert for visa package >= 4 */}
            {(form.watch('visaPackage') === '4 Visa' ||
              form.watch('visaPackage') === '5 Visa' ||
              form.watch('visaPackage') === '6 Visa' ||
              form.watch('visaPackage') === '7 Visa') && (
              <Alert className="flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20">
                <span className="bg-primary/10 p-2 rounded inline-block dark:text-primary-dark">
                  <FilePenLine className="w-4 h-4 stroke-primary dark:text-primary-dark" />
                </span>
                <div className="flex flex-col ml-2">
                  <AlertTitle>NOTE :</AlertTitle>
                  <AlertDescription>
                    Please note that a licensee is required to lease exclusive office space within an IFZA Building in order to obtain a visa package with an entitlement to four (4) or more visas.
                  </AlertDescription>
                </div>
              </Alert>
            )}

            {/* Establishment Card */}
            <FormField
              control={form.control}
              name="establishmentCard"
              render={({ field }: any) => (
                <FormItem>
                  <FormLabel>
                    Establishment Card <span className="text-red-500">*</span>
                  </FormLabel>
                  <div data-field="establishmentCard">
                    <FormControl>
                      <RadioGroup
                        className="flex align-center"
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <RadioGroupItem className="mt-1" value="Yes" id="establishment_yes" />
                        <label htmlFor="establishment_yes">Yes</label>
                        <RadioGroupItem className="mt-1" value="No" id="establishment_no" />
                        <label htmlFor="establishment_no">No</label>
                      </RadioGroup>
                    </FormControl>
                  </div>
                  <FormDescription>
                    Please let us know if you would like to apply for the Establishment Card, this can be applied at a later stage as well. It is advisable to apply for it the same time as the license.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Alert className="flex items-start border-dashed border-blue-500 mt-2 bg-slate-50 dark:bg-primary/20">
              <span className="bg-blue-50 p-2 rounded inline-block dark:text-primary-dark">
                <Lightbulb className="w-4 h-4 stroke-blue-500 dark:text-primary-dark" />
              </span>
              <div className="flex space-y-2 flex-col ml-3">
                <AlertDescription>
                  <p className="text-sm text-slate-800 dark:text-slate-400">
                    Establishment card is necessary once you apply for the Visas under this company, it is also required once you apply for any utilities/phone lines etc.
                  </p>
                  <p className="text-sm text-slate-800 dark:text-slate-400">
                    It is your choice, if you would like to apply for the establishment card at the same time as the license or at a later stage.
                  </p>
                </AlertDescription>
              </div>
            </Alert>

            {/* Financial Year Details */}
            <CardHeader className="px-0 pt-4 pb-0">
              <CardTitle>Financial Year Details</CardTitle>
            </CardHeader>
            <div className="flex flex-wrap space-between gap-2">
              <div className="w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0">
                <FormField
                  control={form.control}
                  name="financialYearStartDate"
                  render={({ field }: any) => (
                    <FormItem>
                      <FormLabel>Financial Year Start Date</FormLabel>
                      <DateTimePicker
                        granularity="day"
                        value={field.value || new Date()}
                        onChange={field.onChange}
                        displayFormat={{ hour24: 'dd MMMM yyyy' }}
                        disabled={true}
                      />
                      <FormDescription>dd-MMM-yyyy</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="w-full sm:w-[49%]">
                <FormField
                  control={form.control}
                  name="financialYearEndDate"
                  render={({ field }: any) => (
                    <FormItem>
                      <FormLabel>Financial Year End Date</FormLabel>
                      <DateTimePicker
                        granularity="day"
                        value={field.value}
                        onChange={field.onChange}
                        displayFormat={{ hour24: 'dd MMMM yyyy' }}
                      />
                      <FormDescription>dd-MMM-yyyy</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default Step2LicenseApplication;
