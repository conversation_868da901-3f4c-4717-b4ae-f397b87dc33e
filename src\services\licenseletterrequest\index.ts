// import { LicenceLetterRequestApplicationFormValues } from "@/features/license-letter-request";
import { BASE_URL } from "@/utils/network";
import axios from "axios";

function capitalize(s: string) {
    return String(s[0]).toUpperCase() + String(s).slice(1);
}

type ShareholderAddress = {
    Address_Line_1: string
    Address_Line_2: string
    City: string
    Counrty: string
}
type ServerData = {
    Trade_License_Number: string,
    Registered_Company_Name: string
    Payment_Method: string
    // contact information
    Title: string;
    First_Name: string;
    Last_Name: string;
    Email: string;
    Type: string
    Is_the_Registry_Extract_document_issued: string
    Language_of_Letter: string
    Letter_Addressed_to: string
    Service_Type: string;
    Please_confirm_if_DSOA_MOA_was_issued_previously: string
    Purpose_of_the_NOC: string;
    Name_of_Developer: string;
    Property_Unit_Number: string
    phone: string;
    App_Update_WhatsApp_Number: string;

    Car_Make: string,
    Car_Model: string
    VIN_Number: string
    Car_Color: string
    Car_Engine_Number: string
    Year_of_manufacture: string
    Country_of_Car_Manufacturer: string
    Details: string
    How_many_shareholder_s_the_company_has: string
    Shareholders_address: Array<ShareholderAddress>
    noofShareholders?: string
    MOFA_Inside: boolean
    Attested_for_Inside_UAE: boolean
    Number_of_Copies: string
}


export const mainpulateLicenseLetterRequestData = (data: any) => {

    const formData = new FormData()
    let preferredPaymentOption = data.preferredPaymentOption
    if (preferredPaymentOption == "online") {
        preferredPaymentOption = "Online Payment"
    }
    if (preferredPaymentOption == "cash") {
        preferredPaymentOption = "Cash"
    }
    if (preferredPaymentOption == "cheque") {
        preferredPaymentOption = "Cheque"
    }
    if (preferredPaymentOption == "atm") {
        preferredPaymentOption = "ATM"
    }

    let mainpulatedData: Partial<ServerData> = {
        Trade_License_Number: data.tradeLicenseNumber!,
        Registered_Company_Name: data.registeredCompanyName!,
        Payment_Method: preferredPaymentOption,
        Title: data.title!,
        First_Name: data.firstName!,
        Last_Name: data.lastName!,
        Email: data.email!,
        Type: data.lettersDocumentsRequired!,
        App_Update_WhatsApp_Number: data.mobileNumber!,
    }


    if (mainpulatedData.Type === "Certificate of Incumbency" || mainpulatedData.Type == "Certified True Copy - Registry Extract" || mainpulatedData.Type == "Letter of Good Standing") {
        mainpulatedData = {
            ...mainpulatedData,
            Is_the_Registry_Extract_document_issued: capitalize(data.registryExtractIssued!),
            Language_of_Letter: data.languageOfLetter!,
            Service_Type: data.servicetype!,
            Letter_Addressed_to: data.registaryAddressedTo
        }
    }
    if (mainpulatedData.Type === "Certificate of Name change") {
        mainpulatedData = {
            ...mainpulatedData,
            Letter_Addressed_to: data.letterAddressedTo!,
        }
    }
    if (mainpulatedData.Type === "Certified True Copy - MOA") {
        mainpulatedData = {
            ...mainpulatedData,
            Service_Type: data.servicetype!,
            Please_confirm_if_DSOA_MOA_was_issued_previously: capitalize(data.moaIssuedPreviously!),
        }
    }

    if( mainpulatedData.Type === "Certified True Copy - Certificate of Formation") {
        mainpulatedData = {
            ...mainpulatedData,
            Letter_Addressed_to: data.letterAddressedTo!,
        }
    }
    if (mainpulatedData.Type === "Certified True Copy - Certificate of Continuation") {
        mainpulatedData = {
            ...mainpulatedData,
            Letter_Addressed_to: data.letterAddressedTo!,
        }
    }
    if (mainpulatedData.Type === "Certified True Copy - Certificate of Incorporation") {
        mainpulatedData = {
            ...mainpulatedData,
            Letter_Addressed_to: data.letterAddressedTo!,
        }
    }
    if (mainpulatedData.Type === "Company Stamp") {
        mainpulatedData = {
            ...mainpulatedData,
            Service_Type: data.serviceType == "regular" || data.serviceType == "personalized" ? capitalize(data.serviceType) : data.serviceType!,
        }
    }
    if (mainpulatedData.Type === "Copy of COF/COC") {
        mainpulatedData = {
            ...mainpulatedData,
            Letter_Addressed_to: data.letterAddressedTo!,
        }
    }
    if (mainpulatedData.Type === "Copy of Original CONC (Certificate of Name Change)") {
        mainpulatedData = {
            ...mainpulatedData,
            Letter_Addressed_to: data.letterAddressedTo!,
        }
    }
    if (mainpulatedData.Type === "Copy of ULA") {
        mainpulatedData = {
            ...mainpulatedData,
            Letter_Addressed_to: data.letterAddressedTo!,
        }
    }


    if (mainpulatedData.Type == "IFZA Letters & NOCs") {

        mainpulatedData = {
            ...mainpulatedData,
            Letter_Addressed_to: data.letterAddressedTo!,
            Service_Type: data.servicetype!,
            Purpose_of_the_NOC: data.purposeOfNOC!,
        }

        if (mainpulatedData.Purpose_of_the_NOC == "To buy/own Property") {
            mainpulatedData = {
                ...mainpulatedData,
                Letter_Addressed_to: data.letterAddressedTo!,
                Purpose_of_the_NOC: data.purposeOfNOC!,
                Name_of_Developer: data.developerName!,
                Property_Unit_Number: data.propertyUnitNumber!
            }
        }

        if (mainpulatedData.Purpose_of_the_NOC == "RTA - To buy/ register car under the company" || mainpulatedData.Purpose_of_the_NOC == "RTA - To buy/ register car under the company") {
            mainpulatedData = {
                ...mainpulatedData,
                Car_Make: data.carMake!,
                Car_Model: data.carModel!,
                VIN_Number: data.vinNumber!,
                Car_Color: data.carColor!,
                Car_Engine_Number: data.carEngineNumber!,
                Year_of_manufacture: data.yearOfManufacture!,
                Country_of_Car_Manufacturer: data.countryOfOrigin!
            }
        }

        if (mainpulatedData.Purpose_of_the_NOC == "Others") {
            mainpulatedData = {
                ...mainpulatedData,
                Details: data.details!
            }
        }

    }

    if (mainpulatedData.Type == "Memorandum & Articles of Association - Hard Copy") {
        mainpulatedData = {
            ...mainpulatedData,
            Letter_Addressed_to: data.letterAddressedTo!,
        }
    }

    if (mainpulatedData.Type == "NOC - For a company member to be a Shareholder in another Freezone/Mainland") {
        mainpulatedData = {
            ...mainpulatedData,
            Letter_Addressed_to: data.letterAddressedTo!,
        }
    }

    if (mainpulatedData.Type == "NOC - For the IFZA company to be a Shareholder in another Freezone/Mainland") {
        mainpulatedData = {
            ...mainpulatedData,
            Letter_Addressed_to: data.letterAddressedTo!,
        }
    }


    if (mainpulatedData.Type == "Registry Extract") {
        mainpulatedData = {
            ...mainpulatedData,
            Letter_Addressed_to: data.letterAddressedTo!,
            Service_Type: data.servicetype!,
            Language_of_Letter: data.languageOfLetter!,
        }
    }

    if (mainpulatedData.Type == "Set of Incorporation Documents") {
        mainpulatedData = {
            ...mainpulatedData,
            Letter_Addressed_to: data.letterAddressedTo!,
        }
    }
    // shareholdersCount
    if (mainpulatedData.Type == "Share Certificate") {
        // shareholdersCount
        // numberOfShareholders
        mainpulatedData = {
            ...mainpulatedData,
            How_many_shareholder_s_the_company_has: data.shareholdersCount?.toString(),
            noofShareholders: data.numberOfShareholders?.toString(),
            Shareholders_address: data.shareholderAddresses.map((item: any)=>({
                Address_Line_1: item.addressLine1, 
                Address_Line_2: item.addressLine2, 
                City: item.city, 
                Counrty: item.country
            }))
            
        }
    }

    console.log("data.typeOfCourierService", data.typeOfCourierService)
    let serviceType = "";
    if(data.typeOfCourierService == "Express") {
        serviceType = "VIP"
    }
    if(data.typeOfCourierService == "Regular" || data.typeOfCourierService == "Reqular") {
        serviceType = "Standard"
        
    }
    if(data.typeOfCourierService == "Next Day Delivery") {
        serviceType = "Next Day Delivery"
    }
   

    if (mainpulatedData.Type == "MOFA Attestation") {
        mainpulatedData = {
            ...mainpulatedData,
            Service_Type: serviceType,
            MOFA_Inside: data.documentIssueLocation === "Issued Inside UAE (IFZA Documents)"? true : false,
            Attested_for_Inside_UAE	: data.isMoaAoa == "Yes" ? true : false,
            Number_of_Copies: data.numDocuments?.toString(),
        }
    }
   
    if(data?.shareCapitalLetter) {
        formData.append("Share Certificate", data.shareCapitalLetter)
    }
    if(data?.attestedDocument) {
        formData.append("General Document Attestation", data.attestedDocument)
    }
    if(data?.companyStampFile) {
        formData.append("Company Stamp", data.companyStampFile)
    }


    if(data.uploadCorporateDocuments1) {
        formData.append("MOFA Attestation", data.uploadCorporateDocuments1)
    }
    if(data.uploadCorporateDocuments2) {
        formData.append("MOFA Attestation", data.uploadCorporateDocuments2)
    }
    if(data.uploadCorporateDocuments3) {
        formData.append("MOFA Attestation", data.uploadCorporateDocuments3)
    }
    if(data.uploadCorporateDocuments4) {
        formData.append("MOFA Attestation", data.uploadCorporateDocuments4)
    }
    if(data.uploadCorporateDocuments5) {
        formData.append("MOFA Attestation", data.uploadCorporateDocuments5)
    }
    if(data.uploadCorporateDocuments6) {
        formData.append("MOFA Attestation", data.uploadCorporateDocuments6)
    }
    if(data.uploadCorporateDocuments7) {
        formData.append("MOFA Attestation", data.uploadCorporateDocuments7)
    }

    return {
       feilds: mainpulatedData,
       formData
    }
    // Object.keys(mainpulatedData).forEach((key) => {
    //     if(key == "Shareholders_address") {
    //         formData.append(key, JSON.stringify(mainpulatedData[key as keyof ServerData]))
    //     } else {
    //         formData.append(key, mainpulatedData[key as keyof ServerData] as string)
    //     }
    // })

    // if(data?.shareCapitalLetter) {
    //     formData.append("file", data.shareCapitalLetter)
    // }
    // if(data?.attestedDocument) {
    //     formData.append("file", data.attestedDocument)
    // }
    // if(data?.companyStampFile) {
    //     formData.append("file", data.companyStampFile)
    // }
    // return formData
}
export function createLicenseLetterRequest(bodyFromData: any, onSucess: (res: any) => void, onError: (err: any) => void) {
    const enpointUrl = `${BASE_URL}api/licenseRequests/create`
    axios({
        method: "POST",
        url: enpointUrl,
        data: bodyFromData,
        // headers: { "Content-Type": "multipart/form-data" }
    }).then((res) => {
        onSucess(res.data)
    }).catch((err) => {
        onError(err)
    })
}

export function createLicenseLetterRequestFiles(id: any ,bodyFromData: any, onSucess: (res: any) => void, onError: (err: any) => void) {
    const enpointUrl = `${BASE_URL}api/licenseRequests/uplaodFile/${id}`
    axios({
        method: "POST",
        url: enpointUrl,
        data: bodyFromData,
        headers: { "Content-Type": "multipart/form-data" }
    }).then((res) => {
        onSucess(res)
    }).catch((err) => {
        onError(err)
    })
}