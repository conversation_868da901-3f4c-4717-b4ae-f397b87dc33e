import React, { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { SidebarTrigger } from '@/components/ui/sidebar'
import IfzaLogo from '../../assets/images/Ifza_logo.svg'
import IfzaLogoWhite from '../../assets/images/Ifza_logoWhite.png'
import IfzaLogoWithoutText from '../../assets/images/ifza-logo_withoutText.svg'

interface HeaderProps extends React.HTMLAttributes<HTMLElement> {
  fixed?: boolean
  ref?: React.Ref<HTMLElement>
  
}

export const Header = ({
  className,
  fixed,
  children,
  ...props
}: HeaderProps) => {
  const [offset, setOffset] = useState(0)
  const [logoSrc, setLogoSrc] = useState(IfzaLogo)

  useEffect(() => {
    const updateLogo = () => {
      const htmlTag = document.querySelector('html')
      if (htmlTag && htmlTag.classList.contains('dark')) {
        setLogoSrc(IfzaLogoWhite)
      } else {
        setLogoSrc(IfzaLogo)
      }
    }

    // Initial check
    updateLogo()

    // Observe HTML class changes for theme switch
    const observer = new MutationObserver(updateLogo)
    const htmlTag = document.querySelector('html')
    if (htmlTag) {
      observer.observe(htmlTag, { attributes: true, attributeFilter: ['class'] })
    }

    return () => {
      if (htmlTag) {
        observer.disconnect()
      }
    }
  }, [])

  // Detect external link by presence of 'id' query param
  const externalId = typeof window !== 'undefined'
    ? new URLSearchParams(window.location.search).get('id')
    : null
  // Do not render header for external links
  if (externalId) return null

  React.useEffect(() => {
    const onScroll = () => {
      setOffset(document.body.scrollTop || document.documentElement.scrollTop)
    }

    // Add scroll listener to the body
    document.addEventListener('scroll', onScroll, { passive: true })

    // Clean up the event listener on unmount
    return () => document.removeEventListener('scroll', onScroll)
  }, [])

  return (
    <header
      className={cn(
        'flex items-center gap-3 sm:gap-4 bg-header p-4 h-18',
        fixed && 'header-fixed peer/header w-[inherit] fixed z-50 rounded-md',
        offset > 10 && fixed ? 'shadow' : 'shadow-none',
        className
      )}
      {...props}
    >
      {/* Mobile logo on the left */}
      <div className="mr-3 sm:hidden bg-logo">
        <img src={logoSrc} alt="Logo" className='w-32 mx-auto' id="logo" />
        <img src={IfzaLogoWithoutText} alt="Collapsed Logo" className='w-8 pt-4 pb-4 mx-auto hidden' id="logo-collapsed" />
      </div>
      {/* Only show sidebar toggle when not an external link */}
      {!externalId && <SidebarTrigger variant='outline' className='scale-125 sm:scale-100' />}
      {/* <Separator orientation='vertical' className='h-6' /> */}
      {children}
    </header>
  )
}

Header.displayName = 'Header'
