"use client";
import React, { useCallback, useState, forwardRef, useEffect } from "react";

// shadcn
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

// utils
import { cn } from "@/lib/utils";

// icons & assets
import { ChevronDown, CheckIcon, Globe, X } from "lucide-react";
import { CircleFlag } from "react-circle-flags";

// data

import { countries } from "country-data-list";

// Custom country name overrides
export const countryNameOverrides: Record<string, string> = {
   "åland islands": "Aland Islands",
  "aruba": "Aruban",
  "belgium (dutch)": "Belgium (Dutch)",
  "belgium (french)": "Belgium (French)",
  "bosnia & herzegovina": "Bosnia and Herzegovina",
  "british antarctic territory": "British Indian Ocean Territory",
  "brunei darussalam": "Brunei",
  "cabo verde": "Cape Verde",
  "french southern and antarctic territories": "French Southern Territories",
  "guinea-bissau": "GuineaBissau",
  "north macedonia": "Macedonia",
  "republic of south sudan": "South Sudan",
  "switzerland": "Switzerland(French)",
  "democratic republic of congo": "The Democratic Republic of Congo",
  "syrian arab republic": "Syria",
  "vietnam": "VietNam",
  "venezuela, bolivarian republic of": "Venezuela",
  "virgin islands (us)": "Virgin Islands",
  "virgin islands (british)": "British Virgin Islands",
  "yemen": "Yemen",
  "iran, islamic republic of": "Iran",
  "luxembourg": "Luxembourg(French)",
  "serbia and montenegro": "Serbia and Montenegro",
  "côte d'ivoire": "Ivory Coast",
  "myanmar": "Burma",
  "lao people's democratic republic": "Laos",
  "korea, democratic people's republic of": "North Korea",
  "korea, republic of": "South Korea",
  "tanzania, united republic of": "Tanzania",
  "timor-leste, democratic republic of": "East Timor",
  "micronesia, federated states of": "Micronesia",
  "macao": "Macao",
  "palestinian territory, occupied": "Palestine",
  "republic of congo": "Congo",
  "viet-nam, democratic republic of": "Vietnam",
  "bolivia, plurinational state of": "Bolivia",
  "russian federation": "Russia",
  "vatican city state": "Vatican",
  "são tome and principe": "Sao Tome and Principe",
  "eswatini": "Swaziland",
  "united states minor outlying islands": "United States Minor Outlying Islands",
  "trinidad and tobago": "Trinidad and Tobago",
  "united arab emirates": "United Arab Emirates",
  "united kingdom": "United Kingdom",
  "united states": "United States",
  "czechia": "Czech Republic"
};

// Apply overrides to countries list (case-insensitive match)
const allCountries = countries.all.map((country: Country) => {
  const override = countryNameOverrides[country.name.trim().toLowerCase()];
  return override ? { ...country, name: override } : country;
});

// Country interface
export interface Country {
  alpha2: string;
  alpha3: string;
  countryCallingCodes: string[];
  currencies: string[];
  emoji?: string;
  ioc: string;
  languages: string[];
  name: string;
  status: string;
}

// Props for single-select dropdown
type SingleSelectProps = {
  options?: Country[];
  multiple?: false;
  defaultValue?: string;
  onChange?: (country: Country) => void;
  disabled?: boolean;
  placeholder?: string;
  slim?: boolean;
};

// Props for multi-select dropdown
type MultiSelectProps = {
  options?: Country[];
  multiple: true;
  defaultValue?: string[];
  onChange?: (countries: Country[]) => void;
  disabled?: boolean;
  placeholder?: string;
  slim?: boolean;
};

export type CountryDropdownProps = SingleSelectProps | MultiSelectProps;

const CountryDropdownComponent = (
  props: CountryDropdownProps,
  ref: React.ForwardedRef<HTMLButtonElement>
) => {
  const {
    options = allCountries.filter(
      (country: Country) =>
        country.emoji &&
        country.status !== "deleted" &&
        country.ioc !== "PRK" &&
        country.name !== "Ceuta" &&
        country.name !== "Melilla"
    ),
    defaultValue,
    disabled = false,
    placeholder = "Select a country",
    slim = false,
    multiple = false,
    onChange,
    ...triggerProps
  } = props;

  const [open, setOpen] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState<Country | undefined>(undefined);
  const [selectedCountries, setSelectedCountries] = useState<Country[]>([]);

  useEffect(() => {
    if (!multiple && defaultValue) {
      const selected = options.find((country) => country.name === defaultValue);
      setSelectedCountry(selected);
    }
  }, [defaultValue, multiple, options]);

  const handleSelect = useCallback(
    (country: Country) => {
      if (multiple) {
        setSelectedCountries((prev) => {
          const exists = prev.find((c) => c.name === country.name);
          const newSelection = exists
            ? prev.filter((c) => c.name !== country.name)
            : [...prev, country];
          (onChange as (countries: Country[]) => void)?.(newSelection);
          return newSelection;
        });
      } else {
        setSelectedCountry(country);
        (onChange as (country: Country) => void)?.(country);
        setOpen(false);
      }
    },
    [onChange, multiple]
  );

  const handleRemoveCountry = useCallback(
    (country: Country, e: React.MouseEvent) => {
      e.stopPropagation();
      setSelectedCountries((prev) => {
        const newSelection = prev.filter((c) => c.name !== country.name);
        (onChange as (countries: Country[]) => void)?.(newSelection);
        return newSelection;
      });
    },
    [onChange]
  );

  const triggerClasses = cn(
    "flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",
    slim === true && "w-20"
  );

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger
        ref={ref}
        className={triggerClasses}
        disabled={disabled}
        {...(triggerProps as React.ButtonHTMLAttributes<HTMLButtonElement>)}
      >
        {multiple ? (
          selectedCountries.length > 0 ? (
            <div className="flex flex-wrap gap-1 max-w-full overflow-hidden">
              {selectedCountries.map((c) => (
                <div
                  key={c.name}
                  className="inline-flex items-center gap-1 px-2 py-0.5 rounded bg-muted text-muted-foreground cursor-default select-none max-w-full border border-border"
                  style={{ minWidth: 0, maxWidth: 150 }}
                >
                  <div
                    className="inline-flex items-center justify-center flex-shrink-0 overflow-hidden rounded-full"
                    style={{ width: 20, height: 20 }}
                  >
                    <CircleFlag
                      countryCode={c.alpha2.toLowerCase()}
                      height={20}
                    />
                  </div>
                  {slim === false && (
                    <span
                      className="overflow-hidden text-ellipsis whitespace-nowrap"
                      style={{ maxWidth: 100, fontSize: 14, lineHeight: 1 }}
                    >
                      {c.name}
                    </span>
                  )}
                  <X
                    size={12}
                    className="cursor-pointer"
                    onClick={(e) => handleRemoveCountry(c, e)}
                  />
                </div>
              ))}
            </div>
          ) : (
            <span>{placeholder}</span>
          )
        ) : selectedCountry ? (
          <div className="flex items-center flex-grow w-0 gap-2 overflow-hidden">
            <div className="inline-flex items-center justify-center w-5 h-5 shrink-0 overflow-hidden rounded-full">
              <CircleFlag
                countryCode={selectedCountry.alpha2.toLowerCase()}
                height={20}
              />
            </div>
            {slim === false && (
              <span className="overflow-hidden text-ellipsis whitespace-nowrap">
                {selectedCountry.name}
              </span>
            )}
          </div>
        ) : (
          <span>
            {slim === false ? placeholder : <Globe size={20} />}
          </span>
        )}
        <ChevronDown size={16} />
      </PopoverTrigger>

      <PopoverContent
        collisionPadding={10}
        side="bottom"
        className="min-w-[--radix-popper-anchor-width] p-0"
        usePortal={false}
        onOpenAutoFocus={(e) => e.preventDefault()}
      >
        <Command className="w-full max-h-[200px] sm:max-h-[270px]">
          <CommandList>
           <div className="sticky top-0 z-10 bg-popover">
  <CommandInput placeholder="Search country..." />
</div>


            <CommandEmpty>No country found.</CommandEmpty>
            <CommandGroup>
              {options
                .filter((x) => x.name)
                .map((option, key: number) => (
                  <CommandItem
                    className="flex items-center w-full gap-2"
                    key={key}
                    onSelect={() => handleSelect(option)}
                  >
                    <div className="flex flex-grow w-0 space-x-2 overflow-hidden">
                      <div className="inline-flex items-center justify-center w-5 h-5 shrink-0 overflow-hidden rounded-full">
                        <CircleFlag
                          countryCode={option.alpha2.toLowerCase()}
                          height={20}
                        />
                      </div>
                      <span className="overflow-hidden text-ellipsis whitespace-nowrap">
                        {option.name}
                      </span>
                    </div>
                    <CheckIcon
                      className={cn(
                        "ml-auto h-4 w-4 shrink-0",
                        multiple
                          ? selectedCountries.some((c) => c.name === option.name)
                            ? "opacity-100"
                            : "opacity-0"
                          : option.name === selectedCountry?.name
                          ? "opacity-100"
                          : "opacity-0"
                      )}
                    />
                  </CommandItem>
                ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

CountryDropdownComponent.displayName = "CountryDropdownComponent";

export const CountryDropdown = forwardRef<
  HTMLButtonElement,
  CountryDropdownProps
>(CountryDropdownComponent);
