import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { formatDateToShortMonth } from '../utils/coomonFunctions'
import { ApplicationInfoRow } from './application-info-row'
import { ApplicationTypeStatusRow } from './application-type-status-row'

interface ApplicationDetailsProps {
  applicationType: string
  status: string
  applicationNumber: string
  applicationDate: Date
}

export default function ApplicationDetails({
  applicationType,
  status,
  applicationNumber,
  applicationDate,
}: ApplicationDetailsProps) {
  return (
    <Card className='border-none shadow-none pb-8 px-2 bg-white dark:bg-slate-800'>
      <CardHeader className='pb-6'>
        <CardTitle className='text-base font-bold'>
          Application Details
        </CardTitle>
      </CardHeader>
      <CardContent className=' rounded-ld px-0 py-4 ml-6 mr-6'>
        <ApplicationTypeStatusRow
          applicationType={applicationType}
          status={status}
        />
        <div className='flex flex-col md:flex-row gap-4 md:gap-10 text-xs ml-6'>
          <ApplicationInfoRow
            label='Application Number'
            value={applicationNumber}
          />
          <ApplicationInfoRow
            label='Application Date'
            value={formatDateToShortMonth(applicationDate)}
          />
        </div>
      </CardContent>
    </Card>
  )
}
