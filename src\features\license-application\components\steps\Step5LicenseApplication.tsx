import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Form } from '@/components/ui/form';
import CompanyMembers, { Member } from '../../company-members';

interface CompanyMembersStepProps {
  form: any;
  members: Member[];
  setMembers: React.Dispatch<React.SetStateAction<Member[]>>;
}

const Step5LicenseApplication: React.FC<CompanyMembersStepProps> = ({ form, members, setMembers }) => {
  return (
    <Card className='mt-6'>
      <CardContent>
        <Form {...form}>
          <form className='space-y-4 fz-form'>
            <CardHeader className='px-0 pt-0 pb-0'>
              <CardTitle>Add Company Members ( Step 5 of 6 )</CardTitle>
            </CardHeader>
            <CompanyMembers
              members={members}
              setMembers={setMembers}
              numberOfShareholders={form.getValues('numberOfShareholders')}
              totalShares={Number(form.getValues('totalNumberOfShares'))}
            />
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default Step5LicenseApplication;
