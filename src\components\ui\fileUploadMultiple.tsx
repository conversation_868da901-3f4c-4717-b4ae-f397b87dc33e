import { useRef, useState, useEffect } from 'react'
import { Upload, CircleX, Repeat } from 'lucide-react'
import { Input } from '@/components/ui/input'

interface FileUploadFieldMultipleProps {
  accept: string
  onchoose?: (files: File[] | null) => void
  value?: File[]
}

export default function FileUploadFieldMultiple({
  accept,
  onchoose,
  value = [],
}: FileUploadFieldMultipleProps) {
  const mainInputRef = useRef<HTMLInputElement | null>(null)
  const [filesChosen, setFilesChosen] = useState<File[]>([])

  // Update local state when external value prop changes
  useEffect(() => {
    if (Array.isArray(value)) {
      setFilesChosen(value)
    } else {
      setFilesChosen([])
    }
  }, [value])

  // Remove a file by its index
  const handleClear = (index: number) => {
    const updatedFiles = filesChosen.filter((_, i) => i !== index)
    setFilesChosen(updatedFiles)
    onchoose?.(updatedFiles.length ? updatedFiles : null)
  }

  // Handle selecting files from the main hidden input (multiple selection)
  const handleMainChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      const selected = Array.from(files)
      const merged = [...filesChosen]

      // Add only files that are not already selected
      selected.forEach((newFile) => {
        const exists = merged.some(
          (f) =>
            f.name === newFile.name &&
            f.size === newFile.size &&
            f.lastModified === newFile.lastModified
        )
        if (!exists) {
          merged.push(newFile)
        }
      })

      setFilesChosen(merged)
      onchoose?.(merged)

      // Reset input value so the same files can be re-selected if needed
      if (mainInputRef.current) mainInputRef.current.value = ''
    }
  }

  // Replace a specific file at given index with a new file
  const handleReplaceChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    index: number
  ) => {
    const files = e.target.files
    if (files && files.length === 1) {
      const newFile = files[0]
      const oldFile = filesChosen[index]

      // If the new file is the same as old, do nothing
      const isSameFile =
        oldFile.name === newFile.name &&
        oldFile.size === newFile.size &&
        oldFile.lastModified === newFile.lastModified

      if (isSameFile) {
        e.target.value = ''
        return
      }

      const updatedFiles = [...filesChosen]
      updatedFiles[index] = newFile
      setFilesChosen(updatedFiles)
      onchoose?.(updatedFiles)

      e.target.value = ''
    }
  }

  // Programmatically open the main file input dialog
  const triggerMainFileDialog = () => {
    mainInputRef.current?.click()
  }

  // Programmatically open the replace file input dialog for a specific file
  const triggerReplaceFileDialog = (index: number) => {
    const input = document.getElementById(
      `replace-file-input-${index}`
    ) as HTMLInputElement | null
    input?.click()
  }

  const maxFilesToShow = 3
  const extraFilesCount = filesChosen.length - maxFilesToShow

  return (
    <div className='relative w-full'>
      {/* Hidden input for selecting multiple files */}
      <Input
        ref={mainInputRef}
        id='multiple-files'
        type='file'
        accept={accept}
        className='hidden'
        multiple
        onChange={handleMainChange}
      />

      {/* Visible container with just Choose files button and placeholder */}
      <div
        className='flex items-center justify-between w-full border rounded-md bg-white shadow-sm px-3 py-[6px] cursor-pointer text-sm leading-tight'
        style={{
          height: 36,
          fontFamily: 'inherit',
          fontWeight: 'normal',
        }}
        onClick={triggerMainFileDialog}
      >
        {/* Placeholder text only */}
        <div className='text-gray-400 truncate'>
          {filesChosen.length > 0
            ? `${filesChosen.length} file(s) selected`
            : 'No files chosen'}
        </div>

        {/* Main choose files button */}
        <button
          type='button'
          className='flex items-center gap-1 flex-shrink-0'
          onClick={(e) => {
            e.stopPropagation()
            triggerMainFileDialog()
          }}
          aria-label='Choose files'
        >
          <Upload className='w-4 h-4' />
          <span className='whitespace-nowrap font-medium'>Choose files</span>
        </button>
      </div>

      {/* Files list below - only file names, no image preview */}
      <div className='mt-2 flex flex-wrap gap-1.5 max-w-full overflow-auto max-h-40 items-center'>
        {filesChosen.length > 0 &&
          filesChosen.slice(0, maxFilesToShow).map((file, idx) => {
            const key = `${file.name}-${file.lastModified}-${file.size}-${idx}`

            return (
              <div
                key={key}
                className='flex items-center h-8 bg-gray-100 px-2 py-0 rounded-full text-xs text-gray-700 max-w-[200px] overflow-hidden shadow-sm hover:bg-gray-200 transition-colors cursor-default'
                title={file.name}
              >
                <span className='truncate'>{file.name}</span>

                {/* Hidden input for replacing this file */}
                <input
                  key={key}
                  type='file'
                  accept={accept}
                  className='hidden'
                  id={`replace-file-input-${idx}`}
                  onChange={(e) => handleReplaceChange(e, idx)}
                />

                {/* Replace button */}
                <button
                  type='button'
                  title='Replace this file'
                  className='w-[14px] h-[14px] ml-1 text-blue-500 hover:text-blue-700 flex-shrink-0'
                  onClick={() => triggerReplaceFileDialog(idx)}
                >
                  <Repeat className='w-[14px] h-[14px]' />
                </button>

                {/* Remove button */}
                <button
                  type='button'
                  title='Remove this file'
                  className='w-[14px] h-[14px] ml-1 text-red-500 hover:text-red-700 flex-shrink-0'
                  onClick={() => handleClear(idx)}
                >
                  <CircleX className='w-[14px] h-[14px]' />
                </button>
              </div>
            )
          })}

        {/* Show "+N more" if more files than maxFilesToShow */}
        {extraFilesCount > 0 && (
          <div className='text-gray-600 text-xs font-medium ml-2'>
            +{extraFilesCount} more
          </div>
        )}
      </div>
    </div>
  )
}
