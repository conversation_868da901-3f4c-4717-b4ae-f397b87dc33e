import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { fetchTrackerStatus } from '@/store/trackerSlice';
import { RootState, AppDispatch } from '@/store';
import { useLocation } from '@tanstack/react-router';
import TrackerBody from '../components/tracker-entire-body';
import { LoadingMessage, ErrorMessage } from '../../../components/loading-and-error';

export default function PublicTrackerStatusPage() { 
  const location = useLocation();
  const params = new URLSearchParams(location.search);
  const applicationNumber = params.get('applicationNumber') || '';

  const dispatch = useDispatch<AppDispatch>();
  const { status: reduxStatus, loading, error } = useSelector((state: RootState) => state.tracker);
  // Use trackerStatus from navigation state if present
  const trackerStatusFromState = (location.state as any)?.trackerStatus;
  const [localStatus, setLocalStatus] = useState(trackerStatusFromState || null);

  const hasFetched = React.useRef(false);

  useEffect(() => {
    if (trackerStatusFromState) {
      setLocalStatus(trackerStatusFromState);
    } else if (!reduxStatus && applicationNumber && !loading && !hasFetched.current) {
      hasFetched.current = true;
      dispatch(fetchTrackerStatus(applicationNumber));
    }
  }, [trackerStatusFromState, reduxStatus, applicationNumber, dispatch, loading]);

  const status = localStatus || reduxStatus;
  
  return (
    <>
      <LoadingMessage loading={loading} />
      <ErrorMessage error={error} trackNewApplicationUrl="/track-your-application"/>

      {status && (() => {
        return (
          <TrackerBody
            
            steps={status.steps}
            currentStep={status.currentStep}
            stepTimestamps={status.stepTimestamps}
            applicationType={status.details.applicationType || ''}
            status={status.steps[status.currentStep] || 'Unknown Status'}
            applicationNumber={status.details.applicationNumber || ''}
            applicationDate={status.details.applicationDate ? status.details.applicationDate : ''}
            trackNewApplicationUrl="/track-your-application"
          />
        );
      })()}

    </>
  );
}
