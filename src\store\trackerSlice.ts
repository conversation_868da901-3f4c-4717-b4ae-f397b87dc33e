import { fetchApplicationStatus } from '@/services/trackapplication';
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import type { ZohoDeal } from '@/features/track-application/utils/application-step-utils';


// Define the type for status based on fetchApplicationStatus return
interface StatusDetails {
  applicationType: string
  status: string | number
  applicationNumber: string
  applicationDate: Date
}


export interface StatusType {
  found: boolean;
  details?: StatusDetails;
  currentStep?: number;
  raw?: ZohoDeal;
}

interface TrackerState {
  status: StatusType | null;
  loading: boolean;
  error: string;
}

const initialState: TrackerState = {
  status: null,
  loading: false,
  error: '',
};

export const fetchTrackerStatus = createAsyncThunk(
  'tracker/fetchStatus',
  async (applicationNumber: string, { rejectWithValue }) => {
    try {
      const result = await fetchApplicationStatus(applicationNumber);
      if (!result.found) {
        return rejectWithValue('Application number not found.');
      }
      return result;
    } catch (error: unknown) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('Unknown error');
    }
  }
);

const trackerSlice = createSlice({
  name: 'tracker',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchTrackerStatus.pending, (state) => {
        state.loading = true;
        state.error = '';
        state.status = null;
      })
      .addCase(fetchTrackerStatus.fulfilled, (state, action) => {
        state.loading = false;
        state.status = action.payload;
      })
      .addCase(fetchTrackerStatus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string || 'Failed to fetch status';
      });
  },
});

export default trackerSlice.reducer;