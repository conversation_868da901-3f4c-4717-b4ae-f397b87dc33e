import Cookies from 'js-cookie'
import { createFileRoute, Outlet } from '@tanstack/react-router'
import { cn } from '@/lib/utils'
import { useEffect } from 'react'
import { useNavigate } from '@tanstack/react-router'
import { useAuthStore } from '@/stores/authStore'
import { SearchProvider } from '@/context/search-context'
import { SidebarProvider } from '@/components/ui/sidebar'
import { AppSidebar } from '@/components/layout/app-sidebar'
import SkipToMain from '@/components/skip-to-main'

export const Route = createFileRoute('/_authenticated')({
  component: RouteComponent,
})

function RouteComponent() {
  const navigate = useNavigate()
  // Detect external link by presence of 'id' in query params and bypass auth/sidebar
  const externalId = typeof window !== 'undefined'
    ? new URLSearchParams(window.location.search).get('id')
    : null
  if (externalId) {
    return (
      <SearchProvider>
        <SkipToMain />
        <div className='px-4 py-6 lg:px-8'>
          <Outlet />
        </div>
      </SearchProvider>
    )
  }
  // Redirect to sign-in if not authenticated
  const accessToken = useAuthStore((state) => state.auth.accessToken)
  useEffect(() => {
    if (!accessToken) {
      navigate({ to: '/sign-in', replace: true, search: { redirect: window.location.pathname } })
    }
  }, [accessToken, navigate])
  if (!accessToken) return null
  const defaultOpen = Cookies.get('sidebar:state') !== 'false'
  return (
    <SearchProvider>
      <SidebarProvider defaultOpen={defaultOpen}>
        <SkipToMain />
        <AppSidebar />
        <div
          id='content'
          className={cn(
            'max-w-full w-full ml-auto',
            'peer-data-[state=collapsed]:w-[calc(100%-var(--sidebar-width-icon))]',
            'peer-data-[state=expanded]:w-[calc(100%-var(--sidebar-width))]',
            'transition-[width] ease-linear duration-200',
            'h-svh flex flex-col',
            'group-data-[scroll-locked=1]/body:h-full',
            'group-data-[scroll-locked=1]/body:has-[main.fixed-main]:h-svh'
          )}
        >
          <Outlet />
        </div>
      </SidebarProvider>
    </SearchProvider>
  )
}
