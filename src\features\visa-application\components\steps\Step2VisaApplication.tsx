import React, { useEffect } from 'react'
import { useFormContext, useWatch } from 'react-hook-form'
import { FilePenLine } from 'lucide-react'
import PhoneInput from 'react-phone-input-2'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { CountryDropdown } from '@/components/ui/country-dropdown'
import { DateTimePicker } from '@/components/ui/datetime-picker'
import FileUploadField from '@/components/ui/fileUpload'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  transliterate,
  containsSpecialCharacters,
} from '../../schemas/translation'
import { ApplicationFormValues } from '../../types/application-form-types'

interface Step2FormProps {
  date: Date | undefined
  setDate: React.Dispatch<React.SetStateAction<Date | undefined>>
}

const Step2VisaApplication: React.FC<Step2FormProps> = ({ date, setDate }) => {
  const form = useFormContext<ApplicationFormValues>()
  const nationality = useWatch({ control: form.control, name: 'nationality' })
  const emiratesID = useWatch({ control: form.control, name: 'emiratesID' })
  const firstName = useWatch({ control: form.control, name: 'firstName' })
  const middleName = useWatch({ control: form.control, name: 'middleName' })
  const lastName = useWatch({ control: form.control, name: 'lastName' })

  useEffect(() => {
    if (!containsSpecialCharacters(firstName || '')) {
      form.setValue('firstNameArabic', transliterate(firstName || ''))
    } else {
      form.setValue('firstNameArabic', '')
    }

    if (!containsSpecialCharacters(middleName || '')) {
      form.setValue('middleNameArabic', transliterate(middleName || ''))
    } else {
      form.setValue('middleNameArabic', '')
    }

    if (!containsSpecialCharacters(lastName || '')) {
      form.setValue('lastNameArabic', transliterate(lastName || ''))
    } else {
      form.setValue('lastNameArabic', '')
    }

    form.trigger(['firstNameArabic', 'middleNameArabic', 'lastNameArabic'])
  }, [firstName, middleName, lastName])

  console.log('Step 2 Form Values:', form.watch());

  return (
    <>
      <form className='space-y-4 fz-form'>
              <div className='flex items-start space-x-2'>
                  <FormField
                    control={form.control}
                    name='title'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Name <span className='text-red-500'>*</span>
                        </FormLabel>
                        <FormControl>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <SelectTrigger className='w-[120px]'>
                              <SelectValue placeholder='Select Title' />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value='Mr.'>Mr.</SelectItem>
                              <SelectItem value='Mrs.'>Mrs.</SelectItem>
                              <SelectItem value='Ms.'>Ms.</SelectItem>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                <div className='w-full'>
                  <FormField
                    control={form.control}
                    name='firstName'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          First Name <span className='text-red-500'>*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder='First Name in English'
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className='w-full'>
                  <FormField
                    control={form.control}
                    name='middleName'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Middle Name
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder='Middle Name in English'
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className='w-full'>
                  <FormField
                    control={form.control}
                    name='lastName'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Last Name <span className='text-red-500'>*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder='Last Name in English'
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            <FormDescription className='translate-y-[-10px]'>
              The name should be exactly as per the passport. Other version of the
              name which are not matching the passport not be accepted and may
              result in application rejection and/or delays.
            </FormDescription>

        
        {nationality === 'Bangladesh' && (
          <>
            <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
              <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
              </span>
              <div className='flex flex-col ml-2'>
                <AlertTitle>
                  Please follow the below guideline for filling in the
                  applicant's name :
                </AlertTitle>

                <AlertDescription>
                  <p className="text-sm dark:text-red-500 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    First name, will be the given name, as per the passport
                  </p>
                  <p className="text-sm dark:text-red-500 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    Middle name, will be the surname, as per the passport
                  </p>
                  <p className="text-sm dark:text-red-500 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    Last name, will be the name of the father (first page), as
                    per the passport
                  </p>
                </AlertDescription>
              </div>
            </Alert>
          </>
        )}

        {nationality === 'Pakistan' && (
          <>
            <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
              <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
              </span>
              <div className='flex flex-col ml-2'>
                <AlertTitle>
                  Please follow the below guideline for filling in the
                  applicant's name :
                </AlertTitle>

                <AlertDescription>
                  <p className="text-sm dark:text-red-500 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    First name, will be the given name, as per the passport
                  </p>
                  <p className="text-sm dark:text-red-500 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    Middle name, will be the surname, as per the passport
                  </p>
                  <p className="text-sm dark:text-red-500 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    Last name, will be the name of the father/husband, as per
                    the passport
                  </p>
                </AlertDescription>
              </div>
            </Alert>
          </>
        )}

        {nationality === 'India' && (
          <>
            <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
              <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
              </span>
              <div className='flex flex-col ml-2'>
                <AlertTitle>
                  Please follow the below guideline for filling in the
                  applicant's name :
                </AlertTitle>

                <AlertDescription>
                  <p className="text-sm dark:text-red-500 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    First name, will be the given name, as per the passport
                  </p>
                  <p className="text-sm dark:text-red-500 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    Middle name, will be the surname, as per the passport
                  </p>
                  <p className="text-sm dark:text-red-500 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    Last name, will be the name of the father (last page), as
                    per the passport
                  </p>
                </AlertDescription>
              </div>
            </Alert>
          </>
        )}
          <FormField
            control={form.control}
            name='arabicName'
            render={({ field }) => (
              <FormItem data-error-field='arabicName'>
                <FormLabel>
                  Do you have Arabic Name in your Passport ?{' '}
                  <span style={{ color: 'red' }}>*</span>
                </FormLabel>
                <Select onValueChange={field.onChange} value={field.value}>
                  <SelectTrigger>
                    <SelectValue placeholder='Select ' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='Yes'>Yes</SelectItem>
                    <SelectItem value='No'>No</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          
                <div className='flex items-start space-x-2'>
                    <FormField
                      control={form.control}
                      name='title'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Name <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <Select
                              onValueChange={field.onChange}
                              value={field.value}
                            >
                              <SelectTrigger className='w-[120px]'>
                                <SelectValue placeholder='Select Title' />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value='Mr.'>Mr.</SelectItem>
                                <SelectItem value='Mrs.'>Mrs.</SelectItem>
                                <SelectItem value='Ms.'>Ms.</SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                  <div className='w-full'>
                    <FormField
                      control={form.control}
                      name='firstNameArabic'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            First Name <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder='First Name in Arabic'
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className='w-full'>
                    <FormField
                      control={form.control}
                      name='middleNameArabic'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Middle Name
                          </FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder='Middle Name in Arabic'
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className='w-full'>
                    <FormField
                      control={form.control}
                      name='lastNameArabic'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Last Name <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder='Last Name in Arabic'
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              
        <FormDescription className='translate-y-[-10px]'>
          The name should be exactly as per the passport. Other version of the
          name which are not matching the passport may not be accepted and may
          result in the application being rejected or delayed.
        </FormDescription>

        <div className='flex flex-wrap space-between gap-y-4'>
          <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
            <FormField
              control={form.control}
              name='emailAddress'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Email Address <span style={{ color: 'red' }}>*</span>
                  </FormLabel>
                  <FormControl>
                    <Input type='Enter Email Address' {...field} />
                  </FormControl>
                  <FormDescription>
                    Email address of the visa applicant
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className='flex-1'>
            <FormField
              control={form.control}
              name='phone'
              render={({ field }) => (
                <FormItem data-error-field='phone'>
                  <FormLabel>
                    Phone <span className='text-red-500'>*</span>
                  </FormLabel>
                  <PhoneInput
                    country={'ae'}
                    value={field.value.toString()}
                    onChange={(phone) => field.onChange(phone)}
                    containerClass='fz-country-code flex h-9 w-full rounded-tl-[6px] rounded-bl-[6px] border border-input bg-transparent text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm'
                    inputClass=' flex  !h-9 !w-full rounded-tl-[6px] rounded-bl-[6px] border !border-transparent !bg-transparent px-3 text-base !shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm transform translate-y-[-2px]'
                    buttonClass='!h-100 !border-l-transparent !border-t-transparent !bg-slate-200 dark:!bg-slate-800 dark:!border-r-transparent !border-b-transparent !border-gray-200 !bg-transparent !rounded-tl-[8px] !rounded-bl-[6px]'
                    enableSearch={true}
                    searchPlaceholder='Search country...'
                  />
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
        <div className='mb-4'>
          <FormField
            control={form.control}
            name='streetAddress'
            render={() => (
              <FormItem className='w-full mt-4'>
                <FormLabel>UAE Address (if Applicable)</FormLabel>

                <div className='flex items-start space-x-2'>
                  <div className='w-full'>
                    <FormField
                      control={form.control}
                      name='streetAddress'
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder='Enter Street Address'
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className='w-full'>
                    <FormField
                      control={form.control}
                      name='addressLine'
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder='Enter Address Line 2'
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className='w-full'>
                    <FormField
                      control={form.control}
                      name='cityAddress'
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input {...field} placeholder='Enter City ' />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className='w-full'>
                    <FormField
                      control={form.control}
                      name='stateProvince'
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder='Enter State/Region/Province'
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className='w-full'>
                    <FormField
                      control={form.control}
                      name='country'
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <CountryDropdown
                              data-field='country'
                              placeholder='Country'
                              defaultValue={field.value as string}
                              onChange={(country) => {
                              field.onChange(country.name)
                            }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </FormItem>
            )}
          />
        </div>
        <div className='mb-4'>
          <FormField
            control={form.control}
            name='streetAddress1'
            render={() => (
              <FormItem className='w-full mt-4'>
                <FormLabel>
                  Home Country Address <span style={{ color: 'red' }}>*</span>
                </FormLabel>

                <div className='flex items-start space-x-2'>
                  <div className='w-full'>
                    <FormField
                      control={form.control}
                      name='streetAddress1'
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder='Enter Street Address'
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <div className='w-full'>
                    <FormField
                      control={form.control}
                      name='addressLine2'
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder='Enter Address Line 2'
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className='w-full'>
                    <FormField
                      control={form.control}
                      name='city'
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input {...field} placeholder='Enter City' />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className='w-full'>
                    <FormField
                      control={form.control}
                      name='province'
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder='Enter State/Region/Province'
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className='w-full'>
                    <FormField
                      control={form.control}
                      name='country1'
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <CountryDropdown
                              data-field='country1'
                              placeholder='Country'
                              defaultValue={field.value as string}
                              onChange={(country) => {
                                field.onChange(country.name)
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </FormItem>
            )}
          />
        </div>
        <div className='mb-4'>
          <FormField
            control={form.control}
            name='emiratesID'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Do you have a previous Emirates ID{' '}
                  <span className='text-red-500'>*</span>
                </FormLabel>
                <FormControl>
                  <Select
                    {...field}
                    onValueChange={(value) => field.onChange(value)}
                    value={field.value}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='Yes'>Yes</SelectItem>
                      <SelectItem value='No'>No</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        {emiratesID === 'Yes' && (
          <>
            <div className='flex flex-wrap space-between gap-y-4'>
              <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                <FormField
                  control={form.control}
                  name='emiratesIDNumber'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Emirates ID Number{' '}
                        <span className='text-red-500'>*</span>
                      </FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className='flex-1'>
                <FormField
                  control={form.control}
                  name='emiratesIDExpiryDate'
                  render={() => (
                    <FormItem data-error-field='emiratesIDExpiryDate'>
                      <FormLabel>
                        Emirates ID Expiry Date{' '}
                        <span className='text-red-500'>*</span>
                      </FormLabel>
                      <DateTimePicker
                        granularity='day'
                        value={date !== null ? date : undefined}
                        onChange={(val) => {
                          setDate(val)
                          if (val) {
                            form.setValue('emiratesIDExpiryDate', val)
                            form.clearErrors('emiratesIDExpiryDate')
                          } else {
                            form.setError('emiratesIDExpiryDate', {
                              type: 'manual',
                              message: 'Select a date',
                            })
                          }
                        }}
                        displayFormat={{
                          hour24: 'dd MMMM yyyy',
                        }}
                      />
                      <FormDescription>dd/MM/yyyy</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
            <div className='mb-4'>
              <FormField
                control={form.control}
                name='emiratesIDCopy'
                render={({ field }) => (
                  <FormItem data-error-field='emiratesIDCopy'>
                    <FormLabel>
                      Emirates ID Copy <span style={{ color: 'red' }}>*</span>
                    </FormLabel>
                    <FormControl>
                      <FileUploadField
                        accept='.pdf,.jpg,.png'
                        value={field.value}
                        onchoose={(file) => {
                          form.setValue('emiratesIDCopy', file || '')
                          form.clearErrors('emiratesIDCopy')
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </>
        )}
      </form>
    </>
  )
}

export default Step2VisaApplication
