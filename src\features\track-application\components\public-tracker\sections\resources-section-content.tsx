import { ArrowUpRight, Lightbulb, LinkIcon, Mail, Phone, Smartphone } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';


export const VisaRelatedInquiries = () => {
  return (
    <div className='text-neutral-600 dark:text-neutral-400'>
      <Alert className='flex gap-4 p-4 items-start text-md border-dashed border-blue-500 !bg-blue-50 dark:!bg-blue-400/20 mb-4'>
        <span className='bg-blue-100 p-2 rounded inline-block'>
          <Lightbulb className='w-4 h-4 stroke-blue-500' />
        </span>
        <div className='flex flex-col'>
          <AlertTitle className='leading-5 mb-2'>
            To initiate a visa application - whether for a residence visa, a
            work permit, for yourself or an employee - please reach out to our
            Partner Support at{' '}
            <Button variant={'link'} className='p-0 text-md' asChild>
              <a href='mailto:<EMAIL>'><EMAIL></a>
            </Button>
          </AlertTitle>
          {/* <Button
            variant={'btn_outline'}
            size={'lg'}
            className='w-fit flex items-center gap-2 px-4 hover:bg-white/50'
            asChild
          >
            <Link to={'/'} target='_blank'>
              <LinkIcon className='w-4 h-4' />
              Submit Request
            </Link>
          </Button> */}
        </div>
      </Alert>

      {/* <p className='font-medium mb-2'>
        You can refer to our step-by-step video guide available{' '}
        <Button variant={'link'} className='p-0 text-md' asChild>
          <a href='#' target='_blank'>
            here
          </a>
        </Button>
        .
      </p> */}
      <p className='mb-2'>
        If you need further assistance, feel free to contact our support team
        {/* at{' '}
        <Button variant={'link'} className='p-0 text-md' asChild>
          <a href='mailto:<EMAIL>'><EMAIL></a>
        </Button> */}
        , and one of our visa specialists will be happy to guide you through the
        process.
      </p>
    </div>
  )
}

export const IfzaLife = () => {
  return (
    <div className='text-neutral-600 dark:text-neutral-400'>
      <p className='mb-2'>
        IFZA Life offers a range of health insurance options, designed
        specifically for small and medium-sized enterprises (SMEs) in the
        region. These packages are uniquely tailored to meet your business
        needs.
      </p>
      <p className='mb-2'>
        Our medical coverage plan meets the requirements of Dubai Health
        Authority (DHA) and is at a price that works when within your budget. To
        learn more about IFZA life you may send an email to{' '}
        <Button variant={'link'} className='p-0 text-md' asChild>
          <a href='mailto:<EMAIL>'><EMAIL></a>
        </Button>{' '}
        or call +971 42 304201.
      </p>
    </div>
  )
}

export const RequestingDocuments = () => {
  return (
    <div className='text-neutral-600 dark:text-neutral-400'>
      <p className='pb-4'>
        You can request documents quickly and easily by submitting your request
        online. We offer support with a wide range of documents, including:
      </p>
      <ul className='list-disc pl-6 pb-4'>
        <li>
          <strong>License-related documents</strong> such as Registry Extracts,
          Certificate of Incumbency or Share Certificates
        </li>
        <li>
          <strong>Visa or employee-related documents</strong> such as Salary
          Certificate and Employee Lists
        </li>
      </ul>
      <p className='pb-4'>
        Each submission will automatically generate a quotation for the
        requested service.
      </p>
      <p className='pb-4'>
        To request <strong> license-related documents</strong> or{' '}
        <strong>visa or employee-related documents</strong>, please reach out to{' '}
        <Button variant={'link'} className='p-0 text-md' asChild>
          <a href='mailto:<EMAIL>'><EMAIL></a>
        </Button>
      </p>
      {/* <ul className='list-disc pl-6 pb-4'>
        <li>
          <span className='flex items-center gap-2'>
            To request <strong> license-related documents:</strong>{' '}
            <Button variant={'link'} className='p-0 text-md h-fit' asChild>
              <Link to='/'>
                <LinkIcon className='w-4 h-4' />
                CLICK HERE
              </Link>
            </Button>
          </span>
        </li>
        <li>
          <span className='flex items-center gap-2'>
            To request <strong>visa or employee-related documents:</strong>{' '}
            <Button variant={'link'} className='p-0 text-md h-fit' asChild>
              <Link to='/'>
                <LinkIcon className='w-4 h-4' />
                CLICK HERE
              </Link>
            </Button>
          </span>
        </li>
      </ul> */}
    </div>
  )
}

export const BankingSupport = () => {
  return (
    <div className='text-neutral-600 dark:text-neutral-400'>
      <Alert className='flex gap-4 p-4 items-start text-md border-dashed border-blue-500 !bg-blue-50 dark:!bg-blue-400/20 mb-4'>
        <span className='bg-blue-100 p-2 rounded inline-block'>
          <Lightbulb className='w-4 h-4 stroke-blue-500' />
        </span>
        <div className='flex flex-col'>
          <AlertTitle className='leading-5 mb-2'>
            IFZA has established partnerships with WIO, Mashreq, RAK and ADIB
            Bank. Our dedicated Banking Team is further networking with 16 banks
            as well as multiple EMIs (Electronic Money Institutions) around the
            world and are happy to answer your questions about requirements,
            eligibility and guide you on how to get started.
          </AlertTitle>
        </div>
      </Alert>
      <p>
        Please reach out to{' '}
        <Button variant={'link'} className='px-0 text-md' asChild>
          <a href={'mailto:<EMAIL>'}><EMAIL></a>
        </Button>{' '}
        or fill directly their{' '}
        <Button variant={'link'} className='px-0 text-md' asChild>
          <a
            href='https://are01.safelinks.protection.outlook.com/?url=https%3A%2F%2Fsurvey.zohopublic.com%2Fzs%2FwnB3oo&data=05%7C02%7Cmsultaneh%40ifza.com%7C1cf74580ba5344caf99308dd8bbc455d%7Cfe855dd6665e4251928c33872188de1f%7C0%7C0%7C638820362962455887%7CUnknown%7CTWFpbGZsb3d8eyJFbXB0eU1hcGkiOnRydWUsIlYiOiIwLjAuMDAwMCIsIlAiOiJXaW4zMiIsIkFOIjoiTWFpbCIsIldUIjoyfQ%3D%3D%7C0%7C%7C%7C&sdata=oqs9ZDt9pHb6Vzz2j0YHg42elqloAWB0MO%2BK%2FkwnfT4%3D&reserved=0'
            target='_blank'
            rel='noopener noreferrer'
          >
            Pre-Evaluation for Corporate Bank Accounts
          </a>
        </Button>{' '}
        Form for tailored Banking recommendations and the team will get back to
        you with Banking options.
      </p>
    </div>
  )
}

export const OfficeSpace = () => {
  return (
    <div className='text-neutral-600 dark:text-neutral-400'>
      <p>
        IFZA Property offers our clients a range of options, from shared Flexi
        Desks to personalized offices within modern, LEED Platinum Certified
        buildings.
      </p>
      <p>
        To learn more, you can view please contact our Property Professionals at{' '}
        <Button variant={'link'} className='p-0 text-md' asChild>
          <a href='mailto:<EMAIL>.'><EMAIL></a>
        </Button>
      </p>
      <p>
        To view our property brochure, kindly click{' '}
        <Button variant={'link'} className='p-0 text-md' asChild>
          <a
            target='_blank'
            href='https://ifzauae-my.sharepoint.com/:b:/g/personal/nmasigon_ifza_com/ESQKaUlFPupJir6uD94woSEBMG8LEU-P6QM-7Y6yUKUCkQ?e=41wgZg'
          >
            here
          </a>
        </Button>
      </p>
    </div>
  )
}

export const CorporateConsultingServices = () => {
  return (
    <div className='text-neutral-600 dark:text-neutral-400 h-96 overflow-y-auto scrollbar-thin pr-4'>
      <p className='pb-4'>
        To ensure you are compliant with the UAE VAT and Corporate Tax
        registration deadlines for your Company, please refer to the below
        information:
      </p>
      {/* VAT RECORDS */}
      <Alert className='flex gap-4 p-4 items-start text-md border-dashed border-blue-500 !bg-blue-50 dark:!bg-blue-400/20 mb-4'>
        <span className='bg-blue-100 p-2 rounded inline-block'>
          <Lightbulb className='w-4 h-4 stroke-blue-500' />
        </span>
        <div className='flex flex-col'>
          <AlertTitle className='leading-5 mb-2'>
            Corporate Income Tax
          </AlertTitle>
          <AlertDescription>
            <ul className='list-disc pl-6 pb-4'>
              <li>
                Mandatory for all UAE Companies to be registered for Corporate
                Tax with the Federal Tax Authority, of the date of your trade
                license issuance date <strong>within 3 months</strong>
              </li>
              <li>
                A penalty of AED 10,000 is applicable for late registration
              </li>
              <li>Keeping proper accounting records is mandatory</li>
            </ul>
          </AlertDescription>
          <Button
            variant={'btn_outline'}
            size={'lg'}
            className='w-fit flex items-center gap-2 px-4 hover:bg-white/50'
            asChild
          >
            <a
              target='_blank'
              href='https://are01.safelinks.protection.outlook.com/?url=https%3A%2F%2Fifza.com%2Fen%2Findustry-analysis%2Fcorporate-tax-uae-guide%2F&data=05%7C02%7Caftersales%40ifza.com%7Cee606f8f804647417fba08dd96aff78e%7Cfe855dd6665e4251928c33872188de1f%7C0%7C0%7C638832404721291737%7CUnknown%7CTWFpbGZsb3d8eyJFbXB0eU1hcGkiOnRydWUsIlYiOiIwLjAuMDAwMCIsIlAiOiJXaW4zMiIsIkFOIjoiTWFpbCIsIldUIjoyfQ%3D%3D%7C0%7C%7C%7C&sdata=vO1IdaA3uCUna%2Bjh9fdm4%2FQAKlRLWaCVHpKtd5w1rc0%3D&reserved=0'
            >
              <LinkIcon className='w-4 h-4' />
              Corporate Tax UAE
            </a>
          </Button>
        </div>
      </Alert>

      <Alert className='flex gap-4 p-4 items-start text-md border-dashed border-blue-500 !bg-blue-50 dark:!bg-blue-400/20 mb-4'>
        <span className='bg-blue-100 p-2 rounded inline-block'>
          <Lightbulb className='w-4 h-4 stroke-blue-500' />
        </span>
        <div className='flex flex-col'>
          <AlertTitle className='leading-5 mb-2'>
            Value Added Tax (“VAT”)
          </AlertTitle>
          <AlertDescription>
            <ul className='list-disc pl-6 pb-4'>
              <li>
                Mandatory to be registered for VAT with the Federal Tax
                Authority,{' '}
                <strong className='text-wrap'>
                  within 30 days of the Company’s taxable supplies meeting the
                  threshold of AED 375,000 either during the last 12 months or
                  is expected to exceed this threshold within the upcoming 30
                  days.
                </strong>{' '}
              </li>
              <li>
                Companies have the option to voluntarily register if the
                Company’s taxable supplies exceed the voluntary threshold of AED
                187,500
              </li>
            </ul>
            <p className='mb-2'>For more information, refer to this link:</p>
          </AlertDescription>
          <Button
            variant={'btn_outline'}
            size={'lg'}
            className='w-fit h-fit text-wrap flex items-center gap-2 py-2 px-4 hover:bg-white/50'
            asChild
          >
            <a
              target='_blank'
              href='https://are01.safelinks.protection.outlook.com/?url=https%3A%2F%2Fifza.com%2Fen%2Findustry-analysis%2Fuae-vat-guide%2F&data=05%7C02%7Caftersales%40ifza.com%7Cee606f8f804647417fba08dd96aff78e%7Cfe855dd6665e4251928c33872188de1f%7C0%7C0%7C638832404721310026%7CUnknown%7CTWFpbGZsb3d8eyJFbXB0eU1hcGkiOnRydWUsIlYiOiIwLjAuMDAwMCIsIlAiOiJXaW4zMiIsIkFOIjoiTWFpbCIsIldUIjoyfQ%3D%3D%7C0%7C%7C%7C&sdata=MWjT59Nq1JnDHKig6JF%2FiV3XfnH7QPrTNMrNkJCFgIo%3D&reserved=0'
            >
              <LinkIcon className='w-4 h-4' />
              UAE Value Added Tax (VAT) Guide
            </a>
          </Button>
        </div>
      </Alert>

      <Alert className='flex gap-4 p-4 items-start text-md border-dashed border-blue-500 !bg-blue-50 dark:!bg-blue-400/20 mb-4'>
        <span className='bg-blue-100 p-2 rounded inline-block'>
          <Lightbulb className='w-4 h-4 stroke-blue-500' />
        </span>
        <div className='flex flex-col'>
          <AlertTitle className='leading-5 mb-2'>Tax Records:</AlertTitle>
          <AlertDescription>
            <ul className='list-disc pl-6 mb-2'>
              <li>
                All changes to a taxpayer’s records - such as a change of
                manager, address updates, new passport or EID details, or the
                issuance of a new license - must be reported to the Federal Tax
                Authority (FTA) within 20 business days of the change (on the
                FTA portal).
              </li>
            </ul>
            <p className='mb-2'>
              <strong>
                Special Welcome Offer - IFZA Corporate Consulting can assist you
                with these registrations at a discounted price (expiring after 3
                weeks of receiving this e-mail):
              </strong>
            </p>
            <ul className='list-disc pl-6 mb-2'>
              <li>AED 1,000 for Corporate Tax registration (once off)</li>
              <li>AED 1,100 for VAT registration (once off)</li>
              <li>AED 1,900 for both registrations</li>
            </ul>
          </AlertDescription>
        </div>
      </Alert>

      <p className='mb-2'>
        To ensure you are compliant with the UAE VAT and Corporate Tax
        registration deadlines for your Company, please refer to the below
        information:{' '}
        <Button
          variant={'link'}
          size={'lg'}
          className='p-0 text-md h-fit'
          asChild
        >
          <a
            href={
              'https://forms.zohopublic.com/ifzafjr/form/CorporateConsulting/formperma/7asmlZVKC1KDSjzdDVx8kuQCjGmql4rKZ7xFf-8MgP8'
            }
            target='_blank'
          >
            Corporate Consulting
            <ArrowUpRight className='w-3 h-3' />
          </a>
        </Button>
      </p>
      <p>
        We also offer accounting and tax filing support – reach out to us today
        to find out more!
      </p>

      <div className='flex flex-col md:flex-row gap-2 mt-4 mb-4'>
        <span className='w-full text-md bg-slate-100 dark:bg-white/10 py-4 px-6 rounded-lg text-neutral-600 dark:text-neutral-200 flex items-center gap-4'>
          <Mail className='h-5 w-5' />
          <a href='mailto:<EMAIL>'><EMAIL></a>
        </span>
        <span className='w-full text-md bg-slate-100 dark:bg-white/10 py-4 px-6 rounded-lg text-neutral-600 dark:text-neutral-200 flex items-center gap-4'>
          <Smartphone className='h-5 w-5' /> +971 50 245 3118
        </span>
      </div>
    </div>
  )
}

export const AttestationServices = () => {
  return (
    <div className='text-neutral-600 dark:text-neutral-400'>
      <p className='mb-2'>
        IFZA assists Corporate Shareholder structures with MoFA (Ministry of
        Foreign Affairs) attestations to ease the process of required
        documentation.
      </p>
      <p>
        Please contact them under{' '}
        <Button
          variant={'link'}
          size={'lg'}
          className='p-0 text-md h-fit'
          asChild
        >
          <a href='mailto:<EMAIL>' target='_blank'>
            <EMAIL>
          </a>
        </Button>{' '}
        for more information.
      </p>
    </div>
  )
}

export const AttestationAmendment = () => {
  return (
    <div className='text-neutral-600 dark:text-neutral-400'>
      <p className='mb-2'>
        If you need to make any changes to your license - such as a company name
        change, a shareholder structure amendment, an upgrade to your visa
        allocation, or any other modifications - our team is available to
        support you.
      </p>
      <p>
        You can reach us at{' '}
        <Button
          variant={'link'}
          size={'lg'}
          className='p-0 text-md h-fit'
          asChild
        >
          <a href='mailto:<EMAIL>' target='_blank'>
            <EMAIL>
          </a>
        </Button>{' '}
        with the details of the changes you’d like to make, and a member of our
        team will be happy to assist you.
      </p>
    </div>
  )
}

export const LicenseRenewal = () => {
  return (
    <div className='text-neutral-600 dark:text-neutral-400'>
      <p className='mb-2'>
        Within 60 days before the expiry date of your license, our License
        Renewal Professionals will be glad to guide and assist you with the
        renewal process.
      </p>
      <p className='mb-2'>
        For license renewal services, please email{' '}
        <Button
          variant={'link'}
          size={'lg'}
          className='p-0 text-md h-fit'
          asChild
        >
          <a href='mailto:<EMAIL>' target='_blank'>
            <EMAIL>
          </a>
        </Button>
        .
      </p>
      <p className='mb-2'>
        Kindly note that all trade licenses are required to be renewed on an
        annual basis. To avoid disruptions to your business operations and the
        incurrence of monthly penalties, we encourage you to begin the renewal
        process well in advance of the expiration date. Please be advised that a
        valid trade license is a federal requirement and holding an expired
        trade license may impact your residency visa status.
      </p>
    </div>
  )
}

export const ClientSupportLine = () => {
  return (
    <div className='text-neutral-600 dark:text-neutral-400'>
      <p className='mb-2'>
        Get immediate assistance from IFZA Client Experience Agents by
        contacting:
      </p>

      <div className='flex flex-col md:flex-row gap-2 mt-4'>
        <span className='w-full text-md bg-slate-100 dark:bg-white/10 py-4 px-6 rounded-lg text-neutral-600 dark:text-neutral-200 flex items-center gap-4'>
          <Phone className='h-5 w-5' />
          800-IFZA (4392) / +971 4 228 5285
        </span>
        <span className='w-full text-md bg-slate-100 dark:bg-white/10 py-4 px-6 rounded-lg text-neutral-600 dark:text-neutral-200 flex items-center gap-4'>
          <Smartphone className='h-5 w-5' />{' '}
          <a target='_blank' href='https://wa.me/+**********'>
            +971 4 216 0710 (WhatsApp)
          </a>
        </span>
      </div>
    </div>
  )
}