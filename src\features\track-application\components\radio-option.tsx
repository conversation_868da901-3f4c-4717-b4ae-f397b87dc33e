import { RadioGroupItem} from '@/components/ui/radio-group'

type RadioOptionProps = {
  value: string;
  label: string;
};

export function RadioOption({ value, label }: RadioOptionProps) {
  return (
    <div className="flex items-center space-x-2">
      <RadioGroupItem value={value} id={value} />
      <label htmlFor={value} className="text-sm font-medium leading-none">
        {label}
      </label>
    </div>
  )
}
