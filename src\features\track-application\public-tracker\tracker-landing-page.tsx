import TrackerHeader from '@/features/track-application/components/public-tracker/layout/header'
import TrackerFooter from '@/features/track-application/components/public-tracker/layout/footer'

import HeroBannerSection from '@/features/track-application/components/public-tracker/sections/hero-section'
import PublicTrackerInputSection from '@/features/track-application/components/public-tracker/sections/tracker-section'
import ServicesSection from '@/features/track-application/components/public-tracker/sections/services-section'
import ResourcesSection from '@/features/track-application/components/public-tracker/sections/resources-section'
import OnebaseBannerSection from '@/features/track-application/components/public-tracker/sections/onebase-section'

export default function PublicTrackerPage() {
  return (
    <>
      <TrackerHeader />

      <main className='w-full font-wotfard dark:bg-neutral-950'>
        <HeroBannerSection />
        <PublicTrackerInputSection />
        <ResourcesSection />
        <ServicesSection />
        <OnebaseBannerSection /> 
      </main>

      <TrackerFooter />
    </>
  )
}
