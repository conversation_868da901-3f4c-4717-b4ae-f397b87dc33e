import React from 'react'
import { useFormContext, useWatch } from 'react-hook-form'
import { DateTimePicker } from '@/components/ui/datetime-picker'
import FileUploadField from '@/components/ui/fileUpload'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { ApplicationFormValues } from '../../types/application-form-types'

interface Step4FormProps {
  setDate: React.Dispatch<React.SetStateAction<Date | undefined>>
  localValues: Record<
    | 'workingDays'
    | 'calendarDays'
    | 'basicSalary'
    | 'transportationAllowance'
    | 'accommodationAllowance'
    | 'otherAllowance',
    string
  >
  setLocalValues: React.Dispatch<
    React.SetStateAction<{
      workingDays: string
      calendarDays: string
      basicSalary: string
      transportationAllowance: string
      accommodationAllowance: string
      otherAllowance: string
    }>
  >
}

const Step4VisaApplication: React.FC<Step4FormProps> = ({
  setDate,
  localValues,
  setLocalValues,
}) => {
  const form = useFormContext<ApplicationFormValues>()
  const visaType = useWatch({ control: form.control, name: 'visaType' })
  const returnTicket = useWatch({ control: form.control, name: 'returnTicket' })
  const annualLeaveEntitlement = useWatch({
    control: form.control,
    name: 'annualLeaveEntitlement',
  })
  const doYouHaveADegree = useWatch({
    control: form.control,
    name: 'doYouHaveADegree',
  })

  // Log all form values for debugging
  console.log('Step 4 Form Values:', form.watch())

  return (
    <>
      <form className='space-y-4 fz-form'>
        <div className='flex flex-wrap space-between gap-y-4'>
          {/* Type of Employment */}
          <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
            <FormField
              control={form.control}
              name='typeOfEmployment'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Type of Employment <span className='text-red-500'>*</span>
                  </FormLabel>
                  <FormControl>
                    <Input {...field} placeholder='' disabled />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          {/* Employment Duration in Months */}
          <div className='flex-1'>
            <FormField
              control={form.control}
              name='employmentDuration'
              render={({ field }) => (
                <FormItem data-error-field='educationQualification'>
                  <FormLabel>
                    Employment Duration in Months{' '}
                    <span style={{ color: 'red' }}>*</span>
                  </FormLabel>
                  <FormControl>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <SelectTrigger>
                        <SelectValue placeholder='Choose' />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='1 Month'>1 Month</SelectItem>
                        <SelectItem value='12 Months'>12 Months</SelectItem>
                        <SelectItem value='24 Months'>24 Months</SelectItem>
                        <SelectItem value='36 Months'>36 Months</SelectItem>
                        <SelectItem value='16 Months'>16 Months</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormDescription>
                    Maximum of 24 Months / 2 Years
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Do you have an attested degree ? */}

        <FormField
          control={form.control}
          name='doYouHaveADegree'
          render={({ field }: any) => (
            <FormItem>
              <FormLabel>
                Do you have an attested degree ?{' '}
                <span className='text-red-500'>*</span>
              </FormLabel>
              <div data-field='doYouHaveADegree'>
                <FormControl>
                  <RadioGroup
                    className='flex align-center'
                    value={field.value}
                    onValueChange={field.onChange}
                  >
                    <RadioGroupItem className='mt-1' value='Yes' id='YES' />
                    <label htmlFor='YES'>Yes</label>
                    <RadioGroupItem className='mt-1' value='No' id='NO' />
                    <label htmlFor='NO'>No</label>
                  </RadioGroup>
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Job Title (Attested Degree) */}

        {doYouHaveADegree === 'Yes' && (
          <>
            <FormField
              control={form.control}
              name='attestedDegree'
              render={({ field }) => (
                <FormItem data-error-field='attestedDegree'>
                  <FormLabel>
                    Job Title (Attested Degree){' '}
                    <span className='text-red-500'>*</span>
                  </FormLabel>
                  <FormControl>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <SelectTrigger>
                        <SelectValue placeholder='Select' />
                      </SelectTrigger>
                      <SelectContent
                        position='popper'
                        side='bottom'
                        sideOffset={5}
                        searchable
                        searchPlaceholder='Search job titles...'
                      >
                        {[
                          'Accountant',
                          'Administration Advisor',
                          'Administration Directors',
                          'Administration Manager',
                          'Administrative Assistant',
                          'Administrative Director',
                          'Administrative Officer',
                          'Administrative Supervisor',
                          'Advertising Manager',
                          'Architectural Engineers',
                          'Assistant Engineer',
                          'Assistant Manager',
                          'Assistant Managing Director',
                          'Business Development Executive',
                          'Cinema Director',
                          'Cleaner General',
                          'Clerk Assistant',
                          'Company Clerk',
                          'Consultant Engineer',
                          'Credit Officer',
                          'Director/Cinema And Tv Films',
                          'Electrical Engineer',
                          'Electronics Engineer',
                          'Engineering Manager',
                          'Executed Assistant',
                          'Finance Manager',
                          'Financial Officer',
                          'General Consultant',
                          'General Geologist',
                          'General Manager',
                          'HR Director',
                          'HR Manager',
                          'HR Officer',
                          'Human Resource Director',
                          'Interior Designer',
                          'Investor',
                          'IT Specialist',
                          'Legal Advisor',
                          'Managing Director',
                          'Marketing Assistant',
                          'Marketing Manager',
                          'Medical Doctor',
                          'Medical Sales Representative',
                          'Office Clerk General',
                          'Office Manager',
                          'Operations Analyst',
                          'Operations Manager',
                          'Partner',
                          'Photographer',
                          'Production Manager',
                          'Projects Manager',
                          'Quality Engineer',
                          'Reception Officer',
                          'Receptionist',
                          'Sales Manager',
                          'Sales Officer',
                          'Sales Representative',
                          'Sales Supervisor',
                          'Secretary',
                          'Senior Officer of Legal Affairs',
                          'Senior Supervisor',
                          'Software Specialist',
                          'Supervisor',
                          'System Administrator',
                          'System Analyst',
                          'System Engineer',
                          'Teacher',
                          'Vice President',
                          'Web Developer',
                        ].map((title) => (
                          <SelectItem key={title} value={title}>
                            {title}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormDescription>
                    The title which will appear on the residence visa stamped on
                    your passport. Please note these titles are subject to
                    change ay any point during the Visa process. Please let us
                    know if you have any questions.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </>
        )}

        {/* Job Title (No Attested Degree) */}

        {doYouHaveADegree === 'No' && (
          <>
            <FormField
              control={form.control}
              name='noAttestedDegree'
              render={({ field }) => (
                <FormItem data-error-field='noAttestedDegree'>
                  <FormLabel>
                    Job Title (No Attested Degree){' '}
                    <span className='text-red-500'>*</span>
                  </FormLabel>
                  <FormControl>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <SelectTrigger>
                        <SelectValue placeholder='Choose' />
                      </SelectTrigger>
                      <SelectContent
                        position='popper'
                        side='bottom'
                        sideOffset={5}
                        searchable
                        searchPlaceholder='Search job titles...'
                      >
                        {[
                          'Accounting Finance Clerks',
                          'Administrative Assistant',
                          'Archive Clerk',
                          'Cleaner General',
                          'Clerk Assistant',
                          'Company Clerk',
                          'Computer Operator',
                          'Customer Service Representative',
                          'Driver',
                          'Executive Assistant',
                          'Hair Stylist',
                          'Heavy Truck Driver',
                          'Investor',
                          'Marketing Assistant',
                          'Marketing Executive',
                          'Messenger',
                          'Musician',
                          'Office Clerks General',
                          'Partner',
                          'Petroleum Product Sales Gen',
                          'Public Relations Clerk',
                          'Reception Officer',
                          'Receptionist',
                          'Sales Representative',
                          'Secretary',
                          'Security Staff',
                          'Senior Financial Accounts',
                          'Senior Operator',
                          'Supervisor',
                          'Tailor General',
                          'Technical Assistant',
                        ].map((title) => (
                          <SelectItem key={title} value={title.trim()}>
                            {title.trim()}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormDescription>
                    The title which will appear on the residence visa stamped on
                    your passport. Please note these titles are subject to
                    change ay any point during the Visa process. Please let us
                    know if you have any questions.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </>
        )}

        {/* Job Title (GM) */}

        {/* {(doYouHaveADegree === 'Yes' || doYouHaveADegree === 'No') &&
          isGeneralManager === 'Yes' && (
            <>
              <FormField
                control={form.control}
                name='jobTitleGM'
                render={({ field }) => (
                  <FormItem data-error-field='jobTitleGM'>
                    <FormLabel>
                      Job Title (GM) <span className='text-red-500'>*</span>
                    </FormLabel>
                    <FormControl>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder='Choose' />
                        </SelectTrigger>
                        <SelectContent
                          position='popper'
                          side='bottom'
                          sideOffset={5}
                          searchable
                          searchPlaceholder='Search job titles (GM) ...'
                        >
                          {[
                            'Accountant',
                            'Administration Directors',
                            'Administrative Advisor',
                            'Administrative Assistant',
                            'Administrative Director',
                            'Administrative Officer',
                            'Administrative Supervisor',
                            'Advertising Manager',
                            'Architectural Engineers',
                            'Assistant Manager',
                            'Assistant Managing Director',
                            'Business Development Executive',
                            'Chairman of the board',
                            'Chief Executive Officer',
                            'Cleaner General',
                            'Clerk Assistant',
                            'Company Clerk',
                            'Computer Operator',
                            'Customer Service Representative',
                            'Driver',
                            'Electronics Engineer',
                            'Engineering Manager',
                            'Executed Assistant',
                            'Executive Assistant',
                            'Finance Manager',
                            'Financial & Administrative Director',
                            'General Consultant',
                            'General Geologist',
                            'General Manager',
                            'Hair Stylist',
                            'HR Director',
                            'HR Manager',
                            'HR Officer',
                            'Human Resource Director',
                            'Interior Designer',
                            'Investor',
                            'Managing Director',
                            'Marketing Assistant',
                            'Marketing Manager',
                            'Medical Doctor',
                            'Messenger',
                            'Musician',
                            'Office Clerk General',
                            'Office Clerks General',
                            'Office Manager',
                            'Operations Analyst',
                            'Operations Manager',
                            'Partner',
                            'Petroleum Product Sales Gen',
                            'Photographer',
                            'Public Relations Clerk',
                            'Reception Officer',
                            'Receptionist',
                            'Sales Manager',
                            'Sales Officer',
                            'Sales Representative',
                            'Sales Supervisor',
                            'Secretary',
                            'Security Staff',
                            'Senior Officer of Legal Affairs',
                            'Senior Operator',
                            'Senior Supervisor',
                            'Software Specialist',
                            'Supervisor',
                            'System Administrator',
                            'System Engineer',
                            'Vice President',
                            'Web Developer',
                          ].map((title) => (
                            <SelectItem key={title} value={title}>
                              {title}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </>
          )} */}

        {/* Education Qualification */}
        <FormField
          control={form.control}
          name='educationQualification'
          render={({ field }) => (
            <FormItem data-error-field='educationQualification'>
              <FormLabel>
                Educational Qualification{' '}
                <span style={{ color: 'red' }}>*</span>
              </FormLabel>
              <FormControl>
                <Select onValueChange={field.onChange} value={field.value}>
                  <SelectTrigger>
                    <SelectValue placeholder='Choose' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='No formal education'>
                      No formal education
                    </SelectItem>
                    <SelectItem value='Primary'>Primary</SelectItem>
                    <SelectItem value='High School'>High School</SelectItem>
                    <SelectItem value='Vocational'>Vocational</SelectItem>
                    <SelectItem value="Bachelor's Degree">
                      Bachelor's Degree
                    </SelectItem>
                    <SelectItem value="Master's Degree">
                      Master's Degree
                    </SelectItem>
                    <SelectItem value='Doctorate'>Doctorate</SelectItem>
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Upload Attested Degree */}

        {doYouHaveADegree === 'Yes' && (
          <>
            <FormField
              control={form.control}
              name='uploadAttestedDegree'
              render={({ field }) => (
                <FormItem data-field='uploadAttestedDegree'>
                  <FormLabel>
                    Upload Attested Degree{' '}
                    <span className='text-red-500'>*</span>
                  </FormLabel>
                  <FormControl>
                    <FileUploadField
                      accept='.jpg,.jpeg,.png'
                      value={field.value}
                      onchoose={(file) => {
                        form.setValue('uploadAttestedDegree', file || '')
                        form.clearErrors('uploadAttestedDegree')
                      }}
                    />
                  </FormControl>
                  <FormDescription>
                    Select FileNo file chosen For an outside country
                    application, degree must be attested by UAE embassy in the
                    applicant's home country & in UAE by MOFA. <br />
                    For an inside country application, degree must be attested
                    in UAE by MOFA.{' '}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </>
        )}

        {/* Do you want to change the Job title of the Visa holder? */}
        {visaType === 'Employment Visa Renewal' && (
          <>
            <div className='mb-4'>
              <FormField
                control={form.control}
                name='jobTitleChange'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Do you want to change the Job title of the Visa holder ?
                    </FormLabel>
                    <FormControl>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder='Choose' />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value='Yes'>Yes</SelectItem>
                          <SelectItem value='No'>No</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </>
        )}
        {/* Employment Start Date */}
        <div className='flex flex-wrap space-between gap-y-4'>
          <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
            <FormField
              control={form.control}
              name='employmentStartDate'
              render={({ field }) => (
                <FormItem data-error-field='employmentStartDate'>
                  <FormLabel>
                    Employment Start Date{' '}
                    <span className='text-red-500'>*</span>
                  </FormLabel>
                  <DateTimePicker
                    granularity='day'
                    value={field.value}
                    onChange={(date) => {
                      field.onChange(date)
                      setDate(date)
                    }}
                    displayFormat={{
                      hour24: 'dd MMMM yyyy',
                    }}
                  />
                  <FormDescription>dd/MM/yyyy</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Probation Period : Drop down*/}
          <div className='flex-1'>
            <FormField
              control={form.control}
              name='probationPeriod'
              render={({ field }) => (
                <FormItem data-error-field='probationPeriod'>
                  <FormLabel>
                    Probation Period <span className='text-red-500'>*</span>
                  </FormLabel>
                  <FormControl>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <SelectTrigger>
                        <SelectValue placeholder='Choose' />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='1'>1</SelectItem>
                        <SelectItem value='3'>3</SelectItem>
                        <SelectItem value='6'>6</SelectItem>
                        <SelectItem value='0'>0</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
        {/* Employment Termination Notice */}
        <div className='flex flex-wrap space-between gap-y-4'>
          <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
            <FormField
              control={form.control}
              name='employmentTerminationNotice'
              render={({ field }) => (
                <FormItem data-error-field='employmentTerminationNotice'>
                  <FormLabel>
                    Employment Termination Notice{' '}
                    <span className='text-red-500'>*</span>
                  </FormLabel>
                  <FormControl>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <SelectTrigger>
                        <SelectValue placeholder='Choose' />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='1 Month'>1 Month</SelectItem>
                        <SelectItem value='2 Months'>2 Months</SelectItem>
                        <SelectItem value='3 Months'>3 Months</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Return Ticket Eligibility */}
          <div className='flex-1'>
            <FormField
              control={form.control}
              name='returnTicket'
              render={({ field }) => (
                <FormItem data-error-field='returnTicket'>
                  <FormLabel>
                    Return Ticket Eligibility{' '}
                    <span className='text-red-500'>*</span>
                  </FormLabel>
                  <FormControl>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <SelectTrigger>
                        <SelectValue placeholder='Choose' />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='Economy'>Economy</SelectItem>
                        <SelectItem value='Business'>Business</SelectItem>
                        <SelectItem value='First Class'>First Class</SelectItem>
                        <SelectItem value='No Entitlement'>
                          No Entitlement
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
        {/* Ticket Entitlement Period */}

        {(returnTicket === 'Economy' ||
          returnTicket === 'Business' ||
          returnTicket === 'First Class') && (
          <>
            <div className='mb-4'>
              <FormField
                control={form.control}
                name='ticketEntitlementPeriod'
                render={({ field }) => (
                  <FormItem data-error-field='ticketEntitlementPeriod'>
                    <FormLabel>
                      Ticket Entitlement Period{' '}
                      <span style={{ color: 'red' }}>*</span>
                    </FormLabel>
                    <FormControl>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder='Choose' />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value='1 Year'>1 Year</SelectItem>
                          <SelectItem value='2 Years'>2 Years</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </>
        )}

        {/* Annual Leave Entitlement */}
        <div className='mb-4'>
          <FormField
            control={form.control}
            name='annualLeaveEntitlement'
            render={({ field }) => (
              <FormItem data-error-field='annualLeaveEntitlement'>
                <FormLabel data-error-field='annualLeaveEntitlement'>
                  Annual Leave Entitlement{' '}
                  <span className='text-red-500'>*</span>
                </FormLabel>
                <FormControl>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <SelectTrigger>
                      <SelectValue placeholder='Choose' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='Working Days'>Working Days</SelectItem>
                      <SelectItem value='Calendar Days'>
                        Calendar Days
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Number of Leave Days (Working Days) */}
        {annualLeaveEntitlement === 'Working Days' && (
          <>
            <div className='mb-4'>
              <FormField
                control={form.control}
                name='workingDays'
                render={({ field }) => (
                  <FormItem data-error-field='workingDays'>
                    <FormLabel>
                      Number of Leave Days (Working Days){' '}
                      <span className='text-red-500'>*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        type='number'
                        placeholder='Enter'
                        value={localValues.workingDays}
                        onChange={(e) => {
                          const value = e.target.value
                          setLocalValues((prev) => ({
                            ...prev,
                            workingDays: value,
                          }))

                          if (value === '') {
                            field.onChange(undefined)
                            form.setError('workingDays', {
                              type: 'manual',
                              message: 'This field is required',
                            })
                          } else if (/^\d+$/.test(value)) {
                            field.onChange(Number(value))
                            form.clearErrors('workingDays')
                          } else {
                            field.onChange(undefined)
                          }
                        }}
                        onKeyDown={(e) => {
                          const invalidChars = ['e', 'E', '+', '-', '.']
                          if (invalidChars.includes(e.key)) {
                            e.preventDefault()
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </>
        )}

        {/* Number of Leave Days (Calendar Days) */}
        {annualLeaveEntitlement === 'Calendar Days' && (
          <>
            <div className='mb-4'>
              <FormField
                control={form.control}
                name='calendarDays'
                render={({ field }) => (
                  <FormItem data-error-field='calendarDays'>
                    <FormLabel>
                      Number of Leave Days (Calendar Days){' '}
                      <span className='text-red-500'>*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        type='number'
                        placeholder='Enter'
                        value={localValues.calendarDays}
                        onChange={(e) => {
                          const value = e.target.value
                          setLocalValues((prev) => ({
                            ...prev,
                            calendarDays: value,
                          }))

                          if (value === '') {
                            field.onChange(undefined)
                            form.setError('calendarDays', {
                              type: 'manual',
                              message: 'This field is required',
                            })
                          } else if (/^\d+$/.test(value)) {
                            field.onChange(Number(value))
                            form.clearErrors('calendarDays')
                          } else {
                            field.onChange(undefined)
                          }
                        }}
                        onKeyDown={(e) => {
                          const invalidChars = ['e', 'E', '+', '-', '.']
                          if (invalidChars.includes(e.key)) {
                            e.preventDefault()
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </>
        )}
      </form>
    </>
  )
}

export default Step4VisaApplication
