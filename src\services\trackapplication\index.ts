// Service for tracking application status
import axios from 'axios';
import { determineApplicationSteps } from '@/features/track-application/utils/application-step-utils';
import { BASE_URL } from '@/utils/network';

export async function fetchApplicationStatus(appNumber: string) {
  try {
    let res;
    let applicationCategory: string;
    let applicationType: string = 'New'; // Default to 'New'
    
    // Determine category and type based on tracker ID and application data
    if(appNumber.startsWith('DL')){
      applicationCategory = 'License';
      // For license, we need to check if it's renewal or new from the data
      res = await axios.get(`${BASE_URL}/api/tracker/trackDealByID/${encodeURIComponent(appNumber)}`);
    } else {
      applicationCategory = 'Visa';
      // applicationType already set to 'New' by default for visa
      res = await axios.get(`${BASE_URL}/api/tracker/trackVisaByID/${encodeURIComponent(appNumber)}`);
    }
    
    if (!res.data || !res.data.data) {
      return { found: false };
    }
    
    const data = res.data.data;
    
    // For license applications, determine if it's renewal or new
    if (applicationCategory === 'License') {
      // Check if it's a renewal based on Type
      const isRenewal = data.Type && data.Type.toLowerCase().includes('renewal');
      applicationType = isRenewal ? 'Renewal' : 'New';
    }

    if(applicationCategory === 'Visa'){
      const isRenewal = data.Service_Type && data.Service_Type.toLowerCase().includes('renewal');
      applicationType = isRenewal ? 'Renewal' : 'New';
    }
    
    // Use determineApplicationSteps to get the currentStep
    const { currentStep, stepTimestamps, steps } = determineApplicationSteps(data, applicationCategory, applicationType);
    
    // Determine correct application number based on category
    let applicationNumber: string;
    if (applicationCategory === 'Visa') {
      applicationNumber = data.VisaAppID || appNumber || data.id || '';
    } else {
      applicationNumber = data.Deal_AppID || appNumber || '';
    }
    
    return {
      found: true,
      details: {
        applicationType: applicationType,
        status: currentStep || '',
        applicationNumber: applicationNumber,
        applicationDate: data.Created_Time ? data.Created_Time : '',
        category: applicationCategory, // Return original category for display
        type: applicationType
      },
      stepTimestamps,
      currentStep,
      steps,
      raw: data,
      applicationCategory
    };
  } catch (err) {
    return { found: false };
  }
}
