# Copilot Instructions for PortalWeb

## Project Overview
- **PortalWeb** is a React + TypeScript app for the IFZA "Customers Hub" portal, built with Vite.
- Uses **TanStack Router** for navigation, **React Query** for data fetching, and **TailwindCSS** + **ShadcnUI** for UI.
- The app is modular: each business domain (e.g., license, visa, letters) is a feature folder under `src/features/`.
- API communication is abstracted in `src/services/` (e.g., `licenseapplication`, `visaapplication`).
- Theming (light/dark) is managed via a context provider in `src/context/theme-context.tsx`.

## Key Patterns & Conventions
- **Forms**: Use React Hook Form + Zod for validation. See `src/features/visa-application/index.tsx` for a complex, multi-step form example.
- **File Uploads**: Files are converted to base64 and sent as arrays of `{ type, base64 }` objects. Always map frontend file fields to backend-required `type` values (see `src/services/visaapplication/index.ts`).
- **API Calls**: Use service helpers (e.g., `createVisaApplication`, `uploadVisaDocuments`). Always handle errors and map payloads to backend expectations.
- **UI Components**: Prefer ShadcnUI components in `src/components/ui/`. Use Tailwind utility classes for layout and styling.
- **Routing**: All routes are defined using TanStack Router in `src/main.tsx` and `src/routes/`.
- **State Management**: Use context or Zustand stores in `src/stores/` for global state (e.g., auth).
- **Country Dropdowns**: To override country names, use the override map in `src/components/ui/country-dropdown.tsx`.

## Developer Workflows
- **Start Dev Server**: `npm run dev`
- **Install Dependencies**: `npm install`
- **Lint**: `npm run lint`
- **Build**: `npm run build`
- **Format**: `npm run format`
- **Test**: (No test suite by default; add tests as needed)

## Integration & Data Flow
- **Frontend-to-Backend**: All form submissions go through service helpers in `src/services/`. Always check backend API docs for required payload structure.
- **File Upload Sequence**: For forms requiring file uploads, first create the application (e.g., visa), then upload documents using the returned ID.
- **Error Handling**: Always surface backend errors to the user via toast notifications (see `use-toast`).

## Examples
- Multi-step form: `src/features/visa-application/index.tsx`
- File upload mapping: `src/services/visaapplication/index.ts`
- Theming: `src/context/theme-context.tsx`
- UI patterns: `src/components/ui/`

## Notes
- Do not hardcode API URLs; use `BASE_URL` from `src/utils/network.ts`.
- Follow the established file/folder structure for new features.
- All code must be TypeScript and use functional React components.

---
If any section is unclear or missing, please request clarification or provide feedback for improvement.
