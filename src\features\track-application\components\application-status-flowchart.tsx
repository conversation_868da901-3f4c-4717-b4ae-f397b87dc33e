import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Check } from 'lucide-react'
import ApplicationDescription from './application-description'
import { ApplicationStep } from '../enums/application-step-enum'

interface ApplicationStatusProps {
  steps: ApplicationStep[];
  currentStep: number;
  stepTimestamps: Partial<Record<ApplicationStep, string>>;
}

export default function ApplicationStatus({ steps, currentStep, stepTimestamps }: ApplicationStatusProps) {
  return (
    <Card className="border-none shadow-none pb-8 px-2 bg-white dark:bg-slate-800">
      <CardHeader className="pb-2">
        <CardTitle className='text-base font-bold border-[#d6ae36] pb-2'>
          Application Status
        </CardTitle>
      </CardHeader>
      <CardContent className="ml-4 pl-6 relative">
        <div className="flex flex-col relative">
          {steps.map((step, idx) => {
            const isDone = idx <= currentStep;
            const isActive = idx === currentStep;
            const isLast = idx === steps.length - 1;
            const timestamp = stepTimestamps?.[step as ApplicationStep];

            return (
              <div key={step} className="relative flex items-start ">
                {/* vertical line to come after each step ,except last step */}
                {!isLast && (
                  <div
                    className={` absolute top-0 left-[11px] h-full w-[2px] ${isDone ? 'bg-[#C2A01E]' : 'bg-gray-200'}`}
                    style={{ top: '10px' }}
                  />
                )}

                {/* Step icon */}
                <div className="relative z-20 mr-9 ">
                  {isDone ? (
                    <div className="w-6 h-6 bg-[#C2A01E] rounded-md flex items-center justify-center ring-[6px] ring-[#C2A01E] ">
                      <Check size={32} color="white" strokeWidth={3} />
                    </div>
                  ) : isActive ? (
                    <div className="w-6 h-6 bg-[#C2A01E] rounded-md flex items-center justify-center ring-[6px] ring-[#EAD484]">
                      <div className="w-2.5 h-2.5 bg-white rounded-[1px]" />
                    </div>
                  ) : (
                    <div className="w-8 h-8 -ml-[4px] flex items-center justify-center">
                      <div className="w-8 h-8 rounded-[10px] border-2 border-[#E5E7EB] bg-white flex items-center justify-center">
                        <div className="w-3 h-3 rounded-[4px] bg-slate-400" />
                      </div>
                    </div>
                  )}
                </div>

                {/* step description */}
                <ApplicationDescription step={step} timestamp={timestamp}/>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}