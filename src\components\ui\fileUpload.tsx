import { useRef, useState, useEffect } from 'react'
import { Upload, CircleX } from 'lucide-react'
import { Input } from '@/components/ui/input'

interface FileUploadFieldProps {
  accept: string
  onchoose?: (file: File | null) => void // Callback to handle file selection
  value?: File | null
}

export default function FileUploadField({
  accept,
  onchoose,
  value,
}: FileUploadFieldProps) {
  const inputRef = useRef<HTMLInputElement | null>(null)
  const [fileChosen, setFileChosen] = useState(false)

  // Track if file is chosen from external value
  useEffect(() => {
    setFileChosen(value instanceof File)
  }, [value])

  // Clear selected file and reset input value
  const handleClear = () => {
    if (inputRef.current) {
      inputRef.current.value = ''
    }
    setFileChosen(false)
    onchoose?.(null)
  }

  // Handle file input change event
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      const file = files[0]
      setFileChosen(true)
      onchoose?.(file)
    }
  }

  // Trigger native file dialog programmatically
  const triggerFileDialog = () => {
    inputRef.current?.click()
  }

  return (
    <div className='relative w-full'>
      {/* Hidden native file input */}
      <Input
        ref={inputRef}
        type='file'
        accept={accept}
        className='hidden'
        onChange={handleChange}
      />

      {/* Visible clickable button that opens file dialog */}
      <button
        type='button'
        onClick={triggerFileDialog}
        className='flex items-center justify-between border rounded-md px-3 py-[6px] bg-white shadow-sm w-full text-sm leading-tight'
        style={{
          height: 36, // fixed height to match inputs
          boxSizing: 'border-box',
          fontFamily: 'inherit',
          fontWeight: 'normal',
        }}
      >
        {/* Left side: file name + delete button */}
        <div className='flex items-center flex-grow min-w-0'>
          {(fileChosen || value instanceof File) && value?.name ? (
            <>
              <span className='truncate text-gray-700'>{value.name}</span>
              <span
                title='Delete file'
                className='ml-2 cursor-pointer flex-shrink-0'
                onClick={(e) => {
                  e.stopPropagation()
                  handleClear()
                }}
              >
                <CircleX className='w-4 h-4 text-red-500 hover:text-red-700' />
              </span>
            </>
          ) : (
            <span className='text-gray-400'>No file chosen</span>
          )}
        </div>

        {/* Right side: Upload icon + Choose/Replace File label */}
        <div
          className='flex items-center gap-1 cursor-pointer flex-shrink-0 ml-4 whitespace-nowrap'
          onClick={(e) => {
            e.stopPropagation()
            triggerFileDialog()
          }}
        >
          <Upload className='w-4 h-4' />
          <span>{fileChosen || value ? 'Replace File' : 'Choose File'}</span>
        </div>
      </button>
    </div>
  )
}
