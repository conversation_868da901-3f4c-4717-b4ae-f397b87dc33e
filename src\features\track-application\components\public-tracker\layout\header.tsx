import { useState, useEffect } from "react";
import { Link } from '@tanstack/react-router'
import { Menu, X, Sun, Moon } from 'lucide-react'
import fzLogoDark from '@/assets/images/Ifza_logo.svg'
import { useTheme } from '@/context/theme-context'
import { Button } from '@/components/ui/button'

export const navLinks = [
  {
    label: 'Track Application',
    href: '#tracker-application',
  },
  {
    label: 'Resources',
    href: '#resources',
  },
  {
    label: 'Services',
    href: '#services',
  },
]

interface NavLinkProps {
  href: string
  children: React.ReactNode
  mobile?: boolean
  onClick?: () => void
  className?: string
}

export const NavLink = ({
  href,
  children,
  mobile = false,
  onClick,
  className,
}: NavLinkProps) => {
  return (
    <a
      href={href}
      className={`font-medium transition-colors duration-300 hover:text-primary-500 ${mobile ? 'text-slate-900 dark:text-slate-50 text-md block w-full' : 'text-slate-700'} ${className}`}
      onClick={onClick}
    >
      {children}
    </a>
  )
}

const TrackerNavHeader = () => {
  const { theme, setTheme } = useTheme()

  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)

  useEffect(() => {
    const handleScroll = () => setIsScrolled(window.scrollY > 10)
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const toggleTheme = () => {
    if (theme === 'light') {
      setTheme('dark')
    } else {
      setTheme('light')
    }
  }

  return (
    <header className='fixed font-tt-firs-neue text-sm uppercase top-0 left-0 right-0 z-50 transition-all duration-300 bg-transparent px-4 py-5'>
      <div
        className={`container mx-auto transition-all duration-300 text-slate-900 ${
          isScrolled
            ? 'max-w-5xl bg-slate-50 border-2 border-slate-100 dark:bg-neutral-700/95 dark:border-neutral-600 py-3 px-6 rounded-full'
            : 'px-4 md:px-6'
        }`}
      >
        <div className='flex items-center justify-between'>
          <a href='/' className='flex items-center'>
            <img
              src={fzLogoDark}
              alt='ifza-logo'
              className={`transition-all duration-300 ease-in-out ${
                isScrolled
                  ? 'w-[145px] h-[30px] dark:invert dark:brightness-[-100%]'
                  : 'w-[151px] h-[36px]'
              }`}
            />
          </a>

          {/* Desktop Navigation */}
          <nav className='hidden lg:flex items-center space-x-8'>
            {navLinks.map(({ label, href }) => (
              <NavLink
                key={href}
                href={href}
                className={`${isScrolled ? 'dark:text-slate-50' : ''}`}
              >
                {label}
              </NavLink>
            ))}
            <Button
              variant={'secondary'}
              size={'lg'}
              className='rounded-full px-6 py-4 text-sm uppercase hover:bg-white dark:bg-neutral-900'
            >
              <Link to={'/'}>Login</Link>
            </Button>

            <button
              onClick={toggleTheme}
              className='pr-2 inline-flex items-center hover:text-secondary-color mr-4 md:mr-0 md:ml-10'
            >
              {theme === 'light' ? (
                <Sun className='h-5 w-5' />
              ) : (
                <Moon className='h-5 w-5 text-white' />
              )}
            </button>
          </nav>

          {/* Mobile menu button */}
          <button
            className='lg:hidden text-slate-700 dark:text-slate-200 focus:outline-none'
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? (
              <X className='h-6 w-6' />
            ) : (
              <Menu className='h-6 w-6' />
            )}
          </button>
        </div>
      </div>

      {/* Mobile Navigation */}
      <div
        className={`lg:hidden bg-white dark:bg-neutral-700/95 ${
          isMenuOpen ? 'max-h-screen py-4 shadow-md' : 'max-h-0 py-0'
        } mt-2 rounded-xl overflow-hidden transition-all duration-300`}
      >
        <div className='container mx-auto px-4'>
          <div className='flex flex-col space-y-4 text-center'>
            {navLinks.map(({ label, href }) => (
              <NavLink
                key={href}
                href={href}
                mobile
                onClick={() => setIsMenuOpen(false)}
              >
                {label}
              </NavLink>
            ))}
            <Button
              variant={'secondary'}
              size={'lg'}
              className='rounded-full px-6 py-4 text-sm uppercase hover:bg-white dark:bg-neutral-900'
            >
              <Link to={'/'}>Login</Link>
            </Button>
          </div>
        </div>
      </div>
    </header>
  )
}

export default TrackerNavHeader;