import { z } from 'zod'

export const applicationFormSchema = z.object({
  appUpdatesEmail: z.string().email({ message: 'Invalid email address.' }),
  whatsAppNumber: z.string().optional(),
  applicationDate: z
    .union([z.date(), z.string().transform((str) => new Date(str))])
    .refine((date) => !isNaN(date.getTime()), {
      message: 'Invalid date format',
    }),

  visaType: z.string().min(1, { message: 'Select a choice.' }),
  serviceType: z.string().optional(),
  tradeLicenseValidated: z.boolean().refine((val) => val === true, {
    message:
      'You must confirm the trade license is valid for at least 60 days.',
  }),
  visaApplicationType: z.string().min(1, { message: 'Select a choice.' }),
  nationality: z.string().min(1, { message: 'Select a choice.' }),
  residentVisaStamping: z.string().min(1, { message: 'Select a choice.' }),
  familyOnHoldLetter: z.string().min(1, { message: 'Select a choice.' }),
  renewalDisclaimer: z.boolean().refine((val) => val === true, {
    message: 'Select this option.',
  }),

  visaFree: z.string().min(1, { message: 'Select a choice.' }).optional(),

  outsideCountryInstructions: z
    .boolean()
    .refine((val) => val === true, {
      message: 'You must confirm you will be outside UAE.',
    })
    .optional(),

  outsideCountry: z
    .boolean()
    .refine((val) => val === true, {
      message: 'You must confirm you have no active visa in UAE.',
    })
    .optional(),
  updatedRVCopy: z
    .any()
    .refine((val) => val instanceof File && val.size > 0, {
      message: 'Upload a file here.',
    })
    .optional(),
  idCardUpload: z
    .any()
    .refine((val) => val instanceof File && val.size > 0, {
      message: 'Upload a file here.',
    })
    .optional(),

  currentVisaStatus: z
    .string()
    .min(1, { message: 'Select a choice.' })
    .optional(),
  visaValidUntil: z.date().optional(),

  title: z.string().min(1, { message: 'Select a choice.' }),
  firstName: z.string().min(2, { message: 'Required.' }),
  middleName: z.string().optional(),
  lastName: z.string().min(2, { message: 'Required.' }),
  title1: z.string().optional(),
  arabicName: z.string().min(1, { message: 'Select a choice.' }),
  firstNameArabic: z.string().optional(),
  middleNameArabic: z.string().optional(),
  lastNameArabic: z.string().optional(),
  emailAddress: z.string().email({ message: 'Invalid email address.' }),
  phone: z.string().min(5, { message: 'You must enter at least 5 digits.' }),
  streetAddress: z.string().optional(),
  addressLine: z.string().optional(),
  cityAddress: z.string().optional(),
  stateProvince: z.string().optional(),
  country: z.string().optional(),
  streetAddress1: z
    .string()
    .min(2, { message: 'Enter a value for this field.' }),
  addressLine2: z.string().optional(),
  city: z.string().min(2, { message: 'Enter a value for this field.' }),
  province: z.string().optional(),
  country1: z.string().min(1, { message: 'Select a choice.' }),

  emiratesID: z.string().min(1, { message: 'Select a choice.' }),
  emiratesIDNumber: z
    .string()
    .min(2, { message: 'Enter a value for this field.' }),
  emiratesIDExpiryDate: z
    .preprocess((val) => (val === '' ? undefined : val), z.date())
    .refine((val) => val !== undefined, {
      message: 'Select a date',
      path: ['emiratesIDExpiryDate'],
    }),

  emiratesIDCopy: z
    .any()
    .refine((val) => val instanceof File && val.size > 0, {
      message: 'Upload a file here.',
    })
    .optional(),

  // nationality1: z.string().min(1, { message: 'Select a choice.' }),

  passportNumber: z
    .string()
    .min(2, { message: 'Enter a value for this field.' }),
  placeOfIssue: z.string().min(2, { message: 'Enter a value for this field.' }),
  placeOfIssueArabic: z.string().optional(),
  passportType: z.string().min(1, { message: 'Select a choice.' }),
  agreementPassportRules: z.boolean().refine((val) => val === true, {
    message: 'You must accept the terms and conditions.',
  }),

  eVisaApplicationType: z
    .any()
    // .min(1, { message: 'Select a choice.' })
    .optional(),

  Agreed: z
    .boolean()
    .refine((val) => val === true, {
      message: 'Choose this option.',
    })
    .optional(),
  countryOfIssuance: z.string().min(1, { message: 'Select a choice.' }),
  coloredPassport: z
    .any()
    .refine((val) => val instanceof File && val.size > 0, {
      message: 'Upload a file here.',
    }),
  coloredPassport2: z
    .any()
    .refine((val) => val instanceof File && val.size > 0, {
      message: 'Upload a file here.',
    }),
  title2: z.string().min(1, { message: 'Select a choice' }),
  firstName2: z.string().min(2, { message: 'Enter a value for this field.' }),
  lastName2: z.string().min(2, { message: 'Enter a value for this field.' }),

  passportIssueDate: z
    .union([z.date(), z.string().transform((str) => new Date(str))])
    .refine(
      (date) => {
        const today = new Date()
        today.setHours(0, 0, 0, 0)
        return date < today
      },
      {
        message: 'Passport Issuing date must be before today.', // Custom error message
      }
    ),
  passportExpiryDate: z
    .union([z.date(), z.string().transform((str) => new Date(str))])
    .refine(
      (date) => {
        const today = new Date()
        const expiryDate = new Date(date)

        // Calculate the difference between the expiry date and today's date
        const diffInTime = expiryDate.getTime() - today.getTime()
        const diffInDays = diffInTime / (1000 * 3600 * 24) // Convert from milliseconds to days

        // Ensure the passport expiry date is at least 210 days in the future (7 months)
        return diffInDays >= 210
      },
      {
        message: 'Passport must be valid for at least another 7 months', // Custom error message
      }
    ),
  cityOfBirth: z.string().min(2, { message: 'Enter a value for this field.' }),
  cityOfBirthArabic: z.string().optional(),
  countryOfBirth: z.string().min(1, { message: 'Select a choice.' }),
  dateOfBirth: z
    .union([z.date(), z.string().transform((str) => new Date(str))])
    .refine(
      (date) => {
        const currentDate = new Date() // Get the current date
        const age = currentDate.getFullYear() - date.getFullYear() // Calculate the age based on the year
        const month = currentDate.getMonth() - date.getMonth() // Calculate the difference in months

        // Check if the age is 18 or older
        return age > 18 || (age === 18 && month >= 0)
      },
      {
        message: 'Person must be at least 18 years old', // Error message if the age is less than 18
      }
    ),
  gender: z.string().min(1, { message: 'Select a choice.' }),
  previousNationality: z.string().optional(),
  maritalStatus: z.string().min(1, { message: 'Select a choice.' }),
  religion: z.string().min(1, { message: 'Select a choice.' }),
  religionSubCategory: z
    .string()
    .min(1, { message: 'Select a choice.' })
    .optional(),
  fatherFullName: z
    .string()
    .min(2, { message: 'Enter a value for this field.' }),
  motherFullName: z
    .string()
    .min(2, { message: 'Enter a value for this field.' }),
  motherFullNameArabic: z.string().optional(),

  photoOfApplicant: z
    .any()
    .refine((val) => val instanceof File && val.size > 0, {
      message: 'Upload a file here.',
    }),

  visaApplicantFiles: z
    .array(z.instanceof(File))
    .min(1, { message: 'Upload at least one file.' }),

  typeOfEmployment: z.string().optional(),
  employmentDuration: z.string().min(1, { message: 'Select a choice.' }),
  doYouHaveADegree: z.string().min(1, { message: 'Select a choice.' }),
  attestedDegree: z.string().min(1, { message: 'Select a choice.' }).optional(),
  noAttestedDegree: z
    .string()
    .min(1, { message: 'Select a choice.' })
    .optional(),
  uploadAttestedDegree: z
    .any()
    .refine((val) => val instanceof File && val.size > 0, {
      message: 'Upload a file here.',
    })
    .optional(),
  jobTitleChange: z.string().optional(),
  jobTitle: z.string().min(1, { message: 'Select a choice.' }),
  educationQualification: z.string().min(1, { message: 'Select a choice.' }),
  employmentStartDate: z
    .union([z.date(), z.string().transform((str) => new Date(str))])
    .refine((date) => !isNaN(date.getTime()), {
      message: 'Invalid date format',
    }),
  probationPeriod: z.string().min(1, { message: 'Select a choice.' }),
  employmentTerminationNotice: z
    .string()
    .min(1, { message: 'Select a choice.' }),
  returnTicket: z.string().min(1, { message: 'Select a choice.' }),
  ticketEntitlementPeriod: z
    .string()
    .min(1, { message: 'Select a choice.' })
    .optional(),
  annualLeaveEntitlement: z.string().min(1, { message: 'Select a choice.' }),
  workingDays: z
    .number({
      required_error: 'This field is required',
      invalid_type_error: 'Please enter a valid number',
    })
    .min(22, { message: 'Enter a value greater than or equal to 22.' })
    .max(99, { message: 'Enter a value less than or equal to 99.' }),

  calendarDays: z
    .number({
      required_error: 'This field is required',
      invalid_type_error: 'Please enter a valid number',
    })
    .min(30, { message: 'Enter a value greater than or equal to 30.' })
    .max(99, { message: 'Enter a value less than or equal to 99.' }),

  salaryChange: z.string().min(1, { message: 'Select a choice.' }),
  basicSalary: z
    .number({
      required_error: 'This field is required',
      invalid_type_error: 'Please enter a valid number',
    })
    .min(10, { message: 'You must enter at least 2 digits.' })
    .max(9999999, { message: 'Maximum limit : 7 digits.' }),

  transportationAllowance: z.number().optional(),
  accommodationAllowance: z.number().optional(),
  otherAllowance: z.number().optional(),
  totalMonthlySalary: z.number().optional(),
  preferredPaymentMethod: z.string().min(1, { message: 'Select a choice.' }),
  salaryAmendmentBankStatement: z
    .any()
    .refine((val) => val instanceof File && val.size > 0, {
      message: 'Upload a file here.',
    })
    .optional(),
  visaFee: z.number().optional(),
  statusChange: z.number().optional(),

  vipStampingFee: z.number().optional(),
  amendmentFee: z.number().optional(),
  partnerInvestorVisa: z.number().optional(),
  totalAmountToBePaid: z.number().optional(),
  proofOfPayment: z.any().optional(),

  companyName: z.string().min(2, { message: 'Enter a value for this field.' }),
  tradeLicenseNumber: z
    .string()
    .min(1, { message: 'Enter a value for this field.' })
    .max(5, { message: 'You can only enter 5 digits.' })
    .regex(/^\d+$/, { message: 'Only digits are allowed.' }),

  establishmentCardNumber: z
    .string()
    .min(2, { message: 'Enter a value for this field.' }),
  authorizedSignatory: z
    .string()
    .min(2, { message: 'Enter a value for this field.' }),
  emailAddressOfGeneralManager: z
    .string()
    .email({ message: 'Invalid email address.' }),
  termsAndConditions: z.boolean().refine((val) => val === true, {
    message: 'You must agree to the Terms & Conditions Agreement.',
  }),

  affirmInformation: z.boolean().refine((val) => val === true, {
    message: 'You must accept the terms and conditions.',
  }),
})
