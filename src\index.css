@import url('https://fonts.cdnfonts.com/css/tt-firs-neue-trl');
@import url('https://fonts.cdnfonts.com/css/wotfard');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --bg-body: #F8FAFC;
    --text-color: #1A202C;
    --header-bg: #fff;
    --fz-primary: rgba(194, 160, 30, 1);
    --bg-primary-light: rgba(194, 160, 30, .5);
    --btn-border: rgba(15, 23, 42, 1);
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 48 73% 44%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    --alert-warning-bg: #fffdf8;
    --alert-warning-border: rgba(194, 160, 30, 1);
    --bg-fade-bottom: 210 40% 98%;
  }
  .dark {
    --bg-body: #020817;
    --text-color: #fff;
    --header-bg: #020817;
    --fz-primary: #c2a01e;
    --bg-primary-light: rgba(194, 160, 30, .5);
    --btn-border: rgba(255, 255, 255, 0.5);
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    --alert-warning-bg: #353944;
    --alert-warning-border: rgba(194, 160, 30, 1);
    --bg-fade-bottom: 222.2 84% 4.9%;
  }

  /* styles.css */
  html{
    font-family: "Inter", serif;
    font-optical-sizing: auto;
    font-weight: 400;
    font-style: normal;
    scroll-behavior: smooth;
  }

  .CollapsibleContent {
    overflow: hidden;
  }
  .CollapsibleContent[data-state='open'] {
    animation: slideDown 300ms ease-out;
  }
  .CollapsibleContent[data-state='closed'] {
    animation: slideUp 300ms ease-out;
  }

  @keyframes slideDown {
    from {
      height: 0;
    }
    to {
      height: var(--radix-collapsible-content-height);
    }
  }

  @keyframes slideUp {
    from {
      height: var(--radix-collapsible-content-height);
    }
    to {
      height: 0;
    }
  }

  /* Prevent focus zoom on mobile devices */
  @media screen and (max-width: 767px) {
    input,
    select,
    textarea {
      font-size: 16px !important;
    }
  }
}

@layer utilities {
  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  .faded-bottom {
    @apply after:pointer-events-none after:absolute after:bottom-0 after:left-0 after:hidden after:h-32 after:w-full after:bg-[linear-gradient(180deg,_transparent_10%,_hsl(var(--bg-fade-bottom))_70%)] after:md:block;
  }

  /* Slim scrollbar styles */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: theme('colors.gray.300') theme('colors.gray.100');
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 4px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: theme('colors.gray.200');
    border-radius: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: theme('colors.gray.200');
    border-radius: 6px;
    border: 0px solid theme('colors.gray.200');
  }
}

@layer base {
  * {
    @apply border-border;
  }
  html {
    @apply overflow-x-hidden;
  }
  body {
    @apply min-h-svh w-full bg-background text-foreground;
  }
}


.bg-background {
  background-color: var(--bg-body);
}
.bg-header{
  background-color: var(--header-bg);
}
.bg-primary {
  background-color: var(--fz-primary);;
}
.color-primary {
  color: var(--fz-primary);
}
.stroke-primary {
  stroke: var(--fz-primary);
}
.fill-primary {
  fill: var(--fz-primary);
}
.border-primary {
  border-color: var(--alert-warning-border);
}
.border-primary-light {
  border-color: rgb(255 251 238 / 50%);
}
.bg-primary-light {
  background-color: var(--bg-primary-light);
}
.btn-border {
  border-color: var(--btn-border);
}
.alert-bg-warning {
  background-color: var(--alert-warning-bg);
}

/* header custom styles */

[data-state="collapsed"] #logo-collapsed {
  display: block;
}

[data-state="collapsed"] #logo {
  display: none;
}

/* form custom style */
main:has(.fz-form) {
  padding-bottom: 70px!important;
}
.fz-form-btns {
  background: linear-gradient(360deg, var(--bg-body) 85%, transparent);
  right: calc(2rem + 1px);
}
[data-state="expanded"] ~ #content .fz-form-btns {
  width: calc(100% - var(--sidebar-width) - 4rem - 2px);
}
[data-state="collapsed"] ~ #content .fz-form-btns {
  width: calc(100% - var(--sidebar-width-icon) - 4rem - 2px);
}
/* react number input npm custom style  */
.fz-country-code.react-tel-input .selected-flag{
  border: 1px solid var(--border);
  border-radius: 4px!important;
  border-top-right-radius: 0!important;
  border-bottom-right-radius: 0!important;
}
.fz-country-code.react-tel-input .selected-flag.open {
  background: inherit!important;
}





/* custom component  */
@media (max-width: 600px) {
  .fz-wizardSteps .step {
    margin-left: 0 !important;
  }
  .fz-wizardSteps .bar {
    width: 50%;
  }
  .fz-form-btns {
    width: 100%;
    left: 0;
    right: 0;
  }
}
