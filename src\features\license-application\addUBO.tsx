import { Button } from '@/components/ui/button'
import { CountryDropdown } from '@/components/ui/country-dropdown'
import { DateTimePicker } from '@/components/ui/datetime-picker'
import { Pencil1Icon, TrashIcon } from '@radix-ui/react-icons'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogFooter,
  AlertDialogTitle,
  AlertDialogCancel,
  AlertDialogAction,
} from '@/components/ui/alert-dialog'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { useToast } from '@/hooks/use-toast'
import { zodResolver } from '@hookform/resolvers/zod'
import { PlusIcon } from '@radix-ui/react-icons'
import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import PhoneInput from 'react-phone-input-2'
import { z } from 'zod'

interface Ubo {
  id: number
  name?: string
  phone?: string
  email: string
  dateOfBir?: Date
  placeOfBirth?: string
  nationallity?: string
  passportNum?: string
  passportIssue?: Date
  passportExpiry?: Date
  residentialAddress?: string
  becameUBO?: string
  ceasedUBO?: string
}

const uboFormSchema = z.object({
  name: z.string().min(2, { message: 'Name must be at least 2 characters.' }),
  phone: z.string().min(5, { message: 'You must enter at least 5 digits.' }),
  email: z.string().email({ message: 'Invalid email address.' }),
  dateOfBir: z
    .union([z.date(), z.string().transform((str) => new Date(str))])
    .refine(
      (date) => {
        const currentDate = new Date() // Get the current date
        const age = currentDate.getFullYear() - date.getFullYear() // Calculate the age based on the year
        const month = currentDate.getMonth() - date.getMonth() // Calculate the difference in months

        // Check if the age is 18 or older
        return age > 18 || (age === 18 && month >= 0)
      },
      {
        message: 'Members must be at least 18 years old', // Error message if the age is less than 18
      }
    ),

  placeOfBirth: z.string().min(1, { message: 'Select a choice.' }),
  nationallity: z.string().min(1, { message: 'Select a choice.' }),

  passportNum: z.string().min(2, { message: 'Enter a value for this field.' }),

  passportIssue: z
    .union([z.date(), z.string().transform((str) => new Date(str))])
    .refine(
      (date) => {
        const today = new Date() // Get today's date
        return date <= today // Validate if the date is not in the future
      },
      {
        message: "Passport Issuing date can't be in the future", // Custom error message
      }
    ),

  passportExpiry: z
    .union([z.date(), z.string().transform((str) => new Date(str))])
    .refine(
      (date) => {
        const today = new Date()
        const expiryDate = new Date(date)

        // Calculate the difference between the expiry date and today's date
        const diffInTime = expiryDate.getTime() - today.getTime()
        const diffInDays = diffInTime / (1000 * 3600 * 24) // Convert from milliseconds to days

        // Ensure the passport expiry date is at least 120 days in the future
        return diffInDays >= 120
      },
      {
        message: "Passport Expiry Date can't be less than 4 months", // Custom error message
      }
    ),

  residentialAddress: z
    .string()
    .min(2, { message: 'Enter a value for this field.' }),
  becameUBO: z.string().min(2, { message: 'Enter a value for this field.' }),
  ceasedUBO: z.string().min(2, { message: 'Enter a value for this field.' }),
})

type UboFormData = z.infer<typeof uboFormSchema>

const AddUBO: React.FC = () => {
  const [ubo, setUbo] = useState<Ubo[]>([])
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingId, setEditingId] = useState<number | null>(null)
const [deleteConfirmId, setDeleteConfirmId] = useState<number | null>(null)
const uboToDelete = ubo.find((m) => m.id === deleteConfirmId);

  // const [date, setDate] = useState<Date | undefined>(undefined)
  const { toast } = useToast()

  const form = useForm<UboFormData>({
    resolver: zodResolver(uboFormSchema),
    defaultValues: {
      name: '',
      phone: '',
      email: '',
      dateOfBir: new Date(),
      placeOfBirth: '',
      nationallity: '',
      passportNum: '',
      passportIssue: new Date(),
      passportExpiry: new Date(),
      residentialAddress: '',
      becameUBO: '',
      ceasedUBO: '',
    },
  })

  const handleAddRow = () => {
    setDialogOpen(true)
  form.reset({
    name: '',
    phone: '',
    email: '',
    dateOfBir: new Date(),
    placeOfBirth: '',
    nationallity: '',
    passportNum: '',
    passportIssue: new Date(),
    passportExpiry: new Date(),
    residentialAddress: '',
    becameUBO: '',
    ceasedUBO: '',
  })
}

const handleCloseDialog = () => {
  setDialogOpen(false)
  setEditingId(null)
  form.reset()
}

  const handleEdit = (id: number) => {
  const uboToEdit = ubo.find((item) => item.id === id)
  if (uboToEdit) {
    form.setValue('name', uboToEdit.name || '')
    form.setValue('phone', uboToEdit.phone || '')
    form.setValue('email', uboToEdit.email || '')
    form.setValue('dateOfBir', uboToEdit.dateOfBir || new Date())
    form.setValue('placeOfBirth', uboToEdit.placeOfBirth || '')
    form.setValue('nationallity', uboToEdit.nationallity || '')
    form.setValue('passportNum', uboToEdit.passportNum || '')
    form.setValue('passportIssue', uboToEdit.passportIssue || new Date())
    form.setValue('passportExpiry', uboToEdit.passportExpiry || new Date())
    form.setValue('residentialAddress', uboToEdit.residentialAddress || '')
    form.setValue('becameUBO', uboToEdit.becameUBO || '')
    form.setValue('ceasedUBO', uboToEdit.ceasedUBO || '')
    setEditingId(id)
    setDialogOpen(true)
  }
}


const handleDelete = (id: number) => {
  setUbo((prevUbo) => prevUbo.filter((item) => item.id !== id))
  toast({ description: 'UBO deleted successfully', variant: 'destructive' })
}



 const handleUboData = (data: UboFormData) => {
  if (editingId !== null) {
    setUbo((prev) =>
      prev.map((item) =>
        item.id === editingId ? { ...item, ...data } : item
      )
    )
  toast({ description: 'UBO updated successfully.', variant: 'default' })
  } else {
    const newUbo: Ubo = {
      id: ubo.length > 0 ? Math.max(...ubo.map((m) => m.id)) + 1 : 1,
      ...data,
    }
    setUbo((prevUbo) => [...prevUbo, newUbo])
    toast({ description: 'UBO added successfully.', variant: 'default' })
  }

  handleCloseDialog()
}


  const handleFormSubmit = (data: UboFormData) => {
    form.reset()
    handleUboData(data)
  }

  return (
    <>
      <Table className='w-full table-auto scrollbar-thin'>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Phone</TableHead>
            <TableHead>Email</TableHead>
            <TableHead>Date of Birth</TableHead>
            <TableHead>Place of Birth</TableHead>
            <TableHead>Nationality</TableHead>
            <TableHead>Passport Number</TableHead>
            <TableHead>Passport Issue Date</TableHead>
            <TableHead>Passport Expiry Date</TableHead>

            <TableHead>Residential Address</TableHead>
            <TableHead>
              Basis and date (DD/MM/YYYY) on which the individual became an UBO
            </TableHead>
            <TableHead>
              Basis and date (DD/MM/YYYY) on which the individual ceased to be
              an UBO
            </TableHead>
          {/* Show Actions column only if there's at least one UBO */}
    {ubo.length > 0 && <TableHead>Actions</TableHead>}

          </TableRow>
        </TableHeader>
        <TableBody>
          {ubo.map((ubo) => (
            <TableRow key={ubo.id}>
              <TableCell>{ubo.name}</TableCell>
              <TableCell>{ubo.phone}</TableCell>
              <TableCell>{ubo.email}</TableCell>
              <TableCell>
                {ubo.dateOfBir?.toLocaleDateString() || 'N/A'}
              </TableCell>
              <TableCell>{ubo.placeOfBirth}</TableCell>
              <TableCell>{ubo.nationallity}</TableCell>
              <TableCell>{ubo.passportNum}</TableCell>
              <TableCell>
                {ubo.passportIssue?.toLocaleDateString() || 'N/A'}
              </TableCell>{' '}
              <TableCell>
                {ubo.passportExpiry?.toLocaleDateString() || 'N/A'}
              </TableCell>
              <TableCell>{ubo.residentialAddress}</TableCell>
              <TableCell>{ubo.becameUBO}</TableCell>
              <TableCell>{ubo.ceasedUBO}</TableCell>
             <TableCell className='flex flex-col space-y-1'>
  <Button
    type='button'
    size='sm'
    variant='link'
    onClick={() => handleEdit(ubo.id)}
    className='flex items-center space-x-1 px-0'
  >
    <Pencil1Icon className='w-4 h-4' />
    <span>Edit</span>
  </Button>
  <Button
  type='button'
  size='sm'
  variant='link'
  className='text-red-600 hover:text-red-800 flex items-center space-x-1 px-0'
  onClick={() => setDeleteConfirmId(ubo.id)}
>
  <TrashIcon className='w-4 h-4' />
  <span>Delete</span>
</Button>
</TableCell>


            </TableRow>
          ))}
        </TableBody>
      </Table>
      <Button
        type='button'
        variant='link'
        className='mt-4'
        onClick={handleAddRow}
      >
        <PlusIcon className='mr-2' /> Add UBO
      </Button>
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className='h-[90vh] pb-14 pr-2' variant='default'>
          <DialogHeader className='static'>
          <DialogTitle>{editingId !== null ? 'Edit UBO' : 'Add UBO'}</DialogTitle>

            <DialogDescription>
              Please fill out the details of the UBO :
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(handleFormSubmit)}
              className='scrollbar-thin overflow-y-auto pr-1 pl-1 space-y-4 fz-form'
            >
              <div className='flex flex-wrap space-between gap-y-4'>
                {/* Name Field */}
                <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                  <FormField
                    control={form.control}
                    name='name'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Name</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder='Please Enter Full Name First Name and Last Name'
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Phone Field */}
                <div className='flex-1'>
                  <FormField
                    control={form.control}
                    name='phone'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Phone</FormLabel>
                        <PhoneInput
                          country={'ae'}
                          // value={field.value.toString()}
                          onChange={(phone) => field.onChange(phone)}
                          containerClass='fz-country-code flex h-9 w-full rounded-tl-[6px] rounded-bl-[6px] border border-input bg-transparent text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm'
                          inputClass=' flex  !h-9 !w-full rounded-tl-[6px] rounded-bl-[6px] border !border-transparent !bg-transparent px-3 text-base !shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm transform translate-y-[-2px]'
                          buttonClass='!h-100 !border-l-transparent !border-t-transparent !bg-slate-200 dark:!bg-slate-800 dark:!border-r-transparent !border-b-transparent !border-gray-200 !bg-transparent !rounded-tl-[8px] !rounded-bl-[6px]'
                          enableSearch={true}
                          searchPlaceholder='Search country...'
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
              <div className='flex flex-wrap space-between gap-y-4'>
                {/* Email Field */}
                <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                  <FormField
                    control={form.control}
                    name='email'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input
                            type='email'
                            {...field}
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Date of Birth Field */}
                <div className='flex-1'>
                  <FormField
                    control={form.control}
                    name='dateOfBir'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Date of Birth</FormLabel>
                        <DateTimePicker
                          granularity='day'
                          value={field.value}
                          onChange={field.onChange}
                          displayFormat={{
                            hour24: 'dd MMMM yyyy', // 31 January 2024
                          }}
                        />
                        <FormDescription>dd-MMM-yyyy</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className='flex flex-wrap space-between gap-y-4'>
                {/* Place of Birth Field */}
                <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                  <FormField
                    control={form.control}
                    name='placeOfBirth'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Place of Birth</FormLabel>
                        <FormControl>
                          <CountryDropdown
                            placeholder='Country'
                            defaultValue={field.value as string}
                            onChange={(country) => {
                              console.log("Country name:", country.name);
                              field.onChange(country.name)
}}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Nationality Field */}
                <div className='flex-1'>
                  <FormField
                    control={form.control}
                    name='nationallity'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nationality</FormLabel>
                        <FormControl>
                          <CountryDropdown
                            placeholder='Country'
                            defaultValue={field.value as string}
                            onChange={(country) => {
                              console.log("Country name:", country.name);
                              field.onChange(country.name)
}}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className='flex flex-wrap space-between gap-y-4'>
                {/* Passport Number Field */}
                <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                  <FormField
                    control={form.control}
                    name='passportNum'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Passport Number</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder='Enter Passport Number'
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Passport Issue Date Field */}
                <div className='flex-1'>
                  <FormField
                    control={form.control}
                    name='passportIssue'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Passport Issue Date</FormLabel>
                        <DateTimePicker
                          granularity='day'
                          value={field.value}
                          onChange={field.onChange}
                          displayFormat={{
                            hour24: 'dd MMMM yyyy', // 31 January 2024
                          }}
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className='flex flex-wrap space-between gap-y-4'>
                {/* Passport Expiry Date Field */}
                <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                  <FormField
                    control={form.control}
                    name='passportExpiry'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Passport Expiry Date</FormLabel>
                        <DateTimePicker
                          granularity='day'
                          value={field.value}
                          onChange={field.onChange}
                          displayFormat={{
                            hour24: 'dd MMMM yyyy', // 31 January 2024
                          }}
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Residential Address Field */}
                <div className='flex-1'>
                  <FormField
                    control={form.control}
                    name='residentialAddress'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Residential Address</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder='Enter Residential Address'
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className='flex flex-wrap space-between gap-y-4'>
                {/* Basis and Date (DD/MM/YYYY) on which the individual became an UBO Field */}

                <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                  <FormField
                    control={form.control}
                    name='becameUBO'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Basis and Date (DD/MM/YYYY) on which the individual
                          became an UBO :
                        </FormLabel>
                        <FormControl>
                          <Input {...field} placeholder='Enter' />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Basis and Date (DD/MM/YYYY) on which the individual ceased to be an UBO Field */}

                <div className='flex-1'>
                  <FormField
                    control={form.control}
                    name='ceasedUBO'
                    render={({ field }) => (
                      <FormItem className='mb-4'>
                        <FormLabel>
                          Basis and Date (DD/MM/YYYY) on which the individual
                          ceased to be an UBO (if applicable) :
                        </FormLabel>
                        <FormControl>
                          <Input {...field} placeholder='Enter ' />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Add UBO Button */}
              <div className='absolute bottom-0 left-0 w-full border-t border-gray-100 p-3 pr-10 bg-background flex justify-end space-x-2 z-10 '>
             <DialogFooter>
    <Button
      type='button'
      variant='outline'
      onClick={handleCloseDialog}
    >
      Cancel
    </Button>
    <Button type='submit'>
      {editingId !== null ? 'Update UBO' : 'Add UBO'}
    </Button>
  </DialogFooter>
</div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      <AlertDialog open={deleteConfirmId !== null} onOpenChange={(open) => !open && setDeleteConfirmId(null)}>
  <AlertDialogContent>
    <AlertDialogHeader>
     <AlertDialogTitle className='text-center'>
        Are you sure you want to delete the UBO
        <p className="font-semibold text-red-600 dark:text-red-400">
          {" "}“{uboToDelete?.name}” <span className='text-gray-800'>?</span>
        </p>
      </AlertDialogTitle>
    </AlertDialogHeader>
    <AlertDialogFooter className='flex sm:justify-center'>
      <AlertDialogCancel onClick={() => setDeleteConfirmId(null)}>
        Cancel
      </AlertDialogCancel>
      <AlertDialogAction
        onClick={() => {
          if (deleteConfirmId !== null) {
            handleDelete(deleteConfirmId)
            setDeleteConfirmId(null)
          }
        }}
      >
        Delete
      </AlertDialogAction>
    </AlertDialogFooter>
  </AlertDialogContent>
</AlertDialog>
    </>
  )
}

export default AddUBO
