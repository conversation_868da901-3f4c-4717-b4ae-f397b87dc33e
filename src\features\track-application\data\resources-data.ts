import { FC } from "react";

import {
  VisaRelatedInquiries,
  IfzaLife,
  RequestingDocuments,
  BankingSupport,
  OfficeSpace,
  CorporateConsultingServices,
  AttestationServices,
  AttestationAmendment,
  LicenseRenewal,
  ClientSupportLine,
} from "@/features/track-application/components/public-tracker/sections/resources-section-content";


interface AccordionItemProps {
  title: string;
  content: FC; // render as Functional Component
}

export const accordionData: AccordionItemProps[] = [
  { title: "Visa Related Inquiries", content: VisaRelatedInquiries },
  { title: "IFZA Life", content: IfzaLife },
  { title: "Requesting Documents", content: RequestingDocuments },
  { title: "Banking Support", content: BankingSupport },
  { title: "Office Space / Flexi Desk", content: OfficeSpace },
  { title: "Corporate Consulting Services", content: CorporateConsultingServices},
  { title: "Attestation Services", content: AttestationServices },
  { title: "Attestation Amendment", content: AttestationAmendment },
  { title: "License Renewal", content: LicenseRenewal },
  { title: "Client Support Line", content: ClientSupportLine },
];