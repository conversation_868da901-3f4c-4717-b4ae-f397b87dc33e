import { Card } from '@/components/ui/card'
import AuthLayout from '../auth-layout'
import { UserAuthForm } from './components/user-auth-form'
import { useState, useEffect } from 'react'
import sliderOne from '@/assets/images/slider_one.png'
import sliderTwo from '@/assets/images/slider_two.png'
import sliderThree from '@/assets/images/slider_three.png'

export default function SignIn() {
  const slides = [sliderOne, sliderTwo, sliderThree]
  const [currentSlide, setCurrentSlide] = useState(0)

  useEffect(() => {
    const timer = setInterval(() => setCurrentSlide(prev => (prev + 1) % slides.length), 3000)
    return () => clearInterval(timer)
  }, [])

  return (
    <div className='flex h-screen'>
      <div className='hidden md:block w-[40%] h-full relative overflow-hidden'>
        {slides.map((slide, index) => (
          <img
        key={index}
        src={slide}
        alt={`slide ${index + 1}`}
        className={`absolute top-0 left-0 w-full h-full object-cover transition-opacity duration-1000 ease-in-out ${currentSlide === index ? 'opacity-100' : 'opacity-0'}`}
          />
        ))}
      </div>
      <div className='w-full md:w-[60%] flex items-center justify-center'>
        {/* Left column: existing AuthLayout and form */}
        <AuthLayout>
          <Card className='p-6'>
        <div className='flex flex-col space-y-2 text-left'>
          <h1 className='text-2xl font-semibold tracking-tight'>Login</h1>
          <p className='text-sm text-muted-foreground'>
            Enter your email and password below <br />
            to log into your account
          </p>
        </div>
        <UserAuthForm />
        <p className='mt-4 px-8 text-center text-sm text-muted-foreground'>
          By clicking login, you agree to our{' '}
          <a
            href='/terms'
            className='underline underline-offset-4 hover:text-primary'
          >
            Terms of Service
          </a>{' '}
          and{' '}
          <a
            href='/privacy'
            className='underline underline-offset-4 hover:text-primary'
          >
            Privacy Policy
          </a>
          .
        </p>
          </Card>
        </AuthLayout>
      </div>
      
    </div>
  )
}
