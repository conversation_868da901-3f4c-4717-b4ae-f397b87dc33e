import React from 'react'
import { useFormContext } from 'react-hook-form'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Checkbox } from '@/components/ui/checkbox'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { ApplicationFormValues } from '../../types/application-form-types'

const Step6VisaApplication: React.FC = () => {
  const form = useFormContext<ApplicationFormValues>()

  // Log all form values for debugging
  console.log('Step 6 Form Values:', form.watch())

  return (
    <>
      <form className='space-y-4 fz-form'>
        <FormField
          control={form.control}
          name='companyName'
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Company Name <span className='text-red-500'>*</span>
              </FormLabel>
              <FormControl>
                <Input placeholder='Enter Company Name' {...field} />
              </FormControl>
              <FormDescription>
                Please type the company exactly as per your Trade License.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name='tradeLicenseNumber'
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Trade License Number <span className='text-red-500'>*</span>
              </FormLabel>
              <FormControl>
                <Input placeholder='Enter Trade License Number' {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name='establishmentCardNumber'
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Establishment Card Number{' '}
                <span className='text-red-500'>*</span>
              </FormLabel>
              <FormControl>
                <Input
                  placeholder='Enter Establishment Card Number'
                  {...field}
                />
              </FormControl>
              <FormDescription>
                Please enter the number mentioned above the Bar Code, e.g.
                1/1/1234567
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name='authorizedSignatory'
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Authorized Signatory/General Manager{' '}
                <span className='text-red-500'>*</span>
              </FormLabel>
              <FormControl>
                <Input {...field} value={field.value || ''} />
              </FormControl>
              <FormDescription>
                Please mention the complete name of the Authorized signatory or
                General Manager on the License.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name='emailAddressOfGeneralManager'
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Email address of General Manager/Authorized Signatory{' '}
                <span className='text-red-500'>*</span>
              </FormLabel>
              <FormControl>
                <Input type='email' {...field} value={field.value || ''} />
              </FormControl>
              <FormDescription>
                Please mention the complete/correct email address of the General
                Manager/Authorized signatory.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name='termsAndConditions'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-start space-x-2 border border-dashed border-yellow-400 rounded-md p-4 bg-yellow-50 dark:bg-primary/20'>
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={(checked: boolean) =>
                      field.onChange(checked)
                    }
                    className='mt-1'
                    data-field='termsAndConditions'
                  />
                </FormControl>
                <FormLabel className='text-base text-primary dark:text-primary-light'>
                  I accept the specific conditions for the issue of Visas in
                  clauses 15-18 of IFZA's Terms and Conditions.
                </FormLabel>
              </div>

              <FormDescription>
                By clicking the agreement box, you agree to IFZA Terms &
                Conditions.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <Alert className='border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
          <AlertDescription>
            ​Please click here for{' '}
            <a
              href='https://www2.ifza.com/wp-content/uploads/2021/02/IFZA_TNCs-v4.pdf.pdf'
              target='_blank'
              rel='noopener noreferrer'
              className='text-blue-500 underline'
            >
              Terms and Conditions.
            </a>
          </AlertDescription>
        </Alert>
      </form>
    </>
  )
}

export default Step6VisaApplication
