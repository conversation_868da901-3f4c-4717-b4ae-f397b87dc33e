import { Link } from '@tanstack/react-router'
import { Button } from '@/components/ui/button'
import { Main } from '@/components/layout/main'
import ApplicationDetails from '@/features/track-application/components/application-details'
import ApplicationStatus from '@/features/track-application/components/application-status-flowchart'
import { ApplicationStep } from '../enums/application-step-enum'

interface TrackerBodyProps {
  steps: ApplicationStep[]
  currentStep: number
  applicationType: string
  status: string
  applicationNumber: string
  applicationDate: Date
  trackNewApplicationUrl?: string
  stepTimestamps?: Partial<Record<ApplicationStep, string>>
}

export default function TrackerBody({
  steps,
  currentStep,
  applicationType,
  status,
  applicationNumber,
  applicationDate,
  trackNewApplicationUrl,
  stepTimestamps,
}: TrackerBodyProps) {
  return (
    <Main className='p-6'>
      <div className='space-y-8'>
        <div className='ml-1 mb-2 flex items-center justify-between flex-wrap'>
          <h1 className='text-2xl font-bold tracking-tight lg:text-[28px] text-[18px] mb-2 md:mb-0'>
            Track Your Application
          </h1>
          <Link to={trackNewApplicationUrl || '/track-application'}>
            <Button
              variant='default'
              className='bg-[#C2A01E] text-white hover:bg-[#b08f1a]'
            >
              Track Another Application
            </Button>
          </Link>
        </div>

        {/* application-details */}
        <ApplicationDetails
          applicationType={applicationType}
          status={status}
          applicationNumber={applicationNumber}
          applicationDate={applicationDate}
        />

        {/* application-status-flowchart */}
        <ApplicationStatus
          steps={steps}
          currentStep={currentStep}
          stepTimestamps={stepTimestamps || {}}
        />
      </div>
    </Main>
  )
}
