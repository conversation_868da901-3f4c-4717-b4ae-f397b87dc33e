import ApplicationProgress from '@/components/application-progress'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'

import { Button } from '@/components/ui/button'
import { toast } from '@/hooks/use-toast'
import {
  createLicenseApplication,
  createLicenseApplicationMembers,
  mainpulateLicenseApplicationData,
  mainpulateLicenseMembersData,
  uploadMemberFiles,
} from '@/services/licenseapplication'
import { zodResolver } from '@hookform/resolvers/zod'
import { Loader2,} from 'lucide-react'
import React, { useEffect, useState } from 'react'
import { FieldErrors, useForm, UseFormReturn } from 'react-hook-form'
import { z } from 'zod'
// import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { useAuthStore } from '@/stores/authStore'
import { activitiesData } from './Business_Acttivites'
import type { Member } from './company-members'
import  Axios  from 'axios'
import { BASE_URL } from '@/utils/network'
// import stringSimilarity from 'string-similarity';
import { checkCompany } from './comapny-check'
import Step1LicenseApplication from './components/steps/Step1LicenseApplication'
import Step2LicenseApplication from './components/steps/Step2LicenseApplication'
import Step3LicenseApplication from './components/steps/Step3LicenseApplication'
import Step4LicenseApplication from './components/steps/Step4LicenseApplication'
import Step5LicenseApplication from './components/steps/Step5LicenseApplication'
import Step6LicenseApplication from './components/steps/Step6LicenseApplication'

const applicationFormSchema = z.object({
  applicationDate: z
    .union([z.date(), z.string().transform((str) => new Date(str))])
    .refine((date) => !isNaN(date.getTime()), {
      message: 'Invalid date format',
    }),

  cem: z.string(),
  paymentType: z.string().min(1, { message: 'Select a choice.' }),

  emailAddress: z.string().email({ message: 'Invalid email address.' }),
  phone: z.string().optional(),
  licenseApplicationType: z.string().min(1, { message: 'Select this choice.' }),
  // companyName: z
  //   .string()
  //   .min(2, { message: 'Enter a value for this field.' })
  //   .optional(),
  countryOfRegistration: z.string().optional(),
  operateAsFranchise: z.string().optional(),
  addressOfParentCompany: z.string().optional(),
  tradeNameOfFranchise: z.string().optional(),
  countryOfOperation: z

    .array(z.string())

    .min(1, { message: 'Select your choice(s).' }),

  option1English: z
    .string()
    .min(2, { message: 'Enter a value for this field.' }),
  option1Arabic: z.string().optional(),

  option2English: z
    .string()
    .min(2, { message: 'Enter a value for this field.' }),
  option2Arabic: z.string().optional(),

  option3English: z
    .string()
    .min(2, { message: 'Enter a value for this field.' }),
  option3Arabic: z.string().optional(),

  agreementTerms: z.string().min(1, { message: 'Select a choice.' }),

  licenseType: z.string().optional(),

  tradeLicenseValidity: z.string().min(1, { message: 'Select a choice.' }),

  visaPackage: z.string().min(1, { message: 'Select a choice.' }),

  includeVisaCostInQuote: z.string().min(1, { message: 'Select a choice.' }),
  establishmentCard: z.string().min(1, { message: 'Select a choice.' }),
  financialYearStartDate: z
    .union([z.date(), z.string().transform((str) => new Date(str))])
    .refine((date) => !isNaN(date.getTime()), {
      message: 'Invalid date format',
    }),
  financialYearEndDate: z
    .union([z.date(), z.string().transform((str) => new Date(str))])
    .refine((date) => !isNaN(date.getTime()), {
      message: 'Invalid date format',
    }),

  numberOfInsideCountryVisas: z
    .string()
    .min(1, { message: 'Select a choice.' })
    .optional(),

  shareholdingType: z.string().min(1, { message: 'Select a choice.' }),

  numberOfShareholders: z
    .number()
    .min(1, { message: 'Enter a value greater than or equal to 1.' }) // Validate min value

    .max(10, { message: 'Enter a value less than or equal to 10.' }), // Validate max value

  proposedShareCapital: z.number().min(10000, {
    message: 'Enter a value greater than or equal to 10000.',
  }),

  shareValue: z
    .number()
    .min(10, { message: 'Enter a value greater than or equal to 10.' }),

  totalNumberOfShares: z.string().optional(),
  shareCapitalPerShareholder: z.string().optional(),

  activityRules: z.string().min(1, { message: 'Select a choice.' }),

  companyStatementValue: z.string().min(1, { message: 'Select a choice.' }),
  agreement: z.string().optional(),
  declaration: z.string().min(1, { message: 'Select a choice.' }),
  confirmation: z.string().min(1, { message: 'Select a choice.' }),

  authorizedSignatoryEmail: z
    .string()
    .email({ message: 'Invalid email address.' }),
  authorizedSignatoryName: z
    .string()
    .min(2, { message: 'Enter a value for this field.' }),

  preparation: z.string().optional(),
  registration: z.string().optional(),
  // members: z.array(
  //   z.object({
  //   })
  // ),
})

type ApplicationFormValues = z.infer<typeof applicationFormSchema>
export default function LicenseApplication() {
  const user = useAuthStore((state) => state.auth.user)
  // const [_, setIsFormSubmitted] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [isVaildId, setIsvaildId] = useState(true)
  // Initialize members state for CompanyMembers
  const [members, setMembers] = useState<Member[]>([])
  // const [date, setDate] = useState<Date | undefined>(undefined)

  const form = useForm<ApplicationFormValues>({
    resolver: zodResolver(applicationFormSchema),
    defaultValues: {
      applicationDate: new Date(),
      cem: user?.name || '',
      paymentType: '',
      emailAddress: '',
      phone: '',
      licenseApplicationType: 'New',
      // companyName: '',
      countryOfRegistration: '',
      operateAsFranchise: 'No',
      addressOfParentCompany: '',
      tradeNameOfFranchise: '',
      countryOfOperation: [],
      option1English: '',
      option1Arabic: '',
      option2English: '',
      option2Arabic: '',
      option3English: '',
      option3Arabic: '',
      agreementTerms: '',
      licenseType: '',
      tradeLicenseValidity: '',
      visaPackage: '',
      includeVisaCostInQuote: '',
      financialYearStartDate: new Date(),
      financialYearEndDate: new Date(new Date().getFullYear(), 11, 31),
      establishmentCard: '',
      numberOfInsideCountryVisas: '',
      shareholdingType: 'Individual',
      numberOfShareholders: 1,
      proposedShareCapital: 1,
      shareValue: 1,
      totalNumberOfShares: '',
      shareCapitalPerShareholder: '',
      activityRules: '',
      companyStatementValue: '',
      agreement: '',
      declaration: '',
      confirmation: '',
      authorizedSignatoryEmail: '',
      authorizedSignatoryName: '',
      preparation: '',
      registration: '',

      // members: [], // Empty members array for step 5
    },
    // shouldUnregister: false,
    mode: 'onChange',
    criteriaMode: 'all',
  })

  const [localNumberOfShareholders, setLocalNumberOfShareholders] = useState(
    form.watch('numberOfShareholders')?.toString() || ''
  )

  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === 'numberOfShareholders') {
        setLocalNumberOfShareholders(
          value?.numberOfShareholders?.toString() || ''
        )
      }
    })

    return () => subscription.unsubscribe()
  }, [form])

  const [localProposedCapital, setLocalProposedCapital] = useState(
    form.watch('proposedShareCapital')?.toString() || ''
  )
  const [localShareValue, setLocalShareValue] = useState(
    form.watch('shareValue')?.toString() || ''
  )

  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === 'proposedShareCapital') {
        setLocalProposedCapital(value?.proposedShareCapital?.toString() || '')
      }
      if (name === 'shareValue') {
        setLocalShareValue(value?.shareValue?.toString() || '')
      }
    })

    return () => subscription.unsubscribe()
  }, [form])

  const [currentStep, setCurrentStep] = React.useState(1) // Track current step

  useEffect(() => {
    const currentValue = form.getValues('financialYearEndDate')

    if (!currentValue) {
      const now = new Date()
      const endOfYear = new Date(now.getFullYear(), 11, 31)

      form.setValue('financialYearEndDate', endOfYear)
    }
  }, [form])

  // Calculate the Total Number of Shares when Proposed Share Capital or Share Value changes
  const calculateTotalNumberOfShares = (form: UseFormReturn<any>) => {
    const proposedShareCapital = form.watch('proposedShareCapital')
    const shareValue = form.watch('shareValue')

    if (proposedShareCapital && shareValue) {
      // Calculate the Total Number of Shares
      const totalNumberOfShares = (proposedShareCapital / shareValue).toFixed(0) // Convert to string

      // Set the calculated value of Total Number of Shares
      form.setValue('totalNumberOfShares', totalNumberOfShares) // Set the value as a string
    }
  }

  // Calculate the Share Capital per Shareholder when Proposed Share Capital or Number of Shareholders changes
  const calculateShareCapitalperShareholder = (form: UseFormReturn<any>) => {
    const proposedShareCapital = form.watch('proposedShareCapital')
    const numberOfShareholders = form.watch('numberOfShareholders')

    if (proposedShareCapital && numberOfShareholders) {
      // Calculate the Share Capital per Shareholder with decimal values
      let shareCapitalPerShareholder = (
        proposedShareCapital / numberOfShareholders
      ).toFixed(2) // Keep 2 decimal places

      // Remove trailing zeros if present
      shareCapitalPerShareholder = parseFloat(
        shareCapitalPerShareholder
      ).toString()

      // Set the calculated value of Share Capital per Shareholder
      form.setValue('shareCapitalPerShareholder', shareCapitalPerShareholder) // Set the value as a string
    }
  }
  // Detect sales=dach from URL
  const location = typeof window !== 'undefined' ? window.location : undefined
  const searchParams = location
    ? new URLSearchParams(location.search)
    : undefined
  const isDachSales = searchParams?.get('sales') === 'dach'

  const fieldsPerStep: Record<number, (keyof ApplicationFormValues)[]> = {
    1: [
      'applicationDate',
      'cem',
      'paymentType',
      'emailAddress',
      'phone',
      'licenseApplicationType',
      // 'companyName',
      'countryOfRegistration',
      'operateAsFranchise',
      'tradeNameOfFranchise',
      'addressOfParentCompany',
      'countryOfOperation',
      'option1English',
      'option1Arabic',
      'option2English',
      'option2Arabic',
      'option3English',
      'option3Arabic',
      'agreementTerms',
    ], // Fields for step 1

    2: [
      'tradeLicenseValidity',
      'visaPackage',
      'includeVisaCostInQuote',
      'numberOfInsideCountryVisas',
      'establishmentCard',
      'financialYearStartDate',
      'financialYearEndDate',
    ], // Fields for step 2

    3: [
      'shareholdingType',
      'numberOfShareholders',
      'proposedShareCapital',
      'shareValue',
      'totalNumberOfShares',
      'shareCapitalPerShareholder',
    ], // Fields for step 3

    4: ['activityRules'], // Fields for step 4

    5: [
      // 'members'
    ], // Fields for step 5

    6: [
      'companyStatementValue',
      'agreement',
      'declaration',
      'confirmation',
      'authorizedSignatoryName',
      'authorizedSignatoryEmail',
      'preparation',
      'registration',
    ], // Fields for step 6
  }

  const [stepValues, setStepValues] = useState<
    Record<number, ApplicationFormValues>
  >({})
  void stepValues

  const getStepSchema = (step: number, values: ApplicationFormValues) => {
    switch (step) {
      case 1:
        return z.object({
          paymentType: applicationFormSchema.shape.paymentType,
          emailAddress: applicationFormSchema.shape.emailAddress,
          licenseApplicationType:
            applicationFormSchema.shape.licenseApplicationType,
          countryOfOperation: applicationFormSchema.shape.countryOfOperation,
          option1English: applicationFormSchema.shape.option1English,
          option2English: applicationFormSchema.shape.option2English,
          option3English: applicationFormSchema.shape.option3English,
          agreementTerms: applicationFormSchema.shape.agreementTerms,
          // ...(values.licenseApplicationType === 'Branch' && {
          //   companyName: applicationFormSchema.shape.companyName,
          // }),
        })

      case 2:
        return z.object({
          tradeLicenseValidity:
            applicationFormSchema.shape.tradeLicenseValidity,
          visaPackage: applicationFormSchema.shape.visaPackage,
          includeVisaCostInQuote:
            applicationFormSchema.shape.includeVisaCostInQuote,
          establishmentCard: applicationFormSchema.shape.establishmentCard,
          ...(values.includeVisaCostInQuote === 'Yes' && {
            numberOfInsideCountryVisas:
              applicationFormSchema.shape.numberOfInsideCountryVisas,
          }),
        })

      case 3:
        return z.object({
          shareholdingType: applicationFormSchema.shape.shareholdingType,
          numberOfShareholders:
            applicationFormSchema.shape.numberOfShareholders,
          proposedShareCapital:
            applicationFormSchema.shape.proposedShareCapital,
          shareValue: applicationFormSchema.shape.shareValue,
        })

      case 4:
        return z.object({
          activityRules: applicationFormSchema.shape.activityRules,
        })
      case 5:
        return z.object({})

      case 6:
        return z.object({
          companyStatementValue:
            applicationFormSchema.shape.companyStatementValue,
          declaration: applicationFormSchema.shape.declaration,
          confirmation: applicationFormSchema.shape.confirmation,
          authorizedSignatoryName:
            applicationFormSchema.shape.authorizedSignatoryName,
          authorizedSignatoryEmail:
            applicationFormSchema.shape.authorizedSignatoryEmail,
        })

      default:
        return z.object({})
    }
  }

  interface RowData {
    id: number
    option: string
    activityCode: string
    ded: string
    tpa: string
    field4: string
    checked: boolean
  }

  const [rows, setRows] = useState<RowData[]>([
    {
      id: 1,
      option: '',
      activityCode: '',
      ded: '',
      tpa: '',
      field4: '',
      checked: false,
    },
    // ...additional rows
  ])

  function handleDelete(id: string) {
    setRows(rows.filter((row) => row.id !== Number(id)))
  }

  function handleOptionChange(id: string, value: string) {
    // Check if the value already exists in another row
    const isDuplicate = rows.some(
      (row) => row.option === value && String(row.id) !== id
    )

    if (isDuplicate) {
      toast({
        title: 'Duplicate Activity',
        description: 'You have already added this activity before.',
        variant: 'destructive',
      })
      return
    }

    const activity = activitiesData.find((a: any) => a.ID === value)

    const updatedRows = rows.map((row) =>
      String(row.id) === id
        ? {
            ...row,
            option: value,
            activityCode: activity?.Activity_Code || '',
            ded: activity?.DED_License_Type || '',
            tpa: activity?.TPA || '',
          }
        : row
    )

    setRows(updatedRows)
  }

  useEffect(() => {
    const selectedTypes = rows.map((r) => r.ded).filter(Boolean)
    const uniqueTypes = new Set(selectedTypes)

    let detectedType = ''
    if (uniqueTypes.size === 1) {
      const type = [...uniqueTypes][0]
      if (type === 'Professional') {
        detectedType = 'Professional (Includes Service & Consultancy)'
      } else if (type === 'Commercial') {
        detectedType = 'Commercial (Includes Trade)'
      } else {
        detectedType = 'Combination'
      }
    } else if (uniqueTypes.size > 1) {
      detectedType = 'Combination'
    }

    // Update licenseType in form
    form.setValue('licenseType', detectedType, { shouldDirty: true })

    // Sync step values after licenseType update
    const updatedValues = form.getValues()
    setStepValues((prev) => ({
      ...prev,
      [currentStep]: updatedValues,
    }))
  }, [rows])

  function handleFieldChange(id: string, field: string, value: string) {
    setRows(
      rows.map((row) =>
        String(row.id) === id ? { ...row, [field]: value } : row
      )
    )
  }

  function handleCheckedChange(id: string, checked: boolean) {
    const selectedRow = rows.find((row) => String(row.id) === id)

    // Prevent checking Main Activity if Activity Name is not selected
    if (checked && (!selectedRow || !selectedRow.option)) {
      toast({
        title: 'Select Activity First',
        description:
          'Please select an Activity Name before marking it as Main.',
        variant: 'destructive',
      })
      return
    }

    if (checked) {
      // Allow only one main activity (uncheck others)
      setRows(
        rows.map((row) =>
          String(row.id) === id
            ? { ...row, checked: true }
            : { ...row, checked: false }
        )
      )
    } else {
      // Prevent unchecking the only selected main activity
      toast({
        title: 'Warning',
        description: 'At least one Main Activity must be selected.',
        variant: 'destructive',
      })
    }
  }

  function handleAddRow() {
    // Check if there are any existing rows without an Activity Name selected
    const hasEmptyRows = rows.some((row) => !row.option)

    if (hasEmptyRows) {
      toast({
        title: 'Incomplete Row',
        description:
          'Please select an Activity Name in existing rows before adding a new one.',
        variant: 'destructive',
      })
      return
    }

    // Prevent adding more than 7 activity rows
    if (rows.length >= 7) {
      toast({
        title: 'Limit Reached',
        description: 'You can only add up to 7 activities.',
        variant: 'destructive',
      })
      return
    }
    const newRow: RowData = {
      id: rows.length ? Math.max(...rows.map((r) => r.id)) + 1 : 1,
      option: '',
      activityCode: '',
      ded: '',
      tpa: '',
      field4: '',
      checked: false,
    }
    setRows([...rows, newRow])
  }

  // Define transliteration map
  function transliterate(input: string): string {
    let base = input.toUpperCase()

    // Helper function to replace all occurrences
    const replaceAll = (
      str: string,
      search: string,
      replacement: string
    ): string => str.split(search).join(replacement)

    // Handle compound patterns first
    base = replaceAll(base, 'TION', 'شن')
    base = replaceAll(base, 'THE', 'ذا')
    base = replaceAll(base, 'PP', 'ب')
    base = replaceAll(base, 'CH', 'ش')
    base = replaceAll(base, 'SH', 'ش')
    base = replaceAll(base, 'TH', 'ذ')
    base = replaceAll(base, 'CK', 'ك')
    base = replaceAll(base, 'PH', 'ف')
    base = replaceAll(base, 'GH', 'غ')
    base = replaceAll(base, 'AI', 'اي')
    base = replaceAll(base, 'OU', 'او')

    // Replace single letters
    base = replaceAll(base, 'A', 'ا')
    base = replaceAll(base, 'B', 'ب')
    base = replaceAll(base, 'C', 'ك')
    base = replaceAll(base, 'D', 'د')
    base = replaceAll(base, 'E', 'ي')
    base = replaceAll(base, 'F', 'ف')
    base = replaceAll(base, 'G', 'ج')
    base = replaceAll(base, 'H', 'ه')
    base = replaceAll(base, 'I', 'ي')
    base = replaceAll(base, 'J', 'ي')
    base = replaceAll(base, 'K', 'ك')
    base = replaceAll(base, 'L', 'ل')
    base = replaceAll(base, 'M', 'م')
    base = replaceAll(base, 'N', 'ن')
    base = replaceAll(base, 'O', 'و')
    base = replaceAll(base, 'P', 'ب')
    base = replaceAll(base, 'Q', 'ك')
    base = replaceAll(base, 'R', 'ر')
    base = replaceAll(base, 'S', 'س')
    base = replaceAll(base, 'T', 'ت')
    base = replaceAll(base, 'U', 'ا')
    base = replaceAll(base, 'V', 'ف')
    base = replaceAll(base, 'W', 'و')
    base = replaceAll(base, 'X', 'كس')
    base = replaceAll(base, 'Y', 'ي')
    base = replaceAll(base, 'Z', 'ز')
    // Clean up FZCO forms
    base = replaceAll(base, '- FZCO', '')
    base = replaceAll(base, '-FZCO', '')
    base = replaceAll(base, '- fzco', '')
    base = replaceAll(base, '-fzco', '')
    base = replaceAll(base, 'FZCO', '')
    base = replaceAll(base, 'fzco', '')

    return `${base.trim()} - ش م ح`
  }

  const [apiScores, setApiScores] = useState({
    option1: 0,
    option2: 0,
    option3: 0,
  })

  const [loadingFields, setLoadingFields] = useState<{
    [key: string]: boolean
  }>({
    option1: false,
    option2: false,
    option3: false,
  })

  // const [existingCompanies, setExistingCompanies] = useState<{
  //   option1?: string;
  //   option2?: string;
  //   option3?: string;
  // }>({});

  // function calculateSimilarity(a: string, b: string): number {
  //   if (!a || !b) return 0;

  //   const s1 = a.toLowerCase().trim();
  //   const s2 = b.toLowerCase().trim();

  //   if (s1 === "" && s2 === "") return 100;
  //   if (s1 === s2) return 100;

  //   const len1 = s1.length;
  //   const len2 = s2.length;

  //   const matrix: number[][] = Array.from({ length: len1 + 1 }, (_, i) =>
  //     Array.from({ length: len2 + 1 }, (_, j) => (i === 0 ? j : j === 0 ? i : 0))
  //   );

  //   for (let i = 1; i <= len1; i++) {
  //     for (let j = 1; j <= len2; j++) {
  //       const cost = s1[i - 1] === s2[j - 1] ? 0 : 1;
  //       matrix[i][j] = Math.min(
  //         matrix[i - 1][j] + 1,      // deletion
  //         matrix[i][j - 1] + 1,      // insertion
  //         matrix[i - 1][j - 1] + cost // substitution
  //       );
  //     }
  //   }

  //   const distance = matrix[len1][len2];
  //   const maxLen = Math.max(len1, len2);
  //   const similarity = (maxLen - distance) / maxLen;

  //   return Math.round(similarity * 100);
  // }

  function calculateSimilarity(a: string, b: string): number {
    if (!a || !b) return 0

    const s1 = a.toLowerCase().trim()
    const s2 = b.toLowerCase().trim()

    const bigrams = (s: string) => {
      const arr: string[] = []
      for (let i = 0; i < s.length - 1; i++) arr.push(s[i] + s[i + 1])
      return arr
    }

    const b1 = bigrams(s1)
    const b2 = bigrams(s2)
    const b2Copy = [...b2]

    let common = 0
    for (const bg of b1) {
      const idx = b2Copy.indexOf(bg)
      if (idx !== -1) {
        common++
        b2Copy.splice(idx, 1)
      }
    }

    const totalBigrams = b1.length + b2.length
    if (totalBigrams === 0) return 100

    const sim = (2 * common) / totalBigrams
    return Math.round(sim * 10000) / 100
  }

  const [similarNames, setSimilarNames] = useState<{ [key: string]: string[] }>(
    {}
  )
  void similarNames

  const [activitiesError, setActivitiesError] = useState<string | null>(null)

  function validateActivities(): boolean {
    let hasErrors = false

    const hasSelectedActivity = rows.some((row) => row.option !== '')
    const hasMainActivity = rows.some((row) => row.checked)

    if (!hasSelectedActivity) {
      setActivitiesError('Please select at least one activity.')
      hasErrors = true
    } else if (!hasMainActivity) {
      setActivitiesError('Please select one main activity.')
      hasErrors = true
    } else {
      setActivitiesError('')
    }

    return !hasErrors
  }
  useEffect(() => {
    validateActivities()
  }, [rows])

  useEffect(() => {
    if (
      form.formState.isSubmitted &&
      Object.keys(form.formState.errors).length > 0
    ) {
      const timer = setTimeout(() => {
        scrollToFirstErrorField()
      }, 300) // Delay to ensure DOM is rendered

      return () => clearTimeout(timer)
    }
  }, [form.formState.errors, form.formState.isSubmitted])

  const scrollToFirstErrorField = () => {
    setTimeout(() => {
      const errorKeys = Object.keys(form.formState.errors)
      if (errorKeys.length > 0) {
        const fieldName = errorKeys[0]

        const selectors = [
          `[name="${fieldName}"]`,
          `[data-error-field="${fieldName}"]`,
          `[data-field="${fieldName}"]`,
          `.form-item[data-error-field="${fieldName}"]`,
          `.form-item[data-field="${fieldName}"]`,
          `.form-item[name="${fieldName}"]`,
          `[data-select-name="${fieldName}"]`,
        ]

        let input = null
        for (const selector of selectors) {
          input = document.querySelector(selector)
          if (input) break
        }

        if (input) {
          const wrapper =
            input.closest('.form-group, .form-item, .form-control') || input
          wrapper.scrollIntoView({ behavior: 'smooth', block: 'center' })
          const focusable = wrapper.querySelector(
            'input, select, textarea, [tabindex]'
          )
          if (
            focusable &&
            typeof (focusable as HTMLElement).focus === 'function'
          ) {
            ;(focusable as HTMLElement).focus()
          }
        } else {
          console.warn(
            `Field with name, data-error-field, or data-field="${fieldName}" not found in DOM.`
          )
        }
      }
    }, 300)
  }

  // Function to move between steps
  const nextStep = async () => {
    if (currentStep === 1) {
      const values = form.getValues()
      const schema = getStepSchema(currentStep, values)
      const requiredFields = Object.keys(
        schema.shape
      ) as (keyof ApplicationFormValues)[]
      const isStepValid = await form.trigger(requiredFields)

      const fieldsToCheck = [
        'option1English',
        'option2English',
        'option3English',
      ] as const

      const apiValidationPromises = fieldsToCheck.map(async (field) => {
        const val = values[field]?.trim() || ''
        if (!val) {
          form.setError(field, { message: 'Enter a value for this field.' })
          return false
        }

        try {
          const res = await checkCompany(val)
          if (!res.valid) {
            form.setError(field, { message: res.message })
            return false
          } else {
            form.clearErrors(field)
            form.setValue(
              field.replace('English', 'Arabic') as any,
              transliterate(val)
            )
            setApiScores((prev) => ({
              ...prev,
              [field.replace('English', '').toLowerCase()]: res.score,
            }))
            return true
          }
        } catch {
          form.setError(field, { message: 'API validation error' })
          return false
        }
      })

      const apiResults = await Promise.all(apiValidationPromises)
      const apiCheckValid = apiResults.every(Boolean)

      if (!isStepValid || !apiCheckValid) {
        const stepFields = Object.keys(
          schema.shape
        ) as (keyof ApplicationFormValues)[]
        const stepValues: Partial<ApplicationFormValues> = {}
        const stepErrors: Partial<FieldErrors<ApplicationFormValues>> = {}

        for (const field of stepFields) {
          stepValues[field] = form.getValues(field) as any
          const error = form.formState.errors[field]
          if (error) {
            stepErrors[field] = error as any
          }
        }

        console.log('Form Data For Step', currentStep, ':', stepValues)
        console.log('Validation Errors For Step', currentStep, ':', stepErrors)

        toast({
          title: 'Validation Error',
          description: 'Please fill all required fields correctly.',
          variant: 'destructive',
        })

        scrollToFirstErrorField()
        return
      }
    }

    if (currentStep === 4) {
      const values = form.getValues()
      const schema = getStepSchema(currentStep, values)
      const requiredFields = Object.keys(
        schema.shape
      ) as (keyof ApplicationFormValues)[]
      await form.trigger(requiredFields)

      const result = schema.safeParse(values)
      let hasFormErrors = false

      if (!result.success) {
        hasFormErrors = true
        const issues = result.error.issues

        issues.forEach((issue) => {
          const field = issue.path[0] as keyof ApplicationFormValues
          if (requiredFields.includes(field)) {
            form.setError(field, {
              type: 'manual',
              message: issue.message,
            })
          }
        })
      }

      const activitiesValid = validateActivities()

      // Show specific activity error messages
      if (!activitiesValid) {
        const hasSelectedActivity = rows.some((row) => row.option !== '')
        const hasMainActivity = rows.some((row) => row.checked)

        if (!hasSelectedActivity) {
          toast({
            title: 'No Activity Name Selected',
            description: 'Please select at least one activity.',
            variant: 'destructive',
          })
        } else if (!hasMainActivity) {
          toast({
            title: 'Main Activity Required',
            description:
              'Mark one activity as the main activity by checking its box.',
            variant: 'destructive',
          })
        }

        scrollToFirstErrorField()
        return
      }

      if (hasFormErrors) {
        toast({
          title: 'Error',
          description: 'Please fill in all required fields.',
          variant: 'destructive',
        })
        scrollToFirstErrorField()
        return
      }

      // All validations passed
      console.log(' Valid Activities Table:', rows)
      rows.forEach((row, index) => {
        console.log(`Row ${index + 1}:`, {
          ActivityID: row.option,
          ActivityCode: row.activityCode,
          DED: row.ded,
          TPA: row.tpa,
          RegulatoryAuthority: row.field4,
          IsMainActivity: row.checked,
        })
      })
    }

    // ensure members count and share sum
    if (currentStep === 5) {
      if (members.length === 0) {
        toast({
          title: 'No Members Added',
          description: 'Please add Members before proceeding.',
          variant: 'destructive',
        })
        return
      }

      const shareholderCount = members.filter((m) => m.roleShareholder).length

      const expectedShareholders = Number(localNumberOfShareholders)

      if (shareholderCount !== expectedShareholders) {
        toast({
          title: 'Incorrect Shareholder Assignment',
          description: `You must assign the Shareholder role to exactly ${expectedShareholders} member${expectedShareholders > 1 ? 's' : ''}.`,
          variant: 'destructive',
        })
        return
      }

      const totalShares = Number(form.getValues('totalNumberOfShares'))
      const sumShares = members.reduce(
        (acc, m) => acc + (m.numberOfShares || 0),
        0
      )

      if (sumShares !== totalShares) {
        toast({
          title: 'Error',
          description: `Sum of shares (${sumShares}) must equal total (${totalShares}).`,
          variant: 'destructive',
        })
        return
      }

      const gmCount = members.filter((m) => m.roleGeneralManager).length
      const secretaryCount = members.filter((m) => m.roleSecretary).length
      const directorCount = members.filter((m) => m.roleDirector).length

      if (members.length === 1) {
        const m = members[0]
        if (
          !m.roleGeneralManager ||
          !m.roleShareholder ||
          !m.roleSecretary ||
          !m.roleDirector
        ) {
          toast({
            title: 'All Roles Required',
            description: (
              <>
                Since there is only one member, you must assign all roles:
                <ul className='list-disc ml-4 mt-2'>
                  <li>General Manager</li>
                  <li>Secretary</li>
                  <li>Shareholder</li>
                  <li>Director</li>
                </ul>
              </>
            ),
            variant: 'destructive',
          })
          return
        }
      }

      const declaredShareholders = form.getValues('numberOfShareholders')

      if (shareholderCount !== declaredShareholders) {
        toast({
          title: 'Shareholder Count Mismatch',
          description: `The number of selected shareholders (${shareholderCount}) does not match the declared number (${declaredShareholders}) in step 3. Also, ensure the shares are correctly distributed to total exactly ${totalShares}.`,
          variant: 'destructive',
        })
        return
      } else {
        // Individual error messages for missing or duplicated roles
        if (gmCount !== 1) {
          toast({
            title: 'Error',
            description:
              gmCount === 0
                ? 'Exactly one General Manager must be selected.'
                : 'Only one General Manager is allowed.',
            variant: 'destructive',
          })
          return
        }

        if (secretaryCount !== 1) {
          toast({
            title: 'Error',
            description:
              secretaryCount === 0
                ? 'Exactly one Secretary must be selected.'
                : 'Only one Secretary is allowed.',
            variant: 'destructive',
          })
          return
        }

        if (directorCount < 1) {
          toast({
            title: 'Error',
            description: 'At least one Director must be selected.',
            variant: 'destructive',
          })
          return
        }
      }
    }

    const values = form.getValues()

    // Get the schema for the current step
    const schema = getStepSchema(currentStep, values)

    // Extract the fields required for validation from the schema
    const requiredFields = Object.keys(
      schema.shape
    ) as (keyof ApplicationFormValues)[]

    // Trigger validation for only the required fields
    await form.trigger(requiredFields)

    // Use Zod to validate the values manually and catch detailed errors
    const result = await schema.safeParseAsync(values)

    const stepFields = fieldsPerStep[currentStep]
    const currentStepValues: Partial<ApplicationFormValues> = {}

    for (const key of stepFields) {
      currentStepValues[key] = form.getValues(key) as any
    }

    console.log('Form Data For Step', currentStep, ':', currentStepValues)

    if (!result.success) {
      const issues = result.error.issues

      // Set manual errors for the invalid fields
      issues.forEach((issue) => {
        const field = issue.path[0] as keyof ApplicationFormValues

        if (requiredFields.includes(field)) {
          form.setError(field, {
            type: 'manual',
            message: issue.message,
          })
        }
      })

      // Show only the errors for the current step's required fields
      const visibleErrors: Partial<FieldErrors<ApplicationFormValues>> = {}
      for (const key of requiredFields) {
        const error = form.formState.errors[key]
        if (error) {
          visibleErrors[key] = error as any
        }
      }

      console.log('Validation Errors For Step', currentStep, ':', visibleErrors)

      toast({
        title: 'Error',
        description: 'Please fill in all the required fields.',
        variant: 'destructive',
      })
      scrollToFirstErrorField()
      return // Stop if there are validation errors
    }

    // Clear errors and proceed to the next step
    form.clearErrors()
    setStepValues((prev) => ({ ...prev, [currentStep]: values }))

    if (currentStep < 6) {
      setCurrentStep(currentStep + 1)
    }

    console.log(
      'Validation Errors For Step',
      currentStep,
      ': All fields have been filled correctly.'
    )
  }

  const prevStep = () => {
    if (currentStep > 1) {
      form.clearErrors()
      setCurrentStep(currentStep - 1)
    }
  }

  const onSubmit = async (data: ApplicationFormValues) => {
    // Validate declared shareholders count against added members
    // if (data.numberOfShareholders > members.length) {
    //   toast({
    //     title: 'Error',
    //     description: `Declared ${data.numberOfShareholders} shareholder(s) but only ${members.length} added.`,
    //     variant: 'destructive',
    //   })
    //   return
    // }
    // Validate totalNumberOfShares equals proposedShareCapital ÷ shareValue
    const expectedShares = data.proposedShareCapital / data.shareValue
    if (Number(data.totalNumberOfShares) !== expectedShares) {
      toast({
        title: 'Error',
        description: `Total shares should be ${expectedShares} (Share Capital ÷ Share Value).`,
        variant: 'destructive',
      })
      return
    }
    // Get the fields for the current step
    const fieldsToValidate = fieldsPerStep[currentStep]

    // Trigger validation for the fields in the current step
    const isValid = await form.trigger(fieldsToValidate)

    // Log form data for the current step
    console.log('Form Data for Step', currentStep, ':', form.getValues())

    // Check if there are validation errors or if everything is filled correctly
    if (Object.keys(form.formState.errors).length === 0) {
      console.log(
        'Validation Errors For Step',
        currentStep,
        ':',
        'All fields have been filled correctly.'
      )
    } else {
      console.log(
        'Validation Errors For Step',
        currentStep,
        ':',
        form.formState.errors
      )
    }

    if (isValid) {
      setIsLoading(true)
      try {
        // Prepare activities data from the rows state
        const activities = rows
          .filter((row) => row.activityCode) // Only include rows with activity codes
          .map((row) => ({
            activityCode: row.activityCode,
            isMainActivity: row.checked,
          }))

        console.log('Activities being sent:', activities)

        // Prepare payload mapping form values to API schema
        const payload = mainpulateLicenseApplicationData({
          ...data,
          activities,
        })
        console.log(
          '☞ sending payload to /api/LIC/create:',
          JSON.stringify(payload, null, 2)
        )
        // Send request to create license application
        const result = await createLicenseApplication(payload)
        // Example response schema: { data: <id>, message: string, license: object }
        console.log('API response:', result)
        console.log('Returned data (ID):', result.data)
        console.log('Returned message:', result.message)
        console.log('Returned license object:', result.license)
        // API returns { data: <id>, message, license }
        const licenseId = result.data ?? result.id ?? result.license?.id
        console.log('Extracted licenseId:', licenseId)
        // Log the full license data as JSON
        console.log(`License record: ${JSON.stringify(result.license)}`)
        // also submit members if any
        if (members.length > 0) {
          // Ensure numberOfShares is set to 0 for any member where it's undefined
          const normalizedMembers = members.map((member) => ({
            ...member,
            numberOfShares: member.numberOfShares || 0,
          }))
          const membersPayload = mainpulateLicenseMembersData(
            normalizedMembers as any
          )
          console.log(
            `☞ sending members payload to /api/LIC/${licenseId}/members:`,
            JSON.stringify(membersPayload, null, 2)
          )
          const memberResult = await createLicenseApplicationMembers(
            licenseId,
            membersPayload
          )
          console.log('Member creation response:', memberResult)
          // Extract member items from response robustly by scanning common array properties
          const possibleArrays = [
            memberResult,
            memberResult.data,
            memberResult.data?.data,
            memberResult.data?.members,
            memberResult.members,
          ]
          const rawItems: any[] =
            possibleArrays.find((arr) => Array.isArray(arr)) || []
          // Map raw items to member IDs
          const memberIds: (string | number)[] = rawItems.map((item) =>
            typeof item === 'object'
              ? (item.id ?? item.data ?? item.memberId)
              : item
          )
          // Upload files for each member
          for (let i = 0; i < normalizedMembers.length; i++) {
            const m = normalizedMembers[i]
            const memberId = memberIds[i]
            if (!memberId) {
              console.error(
                'Missing memberId for file upload:',
                memberResult,
                rawItems
              )
              toast({
                title: `Cannot upload files: memberId undefined for ${m.firstName}`,
                variant: 'destructive',
              })
              continue
            }
            try {
              // Debug: log upload parameters
              console.log('Calling uploadMemberFiles with:', {
                licenseId,
                memberId,
                files: { passport: m.passport, eid: m.emiratesID },
                data: {
                  Member_First_Name: m.firstName,
                  Member_Last_Name: m.lastName,
                  Passport_Number: m.passportNumber,
                },
              })
              // Only passport (required) and eid (optional) per API spec; only three form fields
              await uploadMemberFiles(
                licenseId,
                memberId,
                {
                  passport: m.passport!,
                  eid: m.emiratesID || undefined,
                },
                {
                  Member_First_Name: m.firstName || '',
                  Member_Last_Name: m.lastName || '',
                  Passport_Number: m.passportNumber || '',
                }
              )
              toast({
                title: `Files uploaded for ${m.firstName}`,
                variant: 'success',
              })
            } catch (fileError: any) {
              console.error('File upload error for member', m, fileError)
              toast({
                title: `File upload failed for ${m.firstName}`,
                description:
                  fileError.response?.data?.message || fileError.message,
                variant: 'destructive',
              })
            }
          }
        }
        toast({
          title: result.message ?? 'Success!',
          // description: JSON.stringify(result),
          variant: 'success',
        })
        // Clear form and step values and redirect to step 1
        form.reset()
        setStepValues({})
        setCurrentStep(1)
        // Log or handle returned application ID
        console.log('License application ID:', licenseId)
      } catch (error: any) {
        console.error(
          'API error response:',
          error.response?.data || error.message
        )
        toast({
          title: 'Fail!',
          description:
            error.response?.data?.message ||
            error.message ||
            'Something went wrong.',
          variant: 'error',
        })
      } finally {
        setIsLoading(false)
      }
    } else {
      scrollToFirstErrorField()
      // Handle validation errors
      toast({
        title: 'Error',
        description: 'Please fill in all the required fields.',
        variant: 'destructive',
      })
    }
  }

  // Detect external access via id query param
  const externalId =
    typeof window !== 'undefined'
      ? new URLSearchParams(window.location.search).get('id')
      : null

  useEffect(() => {
    if (externalId) {
      // direct client-side access
      Axios.get(`${BASE_URL}/api/users/getUser?id=${externalId}`)
        .then((res) => {
          form.setValue('cem', res.data.name)
        })
        .catch(() => {
          setIsvaildId(false)
        })
    }
  }, [externalId])

  if (!isVaildId) {
    return null
  }
  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        {/* <TopNav links={topNav} /> */}
        <div className='ml-auto flex items-center space-x-4'>
          {/* <Search /> */}
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Main ===== */}
      <Main>
        <div className='mb-2 flex items-center justify-between space-y-2'>
          <h1 className='text-2xl font-bold tracking-tight lg:text-[28px]'>
            License Application
          </h1>
          <div className='flex items-center space-x-2 lg:relative sm:fixed lg:w-auto sm:space-x-2 fixed bottom-0 right-0 p-3 bg-background w-full text-right flex justify-end z-10 sm:z-auto sm:bg-transparent sm:p-0'></div>
        </div>
        <ApplicationProgress currentStep={currentStep} />

        {/* === Step 1 Content === */}
        <Step1LicenseApplication
          form={form}
          loadingFields={loadingFields}
          setLoadingFields={setLoadingFields}
          apiScores={apiScores}
          setApiScores={setApiScores}
          checkCompany={checkCompany}
          transliterate={transliterate}
          setSimilarNames={setSimilarNames}
          calculateSimilarity={calculateSimilarity}
          currentStep={currentStep}
        />

        {/* === Step 2 Content === */}
        {currentStep === 2 && (
          <Step2LicenseApplication form={form} isDachSales={isDachSales} />
        )}

        {/* === Step 3 Content === */}
        {currentStep === 3 && (
          <Step3LicenseApplication
            form={form}
            localNumberOfShareholders={localNumberOfShareholders}
            setLocalNumberOfShareholders={setLocalNumberOfShareholders}
            localProposedCapital={localProposedCapital}
            setLocalProposedCapital={setLocalProposedCapital}
            localShareValue={localShareValue}
            setLocalShareValue={setLocalShareValue}
            calculateShareCapitalperShareholder={
              calculateShareCapitalperShareholder
            }
            calculateTotalNumberOfShares={calculateTotalNumberOfShares}
          />
        )}

        {/* === Step 4 Content === */}
        {currentStep === 4 && (
          <Step4LicenseApplication
            form={form}
            rows={rows.map((row) => ({ ...row, id: String(row.id) }))}
            handleDelete={handleDelete}
            handleOptionChange={handleOptionChange}
            handleFieldChange={handleFieldChange}
            handleCheckedChange={handleCheckedChange}
            handleAddRow={handleAddRow}
            activitiesData={activitiesData}
            activitiesError={activitiesError ?? ''}
          />
        )}

        {/* === Step 5 Content === */}
        {currentStep === 5 && (
          <Step5LicenseApplication
            form={form}
            members={members}
            setMembers={setMembers}
          />
        )}

        {/* === Step 6 Content === */}
        {currentStep === 6 && <Step6LicenseApplication form={form} />}

        {/* Buttons Section */}

        <div
          className={
            externalId
              ? 'fixed bottom-0 left-0 right-0 p-4 bg-background flex justify-center items-center space-x-4 z-10 fz-form-btns'
              : 'fixed bottom-0 right-0 pl-6 pr-6 pb-3 pt-4 bg-background flex justify-end space-x-2 z-10 fz-form-btns'
          }
        >
          {/* <div className='flex-1'>
            <Button variant={'btn_outline'}>Save as Draft</Button>
          </div> */}
          <div className='flex gap-2'>
            {/* The 'Previous' button will not appear on the first step */}
            {currentStep !== 1 && (
              <Button variant='btn_outline' onClick={prevStep}>
                Previous
              </Button>
            )}

            {/* The 'Next' button will not appear on the last step */}
            {currentStep !== 6 && (
              <Button variant='default' onClick={nextStep}>
                Next
              </Button>
            )}
            {/* The Submit button will appear only in step 6 */}
            {currentStep === 6 && (
              <Button
                variant={'default'}
                onClick={() => {
                  onSubmit(form.getValues())
                }}
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                    Processing...
                  </>
                ) : (
                  'Submit'
                )}
              </Button>
            )}
          </div>
        </div>
      </Main>
    </>
  )
}
