import heroBanner from '@/assets/images/public-tracker/fz-hero-banner.jpg'

const HeroBannerSection = () => {
  return (
    <section
      className='fz-tracker-bg w-full bg-cover bg-no-repeat bg-[position:68%_70%] md:bg-[position:80%_80%]'
      style={{
        backgroundImage: `url(${heroBanner})`,
      }}
    >
      <div className='container h-[580px] md:h-[640px] w-full flex items-center'>
        <div className='flex flex-col w-9/12 md:max-w-lg pt-6'>
          <h1 className='uppercase font-tt-firs-neue text-3xl xl:text-4xl mb-4 text-slate-900'>
            Welcome to the IFZA Customers Hub
          </h1>
          <p className='font-wotfard font-light text-slate-800'>
            IFZA Customer Hub is our digital platform designed to optimize
            business incorporation by offering a range of features and
            functionalities to our Direct Customers.
          </p>
        </div>
      </div>
    </section>
  )
}

export default HeroBannerSection
