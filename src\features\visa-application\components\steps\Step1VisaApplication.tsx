import React, { useEffect } from 'react'
import { useFormContext, useWatch } from 'react-hook-form'
import { Lightbulb, FilePenLine } from 'lucide-react'
import PhoneInput from 'react-phone-input-2'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Checkbox } from '@/components/ui/checkbox'
import { CountryDropdown } from '@/components/ui/country-dropdown'
import { DateTimePicker } from '@/components/ui/datetime-picker'
import FileUploadField from '@/components/ui/fileUpload'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { ApplicationFormValues } from '../../types/application-form-types'

interface Step1FormProps {
  showEVisaType: boolean
}

const Step1VisaApplication: React.FC<Step1FormProps> = ({ showEVisaType }) => {
  const form = useFormContext<ApplicationFormValues>()
  const visaType = useWatch({ control: form.control, name: 'visaType' })
  const visaApplicationType = useWatch({
    control: form.control,
    name: 'visaApplicationType',
  })
  const nationality = useWatch({ control: form.control, name: 'nationality' })
  const residentVisaStamping = useWatch({
    control: form.control,
    name: 'residentVisaStamping',
  })
  const eVisaApplicationType = useWatch({
    control: form.control,
    name: 'eVisaApplicationType',
  })
useEffect(() => {
  form.register('serviceType');
}, [form]);

useEffect(() => {
  const subscription = form.watch((values) => {
    const visaType = values.visaType;

    let newServiceType = '';
    if (
      visaType === 'New Employment Visa' ||
      visaType === 'New Investor Visa' ||
      visaType === 'New Partner Visa'
    ) {
      newServiceType = 'New';
    } else if (
      visaType === 'Employment Visa Renewal' ||
      visaType === 'Work Permit Renewal'
    ) {
      newServiceType = 'Renewal';
    } else if (visaType === 'Work Permit Only') {
      newServiceType = 'Work Permit'
    }

    const currentServiceType = form.getValues('serviceType');
    if (newServiceType && newServiceType !== currentServiceType) {
      form.setValue('serviceType', newServiceType);
    }
  });

  return () => subscription.unsubscribe();
}, [form]);

  // Log all form values for debugging
  console.log('Step 1 Form Values:', form.watch());

  return (
    <>
      <form className='space-y-4 fz-form'>
        <div className='flex flex-wrap space-between gap-y-4'>
          <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
            <FormField
              control={form.control}
              name='appUpdatesEmail'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    App Updates Email Address{' '}
                    <span style={{ color: 'red' }}>*</span>
                  </FormLabel>
                  <FormControl>
                    <Input type='Enter App Updates Email' {...field} />
                  </FormControl>
                  <FormDescription>
                    Email address where notification updates and issued
                    Visas/communication with regards to the Visa will be sent.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          {/* App Updates WhatsApp Number */}
          <div className='flex-1'>
            <FormField
              control={form.control}
              name='whatsAppNumber'
              render={({ field }) => (
                <FormItem data-error-field='whatsAppNumber'>
                  <FormLabel>App Updates WhatsApp Number</FormLabel>
                  <PhoneInput
                    country={'ae'}
                    value={field.value || ''}
                    onChange={(phone) => field.onChange(phone)}
                    containerClass='fz-country-code flex h-9 w-full rounded-tl-[6px] rounded-bl-[6px] border border-input bg-transparent text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm'
                    inputClass=' flex  !h-9 !w-full rounded-tl-[6px] rounded-bl-[6px] border !border-transparent !bg-transparent px-3 text-base !shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm transform translate-y-[-2px]'
                    buttonClass='!h-100 !border-l-transparent !border-t-transparent !bg-slate-200 dark:!bg-slate-800 dark:!border-r-transparent !border-b-transparent !border-gray-200 !bg-transparent !rounded-tl-[8px] !rounded-bl-[6px]'
                    enableSearch={true}
                    searchPlaceholder='Search country...'
                  />
                  <FormDescription>
                    Mobile Number where notification updates and issued
                    Visas/communication with regards to the Visa will be sent.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
        {/* Application Date */}
        <div className='flex flex-wrap space-between gap-y-4'>
          <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
            <FormField
              control={form.control}
              name='applicationDate'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Application Date <span className='text-red-500'>*</span>
                  </FormLabel>
                  <DateTimePicker
                    granularity='day'
                    value={field.value || new Date()}
                    onChange={field.onChange}
                    displayFormat={{
                      hour24: 'dd MMMM yyyy',
                    }}
                    disabled={true}
                  />
                  <FormDescription>dd-MMM-yyyy</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          {/* Visa Type field */}
          <div className='flex-1'>
            <FormField
              control={form.control}
              name='visaType'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Visa Type <span style={{ color: 'red' }}>*</span>{' '}
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <span
                          className='text-yellow-600 cursor-pointer'
                          style={{ fontSize: '1.1rem', lineHeight: '1' }}
                        >
                          🛈
                        </span>
                      </TooltipTrigger>
                      <TooltipContent className='max-w-xs text-sm alert-bg-warning border border-yellow-300 rounded shadow-lg p-2 text-black'>
                        If you are applying for the first time, please select
                        "New Employment Visa". If you currently have a valid UAE
                        Residence Visa and you wish to apply for the work permit
                        only, please select "Work Permit"
                      </TooltipContent>
                    </Tooltip>
                  </FormLabel>
                  <FormControl>
                    <Select
                      {...field}
                      onValueChange={(value) => field.onChange(value)}
                      value={field.value}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder='Please Select' />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='Work Permit Only'>
                          Work Permit Only
                        </SelectItem>
                        <SelectItem value='New Employment Visa'>
                          New Employment Visa
                        </SelectItem>
                        <SelectItem value='New Partner Visa'>
                          New Partner Visa
                        </SelectItem>
                        <SelectItem value='New Investor Visa'>
                          New Investor Visa
                        </SelectItem>
                        <SelectItem value='Employment Visa Renewal'>
                          Employment Visa Renewal
                        </SelectItem>
                        <SelectItem value='Work Permit Renewal'>
                          Work Permit Renewal
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>

                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Instructions Text */}
        {visaType === 'Employment Visa Renewal' && (
          <>
            <Alert className='flex items-start '>
              <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
              </span>
              <div className='flex flex-col ml-2'>
                <AlertTitle>
                  Please note the following requirements :
                </AlertTitle>
                <AlertDescription>
                  <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    If the visa expired while the applicant was outside UAE,
                    renewal will not be possible. You will need to cancel and
                    re-apply (all charges applicable).
                  </p>
                  <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    To proceed with a visa renewal application, we required for
                    the License and establishment card of the company to be
                    valid for 60 days and more
                  </p>
                </AlertDescription>
              </div>
            </Alert>
          </>
        )}

        {/* "Do you require a family on hold letter ?" field */}
        {(visaType === 'New Employment Visa' ||
          visaType === 'New Investor Visa' ||
          visaType === 'New Partner Visa') && (
          <>
            <div className='mb-4'>
              <FormField
                control={form.control}
                name='familyOnHoldLetter'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Do you require a family on hold letter ?{' '}
                      <span style={{ color: 'red' }}>*</span>
                    </FormLabel>
                    <FormControl>
                      <Select
                        {...field}
                        onValueChange={(value) => field.onChange(value)}
                        value={field.value}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder='Select' />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value='Yes'>Yes</SelectItem>
                          <SelectItem value='No'>No</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormDescription>
                      Family on Hold Letter is required when the applicant has
                      dependents on their current visa which they want to cancel
                      and apply for a new visa with IFZA.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </>
        )}

        {/* Instructions Text */}
        {(visaType === 'New Investor Visa' ||
          visaType === 'New Partner Visa') && (
          <>
            <Alert className='flex items-start '>
              <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
              </span>
              <div className='flex flex-col ml-2'>
                <AlertTitle>
                  Please note the following requirements :
                </AlertTitle>
                <AlertDescription>
                  <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    Investor Visa: This can be issued to a single shareholder
                    company. Minimum share capital should be AED 48,000.
                  </p>
                  <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    Partner Visa: This can be issued to 2 shareholders of the
                    company. Minimum individual share capital for the applicant
                    should be AED 48,000.
                  </p>
                </AlertDescription>
              </div>
            </Alert>
          </>
        )}

        <FormField
          control={form.control}
          name='tradeLicenseValidated'
          render={({ field }) => (
            <FormItem>
              <div className='flex items-start space-x-2 border border-dashed border-yellow-400 rounded-md p-4 bg-yellow-50 dark:bg-primary/20'>
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={(checked: boolean) =>
                      field.onChange(checked)
                    }
                    className='mt-1'
                    data-field='tradeLicenseValidated'
                  />
                </FormControl>
                <FormLabel className='text-base text-primary dark:text-primary-light'>
                  I confirm that the trade license is valid for at least{' '}
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <span className='underline cursor-help text-primary font-bold'>
                          60 days
                        </span>
                      </TooltipTrigger>
                      <TooltipContent className='max-w-sm'>
                        <p>
                          This means the trade license expiry date must be at
                          least 60 days from today.
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  , which is mandatory to proceed with the visa application.
                </FormLabel>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {visaType === 'Employment Visa Renewal' && (
          <>
            <FormField
              control={form.control}
              name='renewalDisclaimer'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Renewal Disclaimer <span className='text-red-600'>*</span>
                  </FormLabel>

                  <div className='flex items-start space-x-2 border border-dashed border-yellow-400 rounded-md p-4 bg-yellow-50 dark:bg-primary/20'>
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={(checked: boolean) =>
                          field.onChange(checked)
                        }
                        className='mt-1'
                        data-field='renewalDisclaimer'
                      />
                    </FormControl>
                    <FormLabel className='text-base text-primary dark:text-primary-light'>
                      <div>
                        I confirm that the applicant is{' '}
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <span className='underline cursor-help font-bold text-primary'>
                                inside the UAE and the company license is valid
                                for more than 60 days
                              </span>
                            </TooltipTrigger>
                            <TooltipContent className='max-w-sm'>
                              <p>
                                The applicant must be physically present inside
                                the country, and the company’s trade license
                                must be valid for more than 60 days in order to
                                proceed with Employment Visa Renewal.
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                        .
                      </div>
                    </FormLabel>
                  </div>

                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Instructions Text */}
            <Alert className='flex items-start border-dashed border-blue-500 mt-2 bg-slate-50 dark:bg-primary/20'>
              <span className='bg-blue-50 p-2 rounded inline-block dark:text-primary-dark'>
                <Lightbulb className='w-4 h-4 stroke-blue-500 dark:text-primary-dark' />
              </span>
              <div className='flex space-y-2 flex-col ml-3'>
                <AlertDescription>
                  <p className='text-sm text-slate-800 dark:text-slate-400'>
                    Nationality amendments cannot be processed during visa
                    renewal.
                  </p>
                  <p className='text-sm text-slate-800 dark:text-slate-400'>
                    If a nationality amendment is required, please request once
                    the visa renewal is complete.
                  </p>
                </AlertDescription>
              </div>
            </Alert>
          </>
        )}

        {/* Updated RV copy */}
        {visaType === 'Work Permit Renewal' && (
          <>
            <div className='mb-4'>
              <FormField
                control={form.control}
                name='updatedRVCopy'
                render={({ field }) => (
                  <FormItem data-error-field='updatedRVCopy'>
                    <FormLabel>
                      Updated RV copy <span style={{ color: 'red' }}>*</span>
                    </FormLabel>
                    <FormControl>
                      <FileUploadField
                        accept='.jpg,.jpeg'
                        value={field.value}
                        onchoose={(file) => {
                          form.setValue('updatedRVCopy', file || '')
                          form.clearErrors('updatedRVCopy')
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </>
        )}

        {/* Visa Application Type field */}
        <div className='mb-4'>
          <FormField
            control={form.control}
            name='visaApplicationType'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Visa Application Type <span style={{ color: 'red' }}>*</span>
                </FormLabel>
                <FormControl>
                  <Select
                    {...field}
                    onValueChange={(value) => field.onChange(value)}
                    value={field.value}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='No'>
                        Out of Country
                      </SelectItem>
                      <SelectItem value='Yes'>In-Country</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>

                <FormDescription>
                  Select the correct application type. Visa is processed based
                  on the information provided. Any wrong/incorrect information
                  may result in delays or may even lead to application
                  rejection. Additional charges may incur.
                </FormDescription>
                <FormMessage />
                <Alert className='flex items-start'>
                  <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                    <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                  </span>
                  <div className='flex flex-col ml-2'>
                    <AlertTitle>Clarification :</AlertTitle>
                    <AlertDescription>
                      <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                        <span className='font-medium text-gray-800'>
                          In-Country :
                        </span>{' '}
                        The applicant is currently{' '}
                        <span className='font-semibold'>inside the UAE</span>{' '}
                        during visa processing.
                      </p>
                      <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                        <span className='font-medium text-gray-800'>
                          Out of Country :
                        </span>{' '}
                        The applicant is{' '}
                        <span className='font-semibold'>outside the UAE</span>{' '}
                        during visa processing and{' '}
                        <span className='font-semibold'>
                          cannot enter the country until the visa is issued
                        </span>
                        .
                      </p>
                    </AlertDescription>
                  </div>
                </Alert>
              </FormItem>
            )}
          />
        </div>

        {/* "Do you want to apply VFL for this VISA ?" field */}

        {(visaType === 'New Employment Visa' ||
          visaType === 'New Investor Visa' ||
          visaType === 'New Partner Visa') && (
          <>
            <div className='mb-4'>
              <FormField
                control={form.control}
                name='visaFree'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Do you want to apply VFL for this VISA ?{' '}
                      <span style={{ color: 'red' }}>*</span>
                    </FormLabel>
                    <FormControl>
                      <Select
                        {...field}
                        onValueChange={(value) => field.onChange(value)}
                        value={field.value}
                      >
                        <SelectTrigger data-field='visaFree'>
                          <SelectValue placeholder='Select' />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value='Yes'>Yes</SelectItem>
                          <SelectItem value='No'>No</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormDescription>
                      If you want to apply Free Visa for Life promotion for this
                      Visa select Yes, select No if you already applied for Free
                      Visa for Life with other Visa application.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </>
        )}

        {/*Checkbox : Outside Country Visa Declaration */}
        {visaApplicationType === 'No' && (
          <>
            <FormField
              control={form.control}
              name='outsideCountryInstructions'
              render={({ field }) => (
                <FormItem>
                  <div className='flex items-start space-x-2 border border-dashed border-yellow-400 rounded-md p-4 bg-yellow-50 dark:bg-primary/20'>
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={(checked: boolean) =>
                          field.onChange(checked)
                        }
                        className='mt-1'
                        data-field='outsideCountryInstructions'
                      />
                    </FormControl>
                    <FormLabel className='text-base text-primary dark:text-primary-light'>
                      Applicant should not enter the UAE until the E-visa has
                      been issued. Failure to do so will lead to the application
                      being rejected.{' '}
                    </FormLabel>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='outsideCountry'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Outside Country Visa Declaration{' '}
                    <span className='text-red-600'>*</span>
                  </FormLabel>

                  <div className='flex items-start space-x-2 border border-dashed border-yellow-400 rounded-md p-4 bg-yellow-50 dark:bg-primary/20'>
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={(checked: boolean) =>
                          field.onChange(checked)
                        }
                        className='mt-1'
                        data-field='outsideCountry'
                      />
                    </FormControl>
                    <FormLabel className='text-base text-primary dark:text-primary-light'>
                      <div>
                        I confirm that I have{' '}
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <span className='underline cursor-help font-bold text-primary'>
                                no active visa in the UAE
                              </span>
                            </TooltipTrigger>
                            <TooltipContent className='max-w-sm'>
                              <p>
                                Having an active tourist or resident visa in the
                                UAE may result in delays or even rejection of
                                the application.
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                        .
                      </div>
                    </FormLabel>
                  </div>

                  <FormMessage />
                </FormItem>
              )}
            />
          </>
        )}

        {/* Current Visa Status */}
        {visaApplicationType === 'Yes' && (
          <>
            <div className='mb-4'>
              <FormField
                control={form.control}
                name='currentVisaStatus'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Current Visa Status{' '}
                      <span style={{ color: 'red' }}>*</span>{' '}
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <span
                            className='text-yellow-600 cursor-pointer'
                            style={{ fontSize: '1.1rem', lineHeight: '1' }}
                          >
                            🛈
                          </span>
                        </TooltipTrigger>
                        <TooltipContent className='max-w-xs text-sm alert-bg-warning border border-yellow-300 rounded shadow-lg p-2 text-black'>
                          Please ensure to select the correct visa status.
                          Incorrect information might result in delays, or the
                          application being rejected by immigration.
                        </TooltipContent>
                      </Tooltip>
                    </FormLabel>
                    <FormControl>
                      <Select
                        {...field}
                        onValueChange={(value) => field.onChange(value)}
                        value={field.value}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder='Select' />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value='Tourist'>Tourist</SelectItem>
                          <SelectItem value='Visa on Arrival'>
                            Visa on Arrival
                          </SelectItem>
                          <SelectItem value='Cancelled'>Cancelled</SelectItem>
                          <SelectItem value='Active Residence Visa'>
                            Active Residence Visa
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormDescription>
                      Please make sure you do not have any active Tourist or
                      Residence Visas in UAE. In case there is an active visa,
                      the application might be delayed or rejected by the
                      immigration.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </>
        )}

        {/* Nationality of the Applicant */}
        <div className='mb-4'>
          <FormField
            control={form.control}
            name='nationality'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Nationality of the Applicant{' '}
                  <span style={{ color: 'red' }}>*</span>
                </FormLabel>
                <FormControl>
                  <CountryDropdown
                    data-field='nationality'
                    placeholder='Country'
                    defaultValue={field.value as string}
                    onChange={(country) => {
                      field.onChange(country.name)
                    }}
                  />
                </FormControl>
                <FormDescription>
                  Please mention the Nationality of the Applicant.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        {/* Instructions Text */}

        {visaType === 'New Employment Visa' &&
          visaApplicationType === 'No' &&
          (nationality === 'Bangladesh' ||
            nationality === 'Egypt' ||
            nationality === 'Indonesia' ||
            nationality === 'Kenya' ||
            nationality === 'Malaysia' ||
            nationality === 'Sri Lanka' ||
            nationality === 'Tunisia') && (
            <>
              <Alert className='flex items-start '>
                <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                  <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                </span>
                <div className='flex flex-col ml-2'>
                  <AlertTitle>Please note :</AlertTitle>

                  <AlertDescription>
                    <p className="text-sm dark:text-red-500 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                      For the selected nationality, the visa applicant may be
                      required to visit the UAE Embassy/Consulate in their home
                      country, for the issuance of the e-visa.
                    </p>
                    <p className="text-sm dark:text-red-500 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                      If however, the applicant no longer resides in their home
                      country, they will still be required to travel to their
                      home country and visit the UAE Embassy/Consulate.
                    </p>
                    <p className="text-sm dark:text-red-500 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                      If they are unable to do so, the application will need to
                      be withdrawn and the money will not be refunded.
                    </p>
                  </AlertDescription>
                </div>
              </Alert>
            </>
          )}

        {/* Instructions Text */}
        {visaType === 'Employment Visa Renewal' && (
          <>
            <Alert className='flex items-start '>
              <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
              </span>
              <div className='flex flex-col ml-2'>
                <AlertTitle>Important Note :</AlertTitle>
                <AlertDescription>
                  <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    Kindly note, e-visa & status change will not be issued
                    during this process.
                  </p>
                  <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    We will only require updated passport sized picture,
                    emirates ID application form and medical fitness test to
                    proceed.
                  </p>
                </AlertDescription>
              </div>
            </Alert>
          </>
        )}
        {/* Show thses when nationality is "Afghanistan" or "Iran" or "Iraq" or "Pakistan"  */}
        {(nationality === 'Afghanistan' ||
          nationality === 'Iran' ||
          nationality === 'Iraq' ||
          nationality === 'Pakistan') && (
          <>
            <Alert className='flex items-start '>
              <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
              </span>
              <div className='flex flex-col ml-2'>
                <AlertTitle>Important Note :</AlertTitle>
                <AlertDescription>
                  <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    Please note for the selected nationality, the National ID
                    card (Front & Back is required for Visa Processing) Please
                    upload a valid National ID card.
                  </p>
                </AlertDescription>
              </div>
            </Alert>

            {/* National ID Card upload */}
            <div className='mb-4'>
              <FormField
                control={form.control}
                name='idCardUpload'
                render={({ field }) => (
                  <FormItem data-error-field='idCardUpload'>
                    <FormLabel>
                      National ID Card upload{' '}
                      <span style={{ color: 'red' }}>*</span>
                    </FormLabel>
                    <FormControl>
                      <FileUploadField
                        accept='.pdf,.jpg,.png'
                        value={field.value}
                        onchoose={(file) => {
                          form.setValue('idCardUpload', file || '')
                          form.clearErrors('idCardUpload')
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </>
        )}
        {/* Show theses when 'visaApplicationType' is "In-Country" and nationality is "AFG" or "BGD" or "PAK" or "NGA" */}
        {visaApplicationType === 'Yes' &&
          (nationality === 'Afghanistan' ||
            nationality === 'Bangladesh' ||
            nationality === 'Pakistan' ||
            nationality === 'Nigeria') && (
            <>
              {/* Instructions Text */}
              <Alert className='flex items-start '>
                <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                  <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                </span>
                <div className='flex flex-col ml-2'>
                  <AlertTitle>Important Notes :</AlertTitle>

                  <AlertDescription>
                    <p className="text-sm dark:text-red-500 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                      Please note for this Nationality, the status change after
                      the issuance of the Visa may not be possible. The
                      applicant might have to leave the country and enter again
                      using the issued Visa. This is solely at the discretion of
                      the relevant Govt. Authorities. All charges are still
                      applicable including the status change fees.
                    </p>
                    <p className="text-sm dark:text-red-500 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                      Alternatively, you can apply for an Outside the country
                      Visa and the applicant can enter UAE using the issued
                      Visa.
                    </p>
                  </AlertDescription>
                </div>
              </Alert>

              {/* I agree to the above status change statement and the rules associated : Checkbox */}

              <FormField
                control={form.control}
                name='Agreed'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      I agree to the above status change statement and the rules
                      associated. <span style={{ color: 'red' }}>*</span>
                    </FormLabel>

                    <div className='flex items-start space-x-2 border border-dashed border-yellow-400 rounded-md p-4 bg-yellow-50 dark:bg-primary/20'>
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={(checked: boolean) =>
                            field.onChange(checked)
                          }
                          className='mt-1'
                          data-field='Agreed'
                        />
                      </FormControl>
                      <FormLabel className='text-base text-primary dark:text-primary-light'>
                        <div>Agreed</div>
                      </FormLabel>
                    </div>

                    <FormMessage />
                  </FormItem>
                )}
              />
            </>
          )}

        {/* E-Visa Application Type */}
        {showEVisaType && (
          <>
            <div className='mb-4'>
              <FormField
                control={form.control}
                name='eVisaApplicationType'
                render={({ field }) => (
                  <FormItem data-error-field='eVisaApplicationType'>
                    <FormLabel>
                      E-Visa Application Type{' '}
                      <span style={{ color: 'red' }}>*</span>
                    </FormLabel>
                    <FormControl>
                      <Select
                        {...field}
                        onValueChange={(value) => field.onChange(value)}
                        value={field.value}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder='Select E-Visa Application Type' />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value='Standard'>Standard</SelectItem>
                          <SelectItem value='VIP'>VIP</SelectItem>
                          <SelectItem value='VIP E-VISA (24 hrs)'>
                            VIP E-VISA (24 hrs)
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormDescription>
                      Additional charges apply for the VIP E-Visa issuance. From
                      the time of receiving the completed application and
                      payment confirmation, the E-Visa will be issued within 24
                      hours. T&Cs Apply.{' '}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </>
        )}

        {/* Resident Visa Stamping Type */}

        <div className='mb-4'>
          <FormField
            control={form.control}
            name='residentVisaStamping'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Resident Visa Stamping Type{' '}
                  <span style={{ color: 'red' }}>*</span>
                </FormLabel>
                <FormControl>
                  <Select
                    {...field}
                    onValueChange={(value) => field.onChange(value)}
                    value={field.value}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='Standard'>Standard</SelectItem>
                      <SelectItem value='VIP'>VIP</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormDescription>
                  After your Visa is approved and you have completed the
                  formalities, Residence Visa is then issued/stamped on your
                  passport. Please ensure that you have one full page on the
                  passport without any stamps.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {(residentVisaStamping === 'VIP' || eVisaApplicationType === 'VIP') && (
          <>
            <Alert className='flex items-start '>
              <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
              </span>
              <div className='flex flex-col ml-2 '>
                <AlertTitle className='flex flex-col ml-2 font-semibold'>
                  VIP Processing Timeline
                </AlertTitle>
                <AlertDescription>
                  <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    The 24-hour processing timeframe begins only upon receipt of
                    all required documents, including proof of payment and a
                    signed employment contract :
                  </p>

                  <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    Received before 11:00 AM: Processed within 24 hours.
                  </p>
                  <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    Received after 11:00 AM: 24h timeline to start the next
                    business day at 8:30 AM.
                  </p>
                  <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    Timelines are subject to immigration approval.
                  </p>
                </AlertDescription>
              </div>
            </Alert>

            {/* Instructions Text */}
            {residentVisaStamping === 'VIP' && (
              <>
                <Alert className='flex items-start border-dashed border-blue-500 mt-2 bg-slate-50 dark:bg-primary/20'>
                  <span className='bg-blue-50 p-2 rounded inline-block dark:text-primary-dark'>
                    <Lightbulb className='w-4 h-4 stroke-blue-500 dark:text-primary-dark' />
                  </span>
                  <div className='flex space-y-2 flex-col ml-3'>
                    <AlertDescription>
                      <p className='text-sm text-slate-800 dark:text-slate-400'>
                        Charges will apply for VIP Stamping. By selecting the
                        VIP Stamping service you agree to the charges, these
                        will be shared in the estimate.
                      </p>
                    </AlertDescription>
                  </div>
                </Alert>
              </>
            )}
          </>
        )}

        {/* Visa Valid Until : Show this field when 'Visa App Type' is 'In-Country' AND 'Visa Type' IS NOT 'Employment Visa Renewal' OR 'Work Permit Renewal'*/}
        {visaApplicationType === 'Yes' &&
          (visaType === '' ||
            (visaType !== 'Employment Visa Renewal' &&
              visaType !== 'Work Permit Renewal')) && (
            <>
              <div className='mb-4'>
                <FormField
                  control={form.control}
                  name='visaValidUntil'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Visa Valid Until</FormLabel>
                      <DateTimePicker
                        granularity='day'
                        value={field.value}
                        onChange={field.onChange}
                        displayFormat={{
                          hour24: 'dd MMMM yyyy',
                        }}
                      />

                      <FormDescription>dd/MM/yyyy</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </>
          )}
      </form>
    </>
  )
}

export default Step1VisaApplication
