import React, { useState } from 'react';
import { useNavigate } from '@tanstack/react-router'
import { useDispatch } from 'react-redux'
import { fetchTrackerStatus } from '@/store/trackerSlice'
import { Loader2, Search } from 'lucide-react';


const PublicTrackerInputSection = () => {
  const [appNumber, setAppNumber] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const dispatch = useDispatch<any>();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    const trimmedAppNumber = appNumber.trim();
    if (!trimmedAppNumber) {
      setError('Application number is required.');
      setLoading(false);
      return;
    }
    try {
      // Dispatch the thunk to fetch and store the status in Redux
      const actionResult = await dispatch(fetchTrackerStatus(trimmedAppNumber));
      if (fetchTrackerStatus.rejected.match(actionResult)) {
        const payload = actionResult.payload;
        setError(typeof payload === 'string' ? payload : 'Application number does not exist.');
        setLoading(false);
        return;
      }
      // Navigate to status page with query param
      navigate({
        to: '/track-your-application/status',
        search: { applicationNumber: trimmedAppNumber }
      });
    } catch (err) {
      setError('Error checking application number. Please try again.');
    }
    setLoading(false);
  };

  return (
    <section id='tracker-application' className='w-full bg-neutral-900'>
        <div className='container w-full grid lg:grid-cols-2 gap-8 text-white py-16'>
          <div className='max-w-lg'>
            <h1 className='uppercase font-tt-firs-neue text-xl'>
              Track Your Application
            </h1>
            <p className='font-wotfard'>
              Access Real-Time Progress and Processing Stages of Your
              Application
            </p>
          </div>

          <form onSubmit={handleSubmit} className='w-full lg:w-10/12 lg:ml-auto'>
            <div className='relative'>
              <input
                id='applicationNumber'
                type='text'
                value={appNumber}
                onChange={(e) => setAppNumber(e.target.value)}
                placeholder='Enter your Application Number to Track'
                required
                className='w-full px-4 py-3 pr-28 rounded-full border-2 border-gray-200 focus:outline-none bg-white/20'
              />
              <button
                type='submit'
                className='absolute top-1/2 right-0 transform -translate-y-1/2 bg-ifza-500 hover:bg-ifza-400 opacity-100 h-[52px] w-[52px] rounded-full flex items-center justify-center'
                disabled={loading}
              >
                {loading ? <Loader2 className='h-4 w-4 animate-spin' /> : <Search className='size-5' /> }
              </button>
            </div>

            <div className="text-red-500 mt-1 text-sm text-center capitalize">
              {error}
            </div>
          </form>
        </div>
    </section>
  )
}

export default PublicTrackerInputSection