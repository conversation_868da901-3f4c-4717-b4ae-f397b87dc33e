import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { FilePenLine, Lightbulb } from 'lucide-react';
import { PlusIcon, TrashIcon } from '@radix-ui/react-icons';
import { UseFormReturn } from 'react-hook-form';
import { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider } from "@/components/ui/tooltip";

interface Row {
  id: string
  option: string
  activityCode: string
  ded: string
  tpa: string
  field4: string
  checked: boolean
}

interface BusinessActivitiesSelectionProps {
  form: UseFormReturn<any>
  rows: Row[]
  handleDelete: (id: string) => void
  handleOptionChange: (id: string, value: string) => void
  handleFieldChange: (id: string, field: string, value: string) => void
  handleCheckedChange: (id: string, checked: boolean) => void
  handleAddRow: () => void
  activitiesData: any[]
  activitiesError: string
}

const Step4LicenseApplication: React.FC<BusinessActivitiesSelectionProps> = ({
  form,
  rows,
  handleDelete,
  handleOptionChange,
  handleFieldChange,
  handleCheckedChange,
  handleAddRow,
  activitiesData,
  activitiesError,
}) => {
  return (
    <Card className='mt-6'>
      <CardContent>
        <Form {...form}>
          <form className='space-y-4 fz-form'>
            <CardHeader className='px-0 pt-0 pb-0'>
              <CardTitle>
                Select Your Business Activities ( Step 4 of 6 ) hsgdshgdhsdf
              </CardTitle>
            </CardHeader>
            <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
              <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
              </span>
              <div className='flex flex-col ml-2'>
                <AlertDescription>
                  <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    You are entitled to select up to 3 Business Activities which
                    are included in the License package (Any additional
                    activities starting from the 4th activity will be charged).
                  </p>
                  <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    Maximum activities per trade license is 7 (seven)
                  </p>
                  <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    If you wish to combine commercial and professional
                    activities, additional charges are applicable and will be
                    shared with you in the quotation.
                  </p>
                </AlertDescription>
              </div>
            </Alert>

            <Alert className='flex items-start border-dashed border-blue-500 mt-2 bg-slate-50 dark:bg-primary/20'>
              <span className='bg-blue-50 p-2 rounded inline-block dark:text-primary-dark'>
                <Lightbulb className='w-4 h-4 stroke-blue-500 dark:text-primary-dark' />
              </span>
              <div className='flex space-y-2 flex-col ml-3'>
                <AlertDescription>
                  <p className='text-sm text-slate-800 dark:text-slate-400'>
                    You may select the activities from the following link :{' '}
                    <a
                      href='https://activities.ifza.com/'
                      target='_blank'
                      rel='noopener noreferrer'
                      className='text-blue-500 underline'
                    >
                      https://activities.ifza.com
                    </a>
                  </p>
                </AlertDescription>
              </div>
            </Alert>

            {/* Activities Table */}
            <p className='text-xl font-semibold mt-4 mb-4'>Activities</p>

            <div className='fz-input-table space-y-4'>
              <Table className='table-fixed w-full'>
                <TableHeader>
                  <TableRow className='text-sm'>
                    <TableHead className='w-[35%] text-center'>
                      Activity Name
                    </TableHead>
                    <TableHead className='w-[35%] text-center'>
                      Activity Code
                    </TableHead>
                    <TableHead className='hidden'>License Type</TableHead>
                    <TableHead className='hidden'>
                      Third Party Approval Required
                    </TableHead>
                    <TableHead className='hidden'>
                      Regulatory Authority
                    </TableHead>
                    <TableHead className='w-[15%] text-center'>
                      Main Activity
                    </TableHead>
                    <TableHead className='w-[15%] text-center'> </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {rows.map((row) => (
                    <TableRow key={row.id} className='align-middle'>
                      {/* Activity Name */}
                      <TableCell className='p-2'>
                        <Select
                          value={row.option}
                          onValueChange={(value) =>
                            handleOptionChange(row.id, value)
                          }
                        >
                          <SelectTrigger className='w-full text-sm'>
                            <SelectValue placeholder='Select Activity Name' />
                          </SelectTrigger>
                          <SelectContent
                            position='popper'
                            side='bottom'
                            sideOffset={5}
                            searchable
                            searchPlaceholder='Search activities...'
                          >
                            {activitiesData.map((activity: any) => (
                              <SelectItem key={activity.ID} value={activity.ID}>
                                {activity.Activity_Name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </TableCell>

                      {/* Activity Code */}
                      <TableCell className='p-2'>
                        <Input
                          className='w-full text-sm'
                          value={row.activityCode}
                          disabled
                          onChange={(e) =>
                            handleFieldChange(
                              row.id,
                              'activityCode',
                              e.target.value
                            )
                          }
                          placeholder='Activity Code'
                        />
                      </TableCell>

                      {/* Hidden Fields */}
                      <TableCell className='hidden'>
                        <Input
                          disabled
                          value={row.ded}
                          onChange={(e) =>
                            handleFieldChange(row.id, 'ded', e.target.value)
                          }
                          placeholder='DED'
                        />
                      </TableCell>
                      <TableCell className='hidden'>
                        <Input
                          className='w-full'
                          disabled
                          value={row.tpa}
                          onChange={(e) =>
                            handleFieldChange(row.id, 'tpa', e.target.value)
                          }
                          placeholder='TPA'
                        />
                      </TableCell>
                      <TableCell className='hidden'>
                        <Input
                          value={row.field4}
                          onChange={(e) =>
                            handleFieldChange(row.id, 'field4', e.target.value)
                          }
                          placeholder='Field 4'
                        />
                      </TableCell>

                      {/* Main Activity Checkbox */}
                      <TableCell className='text-center'>
                        <Checkbox
                          checked={row.checked}
                          onCheckedChange={(checked) =>
                            handleCheckedChange(row.id, checked === true)
                          }
                        />
                      </TableCell>

                      {/* Delete Button */}
                      <TableCell className='text-center'>
                         <TooltipProvider>
    <Tooltip>
      <TooltipTrigger asChild>
                        <Button
                          variant='link'
                          size='icon'
                          className='text-red-600 hover:text-red-800 p-0'
                          onClick={() => handleDelete(row.id)}
                        >
                          <TrashIcon className='w-4 h-4' />
                        </Button>
                      </TooltipTrigger>
      <TooltipContent side='top' sideOffset={4}>
        Delete Activity
      </TooltipContent>
    </Tooltip>
  </TooltipProvider>
</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {activitiesError && (
                <p className='text-sm text-red-500 mt-2'>{activitiesError}</p>
              )}

              {/* Add New Row Button */}
              <Button
                type='button'
                variant='link'
                className='mt-4 underline flex items-center text-sm px-0'
                onClick={handleAddRow}
              >
                <PlusIcon className='w-4 h-4 mr-1' />
                <span>Add New</span>
              </Button>
            </div>

            {/* Alert for Business Activities */}
            <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
              <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
              </span>
              <div className='flex flex-col ml-2'>
                <AlertDescription>
                  Business activities should be from{' '}
                  <strong className='font-bold'>
                    {' '}
                    one license type only.{' '}
                  </strong>{' '}
                  Certain activities are subject to third party approvals.
                </AlertDescription>
              </div>
            </Alert>
            {/* License Type */}
            <FormField
              control={form.control}
              name='licenseType'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    License Type <span className='text-red-500'>*</span>
                  </FormLabel>
                  <FormControl>
                    <div className='pointer-events-none'>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <SelectTrigger className='bg-gray-100 cursor-not-allowed'>
                          <SelectValue />
                        </SelectTrigger>
                        {/* Hide dropdown so user can't select */}
                        <SelectContent className='hidden'>
                          <SelectItem value='Commercial (Includes Trade)'>
                            Commercial (Includes Trade)
                          </SelectItem>
                          <SelectItem value='Professional (Includes Service & Consultancy)'>
                            Professional (Includes Service & Consultancy)
                          </SelectItem>
                          <SelectItem value='Combination'>
                            Combination (Additional Charges may apply)
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {/* Activity Rules : Drop down*/}
            <FormField
              control={form.control}
              name='activityRules'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Activity Rules <span className='text-red-500'>*</span>
                  </FormLabel>
                  <div data-field='activityRules'>
                    <FormControl>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder='Select' />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value='I accept the statement above to abide by the activity rules.'>
                            I accept the statement above to abide by the
                            activity rules.
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Alert className='bg-amber-500/10 alert-bg-warning dark:bg-primary/20 border-none'>
              <Alert className='border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                <AlertDescription>
                  We undertake that we will obtain all approvals, permits,
                  licenses or other permissions from UAE government agencies
                  that may be required at any time for particular products or
                  services, and that we will cease to trade in any particular
                  products or services to the extent that such trading is
                  restricted or prohibited by UAE governmental agencies.
                </AlertDescription>
              </Alert>
            </Alert>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}

export default Step4LicenseApplication
