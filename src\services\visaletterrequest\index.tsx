import { BASE_URL } from "@/utils/network";
import axios from "axios";

function formatDate(date: Date) {
    let year = date.getFullYear();
    let month = String(date.getMonth() + 1).padStart(2, '0'); // Months are zero-indexed
    let day = String(date.getDate()).padStart(2, '0');
    
    return `${year}-${month}-${day}`;
}


// const sameTypes = [
//     "Amendment Letter (Company Name)",
//     "Attested Employment Contract",
//     "DNRD Report",
//     "Dubai Residence Visa Cancellation (Outside UAE) - More than 6 Months",
//     "E-Visa Cancellation",
//     "E-Visa Withdrawal",
//     "E-visa Amendment",
//     "Fujairah Residence Visa Cancellation (Inside UAE)",
//     "Fujairah Residence Visa Cancellation (Outside UAE) - Less than 6 Months",
//     "Fujairah Residence Visa Cancellation (Outside UAE) - More than 6 Months",
//     "Labour Card",
//     "Visa Cancellation (Outside the UAE) and Work Permit Issuance",
//     "Visa Cancellation (within the UAE) and Work Permit Issuance"
// ]

// emp passport number
// Dubai Residence Visa Cancellation (Inside UAE) 
// Dubai Residence Visa Cancellation (Outside UAE) - Less than 6 Months

// cahnge the data of emplyee and Letter should be addressed to
// Employee List


// is the NOC for a Employee or Investor/Partner Visa ? * &&  Is the minimum salary AED 30,000 ? *
// NOC for Golden Visa

// Last date of Exit
// Re-entry (At the airport)
// Re-entry Pass (Outside UAE for more than 6 months)

// Currrent salary & Amendment Salary
// Salary Amendment Letter

// Service_Type && Reason for the Letter => Others => Letter should be addressed to
//Salary Certificate


// Amendment Type
// if Designation  => Current Designation & New Designation
// if Applicant Name => Old Name & New Name
// Visa Amendment Letter

interface ServerData {
    // company details fields
    Trade_License_Number: string;
    Registered_Company_Name: string;
    Manager_Name_as_per_TL? : string;

    // Contact Details fields
    Client_Name: string;
    phone: string;
    Email: string;
    App_Update_WhatsApp_Number: string;

    
    // Paymnet Details fields
    Payment_Method: string;

    // employee details fields
    Salutation: string;
    Emp_First_Name: string;
    Emp_Last_Name: string;
    Emp_Middle_Name: string
    Emp_Full_Name: string;

    Emp_List: Array<any>
    //  Salutation_1: String, 
    //  First_Name_1: String, 
    //  Last_Name_1: String,  
    //  Middle_Name_1: String

    // othrer details fields
    Type: string;
    Does_the_member_hold_an_Investor_Partner_Visa?: string;
    Last_date_of_Exit?: string;
    The_letter_should_be_addressed_to: string
    Service_Type: string
    Letter_Addressed_to: string
    Is_the_minimum_salary_AED_30_000: string
    Current_Salary: string
    Amended_Salary: string
    Reason_for_the_Letter: string
    Amendment_Type: string[]
    Current_Designation: string
    New_Designation: string
    New_Name: string
    Old_Name: string
    Passport_Number: string
    Current_Basic: string | number
    New_Basic: string | number
    Current_Accommodation: string | number
    New_Accommodation: string | number
    Current_Transportation: string | number
    New_Transportation: string | number
    Current_Other_Allowance: string | number
    New_Other_Allowance: string | number
}


export const mainpulateVisaLetterRequestData = (data: any) => {
    
    const formData = new FormData()
    let paymentType = data.paymentType
    if (paymentType == "online") {
        paymentType = "Online Payment"
    }
    if (paymentType == "cash") {
        paymentType = "Cash"
    }
    if (paymentType == "cheque") {
        paymentType = "Cheque"
    }

    // initailize data
    let mainpulatedData: Partial<ServerData> = {
        // company details fields
        Type: data.letterDocumentsRequired,
        Trade_License_Number: data.tradeLicenseNumber!,
        Registered_Company_Name: data.registeredCompanyName!,
        // contact details fields
        Client_Name: data.yourName!,
        phone: data.phoneNumber!,
        Email: data.yourEmailAddress!,
        App_Update_WhatsApp_Number: data.whatsAppNumber!,
        // payment details fields
        Payment_Method: paymentType,
    }

    if(mainpulatedData.Type != "Employee List") {
        mainpulatedData = {
            ...mainpulatedData,
            Salutation: data.title!,
            Emp_First_Name: data.firstName!,
            Emp_Last_Name: data.lastName!,
            Emp_Middle_Name: data.middleName!,
            Emp_Full_Name: `${data.firstName} ${data.middleName} ${data.lastName}`
        }
    }

    if (mainpulatedData.Type === "Employee List") {
        mainpulatedData = {
            ...mainpulatedData,
            Emp_List: [
                { Salutation: data.title, First_Name: data.firstName, Last_Name: data.lastName, Middle_Name: data.middleName },
                { Salutation: data.title2, First_Name: data.firstName2, Last_Name: data.lastName2, Middle_Name: data.middleName2 },
                { Salutation: data.title3, First_Name: data.firstName3, Last_Name: data.lastName3, Middle_Name: data.middleName3 },
                { Salutation: data.title4, First_Name: data.firstName4, Last_Name: data.lastName4, Middle_Name: data.middleName4 },
                { Salutation: data.title5, First_Name: data.firstName5, Last_Name: data.lastName5, Middle_Name: data.middleName5 },
                { Salutation: data.title6, First_Name: data.firstName6, Last_Name: data.lastName6, Middle_Name: data.middleName6 },
                { Salutation: data.title7, First_Name: data.firstName7, Last_Name: data.lastName7, Middle_Name: data.middleName7 },
                { Salutation: data.title8, First_Name: data.firstName8, Last_Name: data.lastName8, Middle_Name: data.middleName8 },
                { Salutation: data.title9, First_Name: data.firstName9, Last_Name: data.lastName9, Middle_Name: data.middleName9 },
                { Salutation: data.title10, First_Name: data.firstName10, Last_Name: data.lastName10, Middle_Name: data.middleName10 },
            ],
            Letter_Addressed_to: data.letterShouldBeAddressedTo!
        }
    }

    if(mainpulatedData.Type === "NOC for Golden Visa") {
        mainpulatedData = {
            ...mainpulatedData,
            Does_the_member_hold_an_Investor_Partner_Visa: data.isNOCForEmployeeOrInvestor!,
            Is_the_minimum_salary_AED_30_000: data.isMinimumSalaryAED30K!
        }
    }
    if(mainpulatedData.Type === "Re-entry (At the airport)" || mainpulatedData.Type === "Re-entry Pass (Outside UAE for more than 6 months)") {
    //   console.log("datamgffutgyugyu",  new Date(data.lastDateOfExit.toString()).toLocaleDateString())
        mainpulatedData = {
            ...mainpulatedData,
            Last_date_of_Exit: formatDate(new Date(data.lastDateOfExit!.toString())),
        }
    }
    if(mainpulatedData.Type === "Salary Amendment Letter") {
        mainpulatedData = {
            ...mainpulatedData,
            Current_Salary: data.currentSalary!,
            Amended_Salary: data.amendedSalary!,
            Current_Basic: data.currentBasic,
            Current_Accommodation: data.currentAccommodation,
            Current_Transportation: data.currentTransportation,
            Current_Other_Allowance: data.currentOtherAllowance,
            New_Basic: data.newBasic,
            New_Accommodation: data.newAccommodation,
            New_Transportation: data.newTransportation,
            New_Other_Allowance: data.newOtherAllowance,
            // New_Basic: string | number
            // Current_Accommodation: string | number
            // New_Accommodation: string | number
            // Current_Transportation: string | number
            // New_Transportation: string | number
            // Current_Other_Allowance: string | number
            // New_Other_Allowance: string | number
        }
    }

    if(mainpulatedData.Type === "Salary Certificate") {
        mainpulatedData = {
            ...mainpulatedData,
            Service_Type: data.serviceType!,
            Reason_for_the_Letter: data.reasonForLetter!,
            Manager_Name_as_per_TL: data.managerName!,
        }
        if(data.reasonForLetter === "Others") {
            mainpulatedData = {
                ...mainpulatedData,
                Letter_Addressed_to: data.letterAddressedTo!
            }
        }
    }

    if(mainpulatedData.Type === "Visa Amendment Letter") {
        // amendmentType

        mainpulatedData = {
            ...mainpulatedData,
            Amendment_Type: data.amendmentType.split(",")!
        }


        if (mainpulatedData.Amendment_Type?.includes("Designation (On Residence Visa)")) {

            mainpulatedData = {
                ...mainpulatedData,
                Current_Designation: data.currentDesignation!,
                New_Designation: data.newDesignation!
            }
        }

        if (mainpulatedData.Amendment_Type?.includes("Applicant Name")) {
            mainpulatedData = {
                ...mainpulatedData,
                Old_Name: data.oldName!,
                New_Name: data.newName!
            }
        }
    }


    const Dubai_Residence_Visa_Cancellation = ["Dubai Residence Visa Cancellation (Inside UAE)", "Dubai Residence Visa Cancellation (Outside UAE) - Less than 6 Months", "Dubai Residence Visa Cancellation (Outside UAE) - More than 6 Months"]
    
    if(Dubai_Residence_Visa_Cancellation.includes(mainpulatedData.Type!)) {
            mainpulatedData = {
                ...mainpulatedData,
                Passport_Number: data.passportNum!,
            }
    }
  

    Object.keys(mainpulatedData).forEach((key) => {
        if(key == "Emp_List") {
            formData.append(key, JSON.stringify(mainpulatedData[key as keyof ServerData]))
        } else if(key === "Amendment_Type") {
            formData.append("Amendment_Type", JSON.stringify(mainpulatedData[key as keyof ServerData]))
        } else {
            formData.append(key, mainpulatedData[key as keyof ServerData] as string)
        }
    })


    // files 
    if(data.paymentProof) {
        formData.append("Payment Proof", data.paymentProof)
    }
    if(mainpulatedData.Type === "Salary Amendment Letter") {
        formData.append("Salary Amendment Bank Statement", data.bankStatement)
        formData.append("Salary Amendment Letter", data.salaryAmendmentLetter)
    }
    if(mainpulatedData.Type === "Visa Amendment Letter") {
        
        if(data.amendmentOfficialDocument) {
            formData.append("Visa Amendment Letter Religion Amendment Official Document", data.amendmentOfficialDocument)
        }

        if(data.attestedMofaDegree) {
            formData.append("Visa Amendment Letter Attested MOFA degree", data.attestedMofaDegree)
        }
        if(data.newPassportCo) {
            formData.append("Visa Amendment Letter Employee New Passport Copy", data.newPassportCo)

        }
        if(data.oldPassportCo) {
            formData.append("Visa Amendment Letter Employee Old Passport Copy", data.oldPassportCo)
        }
        if(data.newPassportCopy) {
            formData.append("Visa Amendment Letter New Passport Copy", data.newPassportCopy)
        }
        if(data.oldPassportCopy) {
            formData.append("Visa Amendment Letter Old Passport Copy", data.oldPassportCopy)
        }
        if(data.updatedPassportSizedPicture){
            formData.append("Visa Amendment Letter Updated Passport Sized Picture", data.updatedPassportSizedPicture)
        }


    }

    if(mainpulatedData.Type === "Labour Card") {

        if(data.residenceVisaCopy) {
            formData.append("Labour Card Letter Residence Visa Copy", data.residenceVisaCopy)
        }
    }
   
    const DubaiResidenceVisaCancellation = ["Dubai Residence Visa Cancellation (Inside UAE)", "Dubai Residence Visa Cancellation (Outside UAE) - Less than 6 Months", "Dubai Residence Visa Cancellation (Outside UAE) - More than 6 Months"]

    if(DubaiResidenceVisaCancellation.includes(mainpulatedData.Type!)) {
        if(data.uploadRv) {
            formData.append("Dubai Residence Visa Cancellation Residence Visa", data.uploadRv)
        }
        if(data.passportCo) {
            formData.append("Dubai Residence Visa Cancellation Passport Copy", data.passportCo)
        }
        if(data.goldenVisa) {
            formData.append("Dubai Residence Visa Cancellation Email Confirmation For Golden Visa", data.goldenVisa)
        }
        if(data.requiredDocuments) {
            formData.append("Dubai Residence Visa Cancellation Upload The Documents", data.requiredDocuments)
        }
    }

    const FujairahResidenceVisaCancellation = ["Fujairah Residence Visa Cancellation (Inside UAE)", "Fujairah Residence Visa Cancellation (Outside UAE) - Less than 6 Months", "Fujairah Residence Visa Cancellation (Outside UAE) - More than 6 Months"]
    if(FujairahResidenceVisaCancellation.includes(mainpulatedData.Type!)) {
        if(data.uploadRv) {
            formData.append("Fujairah Residence Visa Cancellation Residence Visa", data.uploadRv)
        }
        if(data.passportCo) {
            formData.append("Fujairah Residence Visa Cancellation Passport Copy", data.passportCo)
        }
        if(data.requiredDocuments) {
            formData.append("Fujairah Residence Visa Cancellation Upload The Documents", data.requiredDocuments)
        }
    }

    if(mainpulatedData.Type === "Re-entry (At the airport)") {
        if(data.passportCopy) {
            formData.append("Re-entry At the airport Passport Copy", data.passportCopy)
        }
        if(data.residenceVisa) {
            formData.append("Re-entry At the airport Residence Visa", data.residenceVisa)
        }
        if(data.signedLetter) {
            formData.append("Re-entry At the airport Signed Letter", data.signedLetter)
        }
    }

    if(mainpulatedData.Type === "Re-entry Pass (Outside UAE for more than 6 months)") {
    
        if(data.passportCo) {
            formData.append("Re-entry Pass Passport Copy", data.passportCo)
        }
        if(data.uploadRv) {
            formData.append("Re-entry Pass Residence Visa", data.uploadRv)
        }
        if(data.requiredDocuments) {
            formData.append("Re-entry Pass Please Upload The Documents", data.requiredDocuments)
        }
    }

    if(mainpulatedData.Type === "Work Permit Cancellation") {
        if(data.requiredDocuments) {
            formData.append("Work Permit Cancellation Upload The Documents", data.requiredDocuments)
        }
    }

    if(mainpulatedData.Type === "NOC for Golden Visa") {
        if(data.auditedFinancialReport) {
            formData.append("NOC for Golden Visa Audited financial report of the company", data.auditedFinancialReport)
        }
    }
  
    if(mainpulatedData.Type === "Salary Certificate") {
        if(data.uploadRv) {
            formData.append("Salart Certificate Residence Visa", data.uploadRv)
        }
    }
    return formData
}


export function createVisaLetterRequest(bodyFromData: any, onSucess: (res: any) => void, onError: (err: any) => void) {
    const enpointUrl = `${BASE_URL}api/visaRequests/create`
    axios({
        method: "POST",
        url: enpointUrl,
        data: bodyFromData,
        headers: { "Content-Type": "multipart/form-data" }
    }).then((res) => {
        onSucess(res)
    }).catch((err) => {
        onError(err)
    })
}