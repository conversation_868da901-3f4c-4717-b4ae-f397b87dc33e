PortalWeb is a React + TypeScript project that serves as the web front‑end for the “Customers Hub” portal. The application is built with Vite and heavily relies on TanStack Router for page navigation, React Query for data fetching, and TailwindCSS for UI styling. The project uses ShadcnUI components, provides light/dark theming through a context provider, and contains numerous feature modules for user authentication, license applications, visa letters, renewals, and more.

Key details:

Tech Stack: ShadcnUI + TailwindCSS, Vite, TanStack Router, TypeScript, ESLint/Prettier, Lucide Icons

Entry Point: The main application logic resides in src/main.tsx, where the router is initialized and the ThemeProvider wraps the app components

Customer Hub Branding: The index.html defines metadata for “Customers Hub” by IFZA and mounts the React app to the page root

API Services: Service helpers (e.g., createLicenseApplication and related functions) in src/services/licenseapplication manage form submissions to the backend API

Overall, the repository is structured around multiple feature folders under src/features that implement forms and flows for different aspects of the customer portal—such as license applications, visa applications, and letter requests—providing a streamlined interface for users to submit information and manage their interactions with IFZA’s backend services.

Go to the project directory

  cd PortalWeb
Install dependencies

  npm install
Start the server

  npm run dev

License
 🚫 **Notice**: This repository is licensed for use by IFZA employees and contractors only. Unauthorized use is prohibited.

